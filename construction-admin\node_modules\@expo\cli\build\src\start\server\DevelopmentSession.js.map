{"version": 3, "sources": ["../../../../src/start/server/DevelopmentSession.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\n\nimport {\n  closeDevelopmentSessionAsync,\n  updateDevelopmentSessionAsync,\n} from '../../api/updateDevelopmentSession';\nimport { getUserAsync } from '../../api/user/user';\nimport { env } from '../../utils/env';\nimport * as ProjectDevices from '../project/devices';\n\nconst debug = require('debug')('expo:start:server:developmentSession') as typeof console.log;\n\nconst UPDATE_FREQUENCY = 20 * 1000; // 20 seconds\n\nasync function isAuthenticatedAsync(): Promise<boolean> {\n  return !!(await getUserAsync().catch(() => null));\n}\n\nexport class DevelopmentSession {\n  protected timeout: NodeJS.Timeout | null = null;\n\n  constructor(\n    /** Project root directory. */\n    private projectRoot: string,\n    /** Development Server URL. */\n    public url: string | null,\n    /** Catch any errors that may occur during the `startAsync` method. */\n    private onError: (error: Error) => void\n  ) {}\n\n  /**\n   * Notify the Expo servers that a project is running, this enables the Expo Go app\n   * and Dev Clients to offer a \"recently in development\" section for quick access.\n   *\n   * This method starts an interval that will continue to ping the servers until we stop it.\n   *\n   * @param projectRoot Project root folder, used for retrieving device installation IDs.\n   * @param props.exp Partial Expo config with values that will be used in the Expo Go app.\n   * @param props.runtime which runtime the app should be opened in. `native` for dev clients, `web` for web browsers.\n   */\n  public async startAsync({\n    exp = getConfig(this.projectRoot).exp,\n    runtime,\n  }: {\n    exp?: Pick<ExpoConfig, 'name' | 'description' | 'slug' | 'primaryColor'>;\n    runtime: 'native' | 'web';\n  }): Promise<void> {\n    try {\n      if (env.EXPO_OFFLINE) {\n        debug(\n          'This project will not be suggested in Expo Go or Dev Clients because Expo CLI is running in offline-mode.'\n        );\n        this.stopNotifying();\n        return;\n      }\n\n      const deviceIds = await this.getDeviceInstallationIdsAsync();\n\n      if (!(await isAuthenticatedAsync()) && !deviceIds?.length) {\n        debug(\n          'Development session will not ping because the user is not authenticated and there are no devices.'\n        );\n        this.stopNotifying();\n        return;\n      }\n\n      if (this.url) {\n        debug(`Development session ping (runtime: ${runtime}, url: ${this.url})`);\n\n        await updateDevelopmentSessionAsync({\n          url: this.url,\n          runtime,\n          exp,\n          deviceIds,\n        });\n      }\n\n      this.stopNotifying();\n\n      this.timeout = setTimeout(() => this.startAsync({ exp, runtime }), UPDATE_FREQUENCY);\n    } catch (error: any) {\n      debug(`Error updating development session API: ${error}`);\n      this.stopNotifying();\n      this.onError(error);\n    }\n  }\n\n  /** Get all recent devices for the project. */\n  private async getDeviceInstallationIdsAsync(): Promise<string[]> {\n    const { devices } = await ProjectDevices.getDevicesInfoAsync(this.projectRoot);\n    return devices.map(({ installationId }) => installationId);\n  }\n\n  /** Stop notifying the Expo servers that the development session is running. */\n  public stopNotifying() {\n    if (this.timeout) {\n      clearTimeout(this.timeout);\n    }\n    this.timeout = null;\n  }\n\n  public async closeAsync(): Promise<void> {\n    this.stopNotifying();\n\n    const deviceIds = await this.getDeviceInstallationIdsAsync();\n\n    if (!(await isAuthenticatedAsync()) && !deviceIds?.length) {\n      return;\n    }\n\n    if (this.url) {\n      await closeDevelopmentSessionAsync({\n        url: this.url,\n        deviceIds,\n      });\n    }\n  }\n}\n"], "names": ["ProjectDevices", "debug", "require", "UPDATE_FREQUENCY", "isAuthenticatedAsync", "getUserAsync", "catch", "DevelopmentSession", "constructor", "projectRoot", "url", "onError", "timeout", "startAsync", "exp", "getConfig", "runtime", "env", "EXPO_OFFLINE", "stopNotifying", "deviceIds", "getDeviceInstallationIdsAsync", "length", "updateDevelopmentSessionAsync", "setTimeout", "error", "devices", "getDevicesInfoAsync", "map", "installationId", "clearTimeout", "closeAsync", "closeDevelopmentSessionAsync"], "mappings": "AAAA;;;;AAAsC,IAAA,OAAc,WAAd,cAAc,CAAA;AAK7C,IAAA,yBAAoC,WAApC,oCAAoC,CAAA;AACd,IAAA,KAAqB,WAArB,qBAAqB,CAAA;AAC9B,IAAA,IAAiB,WAAjB,iBAAiB,CAAA;AACzBA,IAAAA,cAAc,mCAAM,oBAAoB,EAA1B;;;;;;;;;;;;;;;;;;;;;;AAE1B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,sCAAsC,CAAC,AAAsB,AAAC;AAE7F,MAAMC,gBAAgB,GAAG,EAAE,GAAG,IAAI,AAAC,EAAC,aAAa;AAEjD,eAAeC,oBAAoB,GAAqB;IACtD,OAAO,CAAC,CAAE,MAAMC,CAAAA,GAAAA,KAAY,AAAE,CAAA,aAAF,EAAE,CAACC,KAAK,CAAC,IAAM,IAAI;IAAA,CAAC,AAAC,CAAC;CACnD;AAEM,MAAMC,kBAAkB;IAG7BC,YAEUC,WAAmB,EAEpBC,GAAkB,EAEjBC,OAA+B,CACvC;aALQF,WAAmB,GAAnBA,WAAmB;aAEpBC,GAAkB,GAAlBA,GAAkB;aAEjBC,OAA+B,GAA/BA,OAA+B;aAR/BC,OAAO,GAA0B,IAAI;KAS3C;IAEJ;;;;;;;;;KASG,CACH,MAAaC,UAAU,CAAC,EACtBC,GAAG,EAAGC,CAAAA,GAAAA,OAAS,AAAkB,CAAA,UAAlB,CAAC,IAAI,CAACN,WAAW,CAAC,CAACK,GAAG,CAAA,EACrCE,OAAO,CAAA,EAIR,EAAiB;QAChB,IAAI;YACF,IAAIC,IAAG,IAAA,CAACC,YAAY,EAAE;gBACpBjB,KAAK,CACH,2GAA2G,CAC5G,CAAC;gBACF,IAAI,CAACkB,aAAa,EAAE,CAAC;gBACrB,OAAO;aACR;YAED,MAAMC,SAAS,GAAG,MAAM,IAAI,CAACC,6BAA6B,EAAE,AAAC;YAE7D,IAAI,CAAE,MAAMjB,oBAAoB,EAAE,AAAC,IAAI,CAACgB,CAAAA,SAAS,QAAQ,GAAjBA,KAAAA,CAAiB,GAAjBA,SAAS,CAAEE,MAAM,CAAA,EAAE;gBACzDrB,KAAK,CACH,mGAAmG,CACpG,CAAC;gBACF,IAAI,CAACkB,aAAa,EAAE,CAAC;gBACrB,OAAO;aACR;YAED,IAAI,IAAI,CAACT,GAAG,EAAE;gBACZT,KAAK,CAAC,CAAC,mCAAmC,EAAEe,OAAO,CAAC,OAAO,EAAE,IAAI,CAACN,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE1E,MAAMa,CAAAA,GAAAA,yBAA6B,AAKjC,CAAA,8BALiC,CAAC;oBAClCb,GAAG,EAAE,IAAI,CAACA,GAAG;oBACbM,OAAO;oBACPF,GAAG;oBACHM,SAAS;iBACV,CAAC,CAAC;aACJ;YAED,IAAI,CAACD,aAAa,EAAE,CAAC;YAErB,IAAI,CAACP,OAAO,GAAGY,UAAU,CAAC,IAAM,IAAI,CAACX,UAAU,CAAC;oBAAEC,GAAG;oBAAEE,OAAO;iBAAE,CAAC;YAAA,EAAEb,gBAAgB,CAAC,CAAC;SACtF,CAAC,OAAOsB,KAAK,EAAO;YACnBxB,KAAK,CAAC,CAAC,wCAAwC,EAAEwB,KAAK,CAAC,CAAC,CAAC,CAAC;YAC1D,IAAI,CAACN,aAAa,EAAE,CAAC;YACrB,IAAI,CAACR,OAAO,CAACc,KAAK,CAAC,CAAC;SACrB;KACF;IAED,8CAA8C,CAC9C,MAAcJ,6BAA6B,GAAsB;QAC/D,MAAM,EAAEK,OAAO,CAAA,EAAE,GAAG,MAAM1B,cAAc,CAAC2B,mBAAmB,CAAC,IAAI,CAAClB,WAAW,CAAC,AAAC;QAC/E,OAAOiB,OAAO,CAACE,GAAG,CAAC,CAAC,EAAEC,cAAc,CAAA,EAAE,GAAKA,cAAc;QAAA,CAAC,CAAC;KAC5D;IAED,+EAA+E,CAC/E,AAAOV,aAAa,GAAG;QACrB,IAAI,IAAI,CAACP,OAAO,EAAE;YAChBkB,YAAY,CAAC,IAAI,CAAClB,OAAO,CAAC,CAAC;SAC5B;QACD,IAAI,CAACA,OAAO,GAAG,IAAI,CAAC;KACrB;IAED,MAAamB,UAAU,GAAkB;QACvC,IAAI,CAACZ,aAAa,EAAE,CAAC;QAErB,MAAMC,SAAS,GAAG,MAAM,IAAI,CAACC,6BAA6B,EAAE,AAAC;QAE7D,IAAI,CAAE,MAAMjB,oBAAoB,EAAE,AAAC,IAAI,CAACgB,CAAAA,SAAS,QAAQ,GAAjBA,KAAAA,CAAiB,GAAjBA,SAAS,CAAEE,MAAM,CAAA,EAAE;YACzD,OAAO;SACR;QAED,IAAI,IAAI,CAACZ,GAAG,EAAE;YACZ,MAAMsB,CAAAA,GAAAA,yBAA4B,AAGhC,CAAA,6BAHgC,CAAC;gBACjCtB,GAAG,EAAE,IAAI,CAACA,GAAG;gBACbU,SAAS;aACV,CAAC,CAAC;SACJ;KACF;CACF;QAnGYb,kBAAkB,GAAlBA,kBAAkB"}