{"version": 3, "sources": ["../../../src/utils/env.ts"], "sourcesContent": ["import { boolish, int, string } from 'getenv';\n\n// @expo/webpack-config -> expo-pwa -> @expo/image-utils: EXPO_IMAGE_UTILS_NO_SHARP\n\n// TODO: EXPO_CLI_USERNAME, EXPO_CLI_PASSWORD\n\nclass Env {\n  /** Enable profiling metrics */\n  get EXPO_PROFILE() {\n    return boolish('EXPO_PROFILE', false);\n  }\n\n  /** Enable debug logging */\n  get EXPO_DEBUG() {\n    return boolish('EXPO_DEBUG', false);\n  }\n\n  /** Disable all network requests */\n  get EXPO_OFFLINE() {\n    return boolish('EXPO_OFFLINE', false);\n  }\n\n  /** Enable the beta version of Expo (TODO: Should this just be in the beta version of expo releases?) */\n  get EXPO_BETA() {\n    return boolish('EXPO_BETA', false);\n  }\n\n  /** Enable staging API environment */\n  get EXPO_STAGING() {\n    return boolish('EXPO_STAGING', false);\n  }\n\n  /** Enable local API environment */\n  get EXPO_LOCAL() {\n    return boolish('EXPO_LOCAL', false);\n  }\n\n  /** Is running in non-interactive CI mode */\n  get CI() {\n    return boolish('CI', false);\n  }\n\n  /** Disable telemetry (analytics) */\n  get EXPO_NO_TELEMETRY() {\n    return boolish('EXPO_NO_TELEMETRY', false);\n  }\n\n  /** local directory to the universe repo for testing locally */\n  get EXPO_UNIVERSE_DIR() {\n    return string('EXPO_UNIVERSE_DIR', '');\n  }\n\n  /** @deprecated Default Webpack host string */\n  get WEB_HOST() {\n    return string('WEB_HOST', '0.0.0.0');\n  }\n\n  /** Skip warning users about a dirty git status */\n  get EXPO_NO_GIT_STATUS() {\n    return boolish('EXPO_NO_GIT_STATUS', false);\n  }\n  /** Disable auto web setup */\n  get EXPO_NO_WEB_SETUP() {\n    return boolish('EXPO_NO_WEB_SETUP', false);\n  }\n  /** Disable auto TypeScript setup */\n  get EXPO_NO_TYPESCRIPT_SETUP() {\n    return boolish('EXPO_NO_TYPESCRIPT_SETUP', false);\n  }\n  /** Disable all API caches. Does not disable bundler caches. */\n  get EXPO_NO_CACHE() {\n    return boolish('EXPO_NO_CACHE', false);\n  }\n  /** Disable the app select redirect page. */\n  get EXPO_NO_REDIRECT_PAGE() {\n    return boolish('EXPO_NO_REDIRECT_PAGE', false);\n  }\n  /** The React Metro port that's baked into react-native scripts and tools. */\n  get RCT_METRO_PORT() {\n    return int('RCT_METRO_PORT', 0);\n  }\n  /** Skip validating the manifest during `export`. */\n  get EXPO_SKIP_MANIFEST_VALIDATION_TOKEN(): boolean {\n    return !!string('EXPO_SKIP_MANIFEST_VALIDATION_TOKEN', '');\n  }\n\n  /** Public folder path relative to the project root. Default to `public` */\n  get EXPO_PUBLIC_FOLDER(): string {\n    return string('EXPO_PUBLIC_FOLDER', 'public');\n  }\n\n  /** Higher priority `$EDIOTR` variable for indicating which editor to use when pressing `o` in the Terminal UI. */\n  get EXPO_EDITOR(): string {\n    return string('EXPO_EDITOR', '');\n  }\n\n  /** Enable auto server root detection for Metro. This will change the server root to the workspace root. */\n  get EXPO_USE_METRO_WORKSPACE_ROOT(): boolean {\n    return boolish('EXPO_USE_METRO_WORKSPACE_ROOT', false);\n  }\n\n  /**\n   * Overwrite the dev server URL, disregarding the `--port`, `--host`, `--tunnel`, `--lan`, `--localhost` arguments.\n   * This is useful for browser editors that require custom proxy URLs.\n   */\n  get EXPO_PACKAGER_PROXY_URL(): string {\n    return string('EXPO_PACKAGER_PROXY_URL', '');\n  }\n\n  /**\n   * **Experimental** - Disable using `exp.direct` as the hostname for\n   * `--tunnel` connections. This enables **https://** forwarding which\n   * can be used to test universal links on iOS.\n   *\n   * This may cause issues with `expo-linking` and Expo Go.\n   *\n   * Select the exact subdomain by passing a string value that is not one of: `true`, `false`, `1`, `0`.\n   */\n  get EXPO_TUNNEL_SUBDOMAIN(): string | boolean {\n    const subdomain = string('EXPO_TUNNEL_SUBDOMAIN', '');\n    if (['0', 'false', ''].includes(subdomain)) {\n      return false;\n    } else if (['1', 'true'].includes(subdomain)) {\n      return true;\n    }\n    return subdomain;\n  }\n\n  /**\n   * Force Expo CLI to use the [`resolver.resolverMainFields`](https://facebook.github.io/metro/docs/configuration/#resolvermainfields) from the project `metro.config.js` for all platforms.\n   *\n   * By default, Expo CLI will use `['browser', 'module', 'main']` (default for Webpack) for web and the user-defined main fields for other platforms.\n   */\n  get EXPO_METRO_NO_MAIN_FIELD_OVERRIDE(): boolean {\n    return boolish('EXPO_METRO_NO_MAIN_FIELD_OVERRIDE', false);\n  }\n\n  /**\n   * HTTP/HTTPS proxy to connect to for network requests. Configures [https-proxy-agent](https://www.npmjs.com/package/https-proxy-agent).\n   */\n  get HTTP_PROXY(): string {\n    return process.env.HTTP_PROXY || process.env.http_proxy || '';\n  }\n\n  /**\n   * Use the network inspector by overriding the metro inspector proxy with a custom version.\n   * @deprecated This has been replaced by `@react-native/dev-middleware` and is now unused.\n   */\n  get EXPO_NO_INSPECTOR_PROXY(): boolean {\n    return boolish('EXPO_NO_INSPECTOR_PROXY', false);\n  }\n\n  /** Disable lazy bundling in Metro bundler. */\n  get EXPO_NO_METRO_LAZY() {\n    return boolish('EXPO_NO_METRO_LAZY', false);\n  }\n\n  /** Enable the unstable inverse dependency stack trace for Metro bundling errors. */\n  get EXPO_METRO_UNSTABLE_ERRORS() {\n    return boolish('EXPO_METRO_UNSTABLE_ERRORS', false);\n  }\n\n  /** Enable the unstable fast resolver for Metro. */\n  get EXPO_USE_FAST_RESOLVER() {\n    return boolish('EXPO_USE_FAST_RESOLVER', false);\n  }\n\n  /** Disable Environment Variable injection in client bundles. */\n  get EXPO_NO_CLIENT_ENV_VARS(): boolean {\n    return boolish('EXPO_NO_CLIENT_ENV_VARS', false);\n  }\n\n  /** Enable the React Native JS Inspector, instead of the \"classic\" Chrome DevTools (SDK <=49) */\n  get EXPO_USE_UNSTABLE_DEBUGGER(): boolean {\n    return boolish('EXPO_USE_UNSTABLE_DEBUGGER', false);\n  }\n\n  /** Set the default `user` that should be passed to `--user` with ADB commands. Used for installing APKs on Android devices with multiple profiles. Defaults to `0`. */\n  get EXPO_ADB_USER(): string {\n    return string('EXPO_ADB_USER', '0');\n  }\n}\n\nexport const env = new Env();\n"], "names": ["Env", "EXPO_PROFILE", "boolish", "EXPO_DEBUG", "EXPO_OFFLINE", "EXPO_BETA", "EXPO_STAGING", "EXPO_LOCAL", "CI", "EXPO_NO_TELEMETRY", "EXPO_UNIVERSE_DIR", "string", "WEB_HOST", "EXPO_NO_GIT_STATUS", "EXPO_NO_WEB_SETUP", "EXPO_NO_TYPESCRIPT_SETUP", "EXPO_NO_CACHE", "EXPO_NO_REDIRECT_PAGE", "RCT_METRO_PORT", "int", "EXPO_SKIP_MANIFEST_VALIDATION_TOKEN", "EXPO_PUBLIC_FOLDER", "EXPO_EDITOR", "EXPO_USE_METRO_WORKSPACE_ROOT", "EXPO_PACKAGER_PROXY_URL", "EXPO_TUNNEL_SUBDOMAIN", "subdomain", "includes", "EXPO_METRO_NO_MAIN_FIELD_OVERRIDE", "HTTP_PROXY", "process", "env", "http_proxy", "EXPO_NO_INSPECTOR_PROXY", "EXPO_NO_METRO_LAZY", "EXPO_METRO_UNSTABLE_ERRORS", "EXPO_USE_FAST_RESOLVER", "EXPO_NO_CLIENT_ENV_VARS", "EXPO_USE_UNSTABLE_DEBUGGER", "EXPO_ADB_USER"], "mappings": "AAAA;;;;;AAAqC,IAAA,OAAQ,WAAR,QAAQ,CAAA;AAE7C,mFAAmF;AAEnF,6CAA6C;AAE7C,MAAMA,GAAG;IACP,+BAA+B,CAC/B,IAAIC,YAAY,GAAG;QACjB,OAAOC,CAAAA,GAAAA,OAAO,AAAuB,CAAA,QAAvB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;KACvC;IAED,2BAA2B,CAC3B,IAAIC,UAAU,GAAG;QACf,OAAOD,CAAAA,GAAAA,OAAO,AAAqB,CAAA,QAArB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;KACrC;IAED,mCAAmC,CACnC,IAAIE,YAAY,GAAG;QACjB,OAAOF,CAAAA,GAAAA,OAAO,AAAuB,CAAA,QAAvB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;KACvC;IAED,wGAAwG,CACxG,IAAIG,SAAS,GAAG;QACd,OAAOH,CAAAA,GAAAA,OAAO,AAAoB,CAAA,QAApB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;KACpC;IAED,qCAAqC,CACrC,IAAII,YAAY,GAAG;QACjB,OAAOJ,CAAAA,GAAAA,OAAO,AAAuB,CAAA,QAAvB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;KACvC;IAED,mCAAmC,CACnC,IAAIK,UAAU,GAAG;QACf,OAAOL,CAAAA,GAAAA,OAAO,AAAqB,CAAA,QAArB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;KACrC;IAED,4CAA4C,CAC5C,IAAIM,EAAE,GAAG;QACP,OAAON,CAAAA,GAAAA,OAAO,AAAa,CAAA,QAAb,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KAC7B;IAED,oCAAoC,CACpC,IAAIO,iBAAiB,GAAG;QACtB,OAAOP,CAAAA,GAAAA,OAAO,AAA4B,CAAA,QAA5B,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;KAC5C;IAED,+DAA+D,CAC/D,IAAIQ,iBAAiB,GAAG;QACtB,OAAOC,CAAAA,GAAAA,OAAM,AAAyB,CAAA,OAAzB,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;KACxC;IAED,8CAA8C,CAC9C,IAAIC,QAAQ,GAAG;QACb,OAAOD,CAAAA,GAAAA,OAAM,AAAuB,CAAA,OAAvB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;KACtC;IAED,kDAAkD,CAClD,IAAIE,kBAAkB,GAAG;QACvB,OAAOX,CAAAA,GAAAA,OAAO,AAA6B,CAAA,QAA7B,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;KAC7C;IACD,6BAA6B,CAC7B,IAAIY,iBAAiB,GAAG;QACtB,OAAOZ,CAAAA,GAAAA,OAAO,AAA4B,CAAA,QAA5B,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;KAC5C;IACD,oCAAoC,CACpC,IAAIa,wBAAwB,GAAG;QAC7B,OAAOb,CAAAA,GAAAA,OAAO,AAAmC,CAAA,QAAnC,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;KACnD;IACD,+DAA+D,CAC/D,IAAIc,aAAa,GAAG;QAClB,OAAOd,CAAAA,GAAAA,OAAO,AAAwB,CAAA,QAAxB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;KACxC;IACD,4CAA4C,CAC5C,IAAIe,qBAAqB,GAAG;QAC1B,OAAOf,CAAAA,GAAAA,OAAO,AAAgC,CAAA,QAAhC,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;KAChD;IACD,6EAA6E,CAC7E,IAAIgB,cAAc,GAAG;QACnB,OAAOC,CAAAA,GAAAA,OAAG,AAAqB,CAAA,IAArB,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;KACjC;IACD,oDAAoD,CACpD,IAAIC,mCAAmC,GAAY;QACjD,OAAO,CAAC,CAACT,CAAAA,GAAAA,OAAM,AAA2C,CAAA,OAA3C,CAAC,qCAAqC,EAAE,EAAE,CAAC,CAAC;KAC5D;IAED,2EAA2E,CAC3E,IAAIU,kBAAkB,GAAW;QAC/B,OAAOV,CAAAA,GAAAA,OAAM,AAAgC,CAAA,OAAhC,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;KAC/C;IAED,kHAAkH,CAClH,IAAIW,WAAW,GAAW;QACxB,OAAOX,CAAAA,GAAAA,OAAM,AAAmB,CAAA,OAAnB,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;KAClC;IAED,2GAA2G,CAC3G,IAAIY,6BAA6B,GAAY;QAC3C,OAAOrB,CAAAA,GAAAA,OAAO,AAAwC,CAAA,QAAxC,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;KACxD;IAED;;;KAGG,CACH,IAAIsB,uBAAuB,GAAW;QACpC,OAAOb,CAAAA,GAAAA,OAAM,AAA+B,CAAA,OAA/B,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;KAC9C;IAED;;;;;;;;KAQG,CACH,IAAIc,qBAAqB,GAAqB;QAC5C,MAAMC,SAAS,GAAGf,CAAAA,GAAAA,OAAM,AAA6B,CAAA,OAA7B,CAAC,uBAAuB,EAAE,EAAE,CAAC,AAAC;QACtD,IAAI;YAAC,GAAG;YAAE,OAAO;YAAE,EAAE;SAAC,CAACgB,QAAQ,CAACD,SAAS,CAAC,EAAE;YAC1C,OAAO,KAAK,CAAC;SACd,MAAM,IAAI;YAAC,GAAG;YAAE,MAAM;SAAC,CAACC,QAAQ,CAACD,SAAS,CAAC,EAAE;YAC5C,OAAO,IAAI,CAAC;SACb;QACD,OAAOA,SAAS,CAAC;KAClB;IAED;;;;KAIG,CACH,IAAIE,iCAAiC,GAAY;QAC/C,OAAO1B,CAAAA,GAAAA,OAAO,AAA4C,CAAA,QAA5C,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;KAC5D;IAED;;KAEG,CACH,IAAI2B,UAAU,GAAW;QACvB,OAAOC,OAAO,CAACC,GAAG,CAACF,UAAU,IAAIC,OAAO,CAACC,GAAG,CAACC,UAAU,IAAI,EAAE,CAAC;KAC/D;IAED;;;KAGG,CACH,IAAIC,uBAAuB,GAAY;QACrC,OAAO/B,CAAAA,GAAAA,OAAO,AAAkC,CAAA,QAAlC,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;KAClD;IAED,8CAA8C,CAC9C,IAAIgC,kBAAkB,GAAG;QACvB,OAAOhC,CAAAA,GAAAA,OAAO,AAA6B,CAAA,QAA7B,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;KAC7C;IAED,oFAAoF,CACpF,IAAIiC,0BAA0B,GAAG;QAC/B,OAAOjC,CAAAA,GAAAA,OAAO,AAAqC,CAAA,QAArC,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;KACrD;IAED,mDAAmD,CACnD,IAAIkC,sBAAsB,GAAG;QAC3B,OAAOlC,CAAAA,GAAAA,OAAO,AAAiC,CAAA,QAAjC,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;KACjD;IAED,gEAAgE,CAChE,IAAImC,uBAAuB,GAAY;QACrC,OAAOnC,CAAAA,GAAAA,OAAO,AAAkC,CAAA,QAAlC,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;KAClD;IAED,gGAAgG,CAChG,IAAIoC,0BAA0B,GAAY;QACxC,OAAOpC,CAAAA,GAAAA,OAAO,AAAqC,CAAA,QAArC,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;KACrD;IAED,uKAAuK,CACvK,IAAIqC,aAAa,GAAW;QAC1B,OAAO5B,CAAAA,GAAAA,OAAM,AAAsB,CAAA,OAAtB,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;KACrC;CACF;AAEM,MAAMoB,GAAG,GAAG,IAAI/B,GAAG,EAAE,AAAC;QAAhB+B,GAAG,GAAHA,GAAG"}