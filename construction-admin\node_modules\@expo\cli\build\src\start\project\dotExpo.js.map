{"version": 3, "sources": ["../../../../src/start/project/dotExpo.ts"], "sourcesContent": ["import JsonFile, { JSONObject } from '@expo/json-file';\nimport fs from 'fs';\nimport path from 'path';\n\n/** Create a set of functions for managing a file in the project's `.expo` directory. */\nexport function createTemporaryProjectFile<T extends JSONObject>(fileName: string, defaults: T) {\n  function getFile(projectRoot: string): JsonFile<T> {\n    const dotExpoDir = ensureDotExpoProjectDirectoryInitialized(projectRoot);\n    return new JsonFile<T>(path.join(dotExpoDir, fileName));\n  }\n\n  async function readAsync(projectRoot: string): Promise<T> {\n    let projectSettings;\n    try {\n      projectSettings = await getFile(projectRoot).readAsync();\n    } catch {\n      projectSettings = await getFile(projectRoot).writeAsync(defaults);\n    }\n    // Set defaults for any missing fields\n    return { ...defaults, ...projectSettings };\n  }\n\n  async function setAsync(projectRoot: string, json: Partial<T>): Promise<T> {\n    try {\n      return await getFile(projectRoot).mergeAsync(json, {\n        cantReadFileDefault: defaults,\n      });\n    } catch {\n      return await getFile(projectRoot).writeAsync({\n        ...defaults,\n        ...json,\n      });\n    }\n  }\n\n  return {\n    getFile,\n    readAsync,\n    setAsync,\n  };\n}\n\nfunction getDotExpoProjectDirectory(projectRoot: string): string {\n  return path.join(projectRoot, '.expo');\n}\n\nexport function ensureDotExpoProjectDirectoryInitialized(projectRoot: string): string {\n  const dirPath = getDotExpoProjectDirectory(projectRoot);\n  fs.mkdirSync(dirPath, { recursive: true });\n\n  const readmeFilePath = path.resolve(dirPath, 'README.md');\n  if (!fs.existsSync(readmeFilePath)) {\n    fs.writeFileSync(\n      readmeFilePath,\n      `> Why do I have a folder named \".expo\" in my project?\nThe \".expo\" folder is created when an Expo project is started using \"expo start\" command.\n> What do the files contain?\n- \"devices.json\": contains information about devices that have recently opened this project. This is used to populate the \"Development sessions\" list in your development builds.\n- \"settings.json\": contains the server configuration that is used to serve the application manifest.\n> Should I commit the \".expo\" folder?\nNo, you should not share the \".expo\" folder. It does not contain any information that is relevant for other developers working on the project, it is specific to your machine.\nUpon project creation, the \".expo\" folder is already added to your \".gitignore\" file.\n`\n    );\n  }\n  return dirPath;\n}\n"], "names": ["createTemporaryProjectFile", "ensureDotExpoProjectDirectoryInitialized", "fileName", "defaults", "getFile", "projectRoot", "dotExpoDir", "JsonFile", "path", "join", "readAsync", "projectSettings", "writeAsync", "setAsync", "json", "mergeAsync", "cantReadFileDefault", "getDotExpoProjectDirectory", "<PERSON><PERSON><PERSON>", "fs", "mkdirSync", "recursive", "readmeFilePath", "resolve", "existsSync", "writeFileSync"], "mappings": "AAAA;;;;QAKgBA,0BAA0B,GAA1BA,0BAA0B;QAyC1BC,wCAAwC,GAAxCA,wCAAwC;AA9CnB,IAAA,SAAiB,kCAAjB,iBAAiB,EAAA;AACvC,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;;;;;;AAGhB,SAASD,0BAA0B,CAAuBE,QAAgB,EAAEC,QAAW,EAAE;IAC9F,SAASC,OAAO,CAACC,WAAmB,EAAe;QACjD,MAAMC,UAAU,GAAGL,wCAAwC,CAACI,WAAW,CAAC,AAAC;QACzE,OAAO,IAAIE,SAAQ,QAAA,CAAIC,KAAI,QAAA,CAACC,IAAI,CAACH,UAAU,EAAEJ,QAAQ,CAAC,CAAC,CAAC;KACzD;IAED,eAAeQ,SAAS,CAACL,WAAmB,EAAc;QACxD,IAAIM,eAAe,AAAC;QACpB,IAAI;YACFA,eAAe,GAAG,MAAMP,OAAO,CAACC,WAAW,CAAC,CAACK,SAAS,EAAE,CAAC;SAC1D,CAAC,OAAM;YACNC,eAAe,GAAG,MAAMP,OAAO,CAACC,WAAW,CAAC,CAACO,UAAU,CAACT,QAAQ,CAAC,CAAC;SACnE;QACD,sCAAsC;QACtC,OAAO;YAAE,GAAGA,QAAQ;YAAE,GAAGQ,eAAe;SAAE,CAAC;KAC5C;IAED,eAAeE,QAAQ,CAACR,WAAmB,EAAES,IAAgB,EAAc;QACzE,IAAI;YACF,OAAO,MAAMV,OAAO,CAACC,WAAW,CAAC,CAACU,UAAU,CAACD,IAAI,EAAE;gBACjDE,mBAAmB,EAAEb,QAAQ;aAC9B,CAAC,CAAC;SACJ,CAAC,OAAM;YACN,OAAO,MAAMC,OAAO,CAACC,WAAW,CAAC,CAACO,UAAU,CAAC;gBAC3C,GAAGT,QAAQ;gBACX,GAAGW,IAAI;aACR,CAAC,CAAC;SACJ;KACF;IAED,OAAO;QACLV,OAAO;QACPM,SAAS;QACTG,QAAQ;KACT,CAAC;CACH;AAED,SAASI,0BAA0B,CAACZ,WAAmB,EAAU;IAC/D,OAAOG,KAAI,QAAA,CAACC,IAAI,CAACJ,WAAW,EAAE,OAAO,CAAC,CAAC;CACxC;AAEM,SAASJ,wCAAwC,CAACI,WAAmB,EAAU;IACpF,MAAMa,OAAO,GAAGD,0BAA0B,CAACZ,WAAW,CAAC,AAAC;IACxDc,GAAE,QAAA,CAACC,SAAS,CAACF,OAAO,EAAE;QAAEG,SAAS,EAAE,IAAI;KAAE,CAAC,CAAC;IAE3C,MAAMC,cAAc,GAAGd,KAAI,QAAA,CAACe,OAAO,CAACL,OAAO,EAAE,WAAW,CAAC,AAAC;IAC1D,IAAI,CAACC,GAAE,QAAA,CAACK,UAAU,CAACF,cAAc,CAAC,EAAE;QAClCH,GAAE,QAAA,CAACM,aAAa,CACdH,cAAc,EACd,CAAC;;;;;;;;AAQP,CAAC,CACI,CAAC;KACH;IACD,OAAOJ,OAAO,CAAC;CAChB"}