{"version": 3, "sources": ["../../../src/utils/editor.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport editors from 'env-editor';\n\nimport { env } from './env';\nimport * as Log from '../log';\n\nconst debug = require('debug')('expo:utils:editor') as typeof console.log;\n\n/** Guess what the default editor is and default to VSCode. */\nexport function guessEditor(): editors.Editor {\n  try {\n    const editor = env.EXPO_EDITOR;\n    if (editor) {\n      debug('Using $EXPO_EDITOR:', editor);\n      return editors.getEditor(editor);\n    }\n    debug('Falling back on $EDITOR:', editor);\n    return editors.defaultEditor();\n  } catch {\n    debug('Falling back on vscode');\n    return editors.getEditor('vscode');\n  }\n}\n\n/** Open a file path in a given editor. */\nexport async function openInEditorAsync(path: string): Promise<boolean> {\n  const editor = guessEditor();\n\n  debug(`Opening ${path} in ${editor?.name} (bin: ${editor?.binary}, id: ${editor?.id})`);\n  if (editor) {\n    try {\n      await spawnAsync(editor.binary, [path]);\n      return true;\n    } catch (error: any) {\n      debug(`Failed to auto open path in editor (path: ${path}, binary: ${editor.binary}):`, error);\n    }\n  }\n\n  Log.error(\n    'Could not open editor, you can set it by defining the $EDITOR environment variable with the binary of your editor. (e.g. \"vscode\" or \"atom\")'\n  );\n  return false;\n}\n"], "names": ["guessEditor", "openInEditorAsync", "Log", "debug", "require", "editor", "env", "EXPO_EDITOR", "editors", "getEditor", "defaultEditor", "path", "name", "binary", "id", "spawnAsync", "error"], "mappings": "AAAA;;;;QASgBA,WAAW,GAAXA,WAAW;QAgBLC,iBAAiB,GAAjBA,iBAAiB;AAzBhB,IAAA,WAAmB,kCAAnB,mBAAmB,EAAA;AACtB,IAAA,UAAY,kCAAZ,YAAY,EAAA;AAEZ,IAAA,IAAO,WAAP,OAAO,CAAA;AACfC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEf,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,mBAAmB,CAAC,AAAsB,AAAC;AAGnE,SAASJ,WAAW,GAAmB;IAC5C,IAAI;QACF,MAAMK,MAAM,GAAGC,IAAG,IAAA,CAACC,WAAW,AAAC;QAC/B,IAAIF,MAAM,EAAE;YACVF,KAAK,CAAC,qBAAqB,EAAEE,MAAM,CAAC,CAAC;YACrC,OAAOG,UAAO,QAAA,CAACC,SAAS,CAACJ,MAAM,CAAC,CAAC;SAClC;QACDF,KAAK,CAAC,0BAA0B,EAAEE,MAAM,CAAC,CAAC;QAC1C,OAAOG,UAAO,QAAA,CAACE,aAAa,EAAE,CAAC;KAChC,CAAC,OAAM;QACNP,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAChC,OAAOK,UAAO,QAAA,CAACC,SAAS,CAAC,QAAQ,CAAC,CAAC;KACpC;CACF;AAGM,eAAeR,iBAAiB,CAACU,IAAY,EAAoB;IACtE,MAAMN,MAAM,GAAGL,WAAW,EAAE,AAAC;IAE7BG,KAAK,CAAC,CAAC,QAAQ,EAAEQ,IAAI,CAAC,IAAI,EAAEN,MAAM,QAAM,GAAZA,KAAAA,CAAY,GAAZA,MAAM,CAAEO,IAAI,CAAC,OAAO,EAAEP,MAAM,QAAQ,GAAdA,KAAAA,CAAc,GAAdA,MAAM,CAAEQ,MAAM,CAAC,MAAM,EAAER,MAAM,QAAI,GAAVA,KAAAA,CAAU,GAAVA,MAAM,CAAES,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,IAAIT,MAAM,EAAE;QACV,IAAI;YACF,MAAMU,CAAAA,GAAAA,WAAU,AAAuB,CAAA,QAAvB,CAACV,MAAM,CAACQ,MAAM,EAAE;gBAACF,IAAI;aAAC,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC;SACb,CAAC,OAAOK,KAAK,EAAO;YACnBb,KAAK,CAAC,CAAC,0CAA0C,EAAEQ,IAAI,CAAC,UAAU,EAAEN,MAAM,CAACQ,MAAM,CAAC,EAAE,CAAC,EAAEG,KAAK,CAAC,CAAC;SAC/F;KACF;IAEDd,GAAG,CAACc,KAAK,CACP,8IAA8I,CAC/I,CAAC;IACF,OAAO,KAAK,CAAC;CACd"}