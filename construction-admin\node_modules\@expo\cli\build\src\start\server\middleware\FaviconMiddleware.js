"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
var _expoMiddleware = require("./ExpoMiddleware");
var _favicon = require("../../../export/favicon");
const debug = require("debug")("expo:start:server:middleware:favicon");
class FaviconMiddleware extends _expoMiddleware.ExpoMiddleware {
    constructor(projectRoot){
        super(projectRoot, [
            "/favicon.ico"
        ]);
        this.projectRoot = projectRoot;
    }
    async handleRequestAsync(req, res, next) {
        if (![
            "GET",
            "HEAD"
        ].includes(req.method || "")) {
            return next();
        }
        let faviconImageData;
        try {
            const data = await (0, _favicon).getFaviconFromExpoConfigAsync(this.projectRoot, {
                force: true
            });
            if (!data) {
                debug("No favicon defined in the Expo Config, skipping generation.");
                return next();
            }
            faviconImageData = data.source;
            debug("\u2705 Generated favicon successfully.");
        } catch (error) {
            debug("Failed to generate favicon from Expo config:", error);
            return next(error);
        }
        // Respond with the generated favicon file
        res.setHeader("Content-Type", "image/x-icon");
        res.end(faviconImageData);
    }
}
exports.FaviconMiddleware = FaviconMiddleware;

//# sourceMappingURL=FaviconMiddleware.js.map