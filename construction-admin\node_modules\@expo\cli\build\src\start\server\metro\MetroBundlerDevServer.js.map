{"version": 3, "sources": ["../../../../../src/start/server/metro/MetroBundlerDevServer.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { getConfig } from '@expo/config';\nimport * as runtimeEnv from '@expo/env';\nimport { SerialAsset } from '@expo/metro-config/build/serializer/serializerAssets';\nimport chalk from 'chalk';\nimport { AssetData } from 'metro';\nimport fetch from 'node-fetch';\nimport path from 'path';\n\nimport { bundleApiRoute, invalidateApiRouteCache } from './bundleApiRoutes';\nimport { createRouteHandlerMiddleware } from './createServerRouteMiddleware';\nimport { ExpoRouterServerManifestV1, fetchManifest } from './fetchRouterManifest';\nimport { instantiateMetroAsync } from './instantiateMetro';\nimport { metroWatchTypeScriptFiles } from './metroWatchTypeScriptFiles';\nimport {\n  getRouterDirectoryModuleIdWithManifest,\n  hasWarnedAboutApiRoutes,\n  isApiRouteConvention,\n  warnInvalidWebOutput,\n} from './router';\nimport { serializeHtmlWithAssets } from './serializeHtml';\nimport { observeAnyFileChanges, observeFileChanges } from './waitForMetroToObserveTypeScriptFile';\nimport { ExportAssetMap } from '../../../export/saveAssets';\nimport { Log } from '../../../log';\nimport getDevClientProperties from '../../../utils/analytics/getDevClientProperties';\nimport { logEventAsync } from '../../../utils/analytics/rudderstackClient';\nimport { CommandError } from '../../../utils/errors';\nimport { getFreePortAsync } from '../../../utils/port';\nimport { BundlerDevServer, BundlerStartOptions, DevServerInstance } from '../BundlerDevServer';\nimport { getStaticRenderFunctions } from '../getStaticRenderFunctions';\nimport { ContextModuleSourceMapsMiddleware } from '../middleware/ContextModuleSourceMapsMiddleware';\nimport { CreateFileMiddleware } from '../middleware/CreateFileMiddleware';\nimport { DevToolsPluginMiddleware } from '../middleware/DevToolsPluginMiddleware';\nimport { FaviconMiddleware } from '../middleware/FaviconMiddleware';\nimport { HistoryFallbackMiddleware } from '../middleware/HistoryFallbackMiddleware';\nimport { InterstitialPageMiddleware } from '../middleware/InterstitialPageMiddleware';\nimport { resolveMainModuleName } from '../middleware/ManifestMiddleware';\nimport { ReactDevToolsPageMiddleware } from '../middleware/ReactDevToolsPageMiddleware';\nimport {\n  DeepLinkHandler,\n  RuntimeRedirectMiddleware,\n} from '../middleware/RuntimeRedirectMiddleware';\nimport { ServeStaticMiddleware } from '../middleware/ServeStaticMiddleware';\nimport {\n  shouldEnableAsyncImports,\n  createBundleUrlPath,\n  getBaseUrlFromExpoConfig,\n  getAsyncRoutesFromExpoConfig,\n} from '../middleware/metroOptions';\nimport { prependMiddleware } from '../middleware/mutations';\nimport { startTypescriptTypeGenerationAsync } from '../type-generation/startTypescriptTypeGeneration';\n\nexport type ExpoRouterRuntimeManifest = Awaited<\n  ReturnType<typeof import('expo-router/build/static/renderStaticContent').getManifest>\n>;\n\nexport class ForwardHtmlError extends CommandError {\n  constructor(\n    message: string,\n    public html: string,\n    public statusCode: number\n  ) {\n    super(message);\n  }\n}\n\nconst debug = require('debug')('expo:start:server:metro') as typeof console.log;\n\n/** Default port to use for apps running in Expo Go. */\nconst EXPO_GO_METRO_PORT = 8081;\n\n/** Default port to use for apps that run in standard React Native projects or Expo Dev Clients. */\nconst DEV_CLIENT_METRO_PORT = 8081;\n\nexport class MetroBundlerDevServer extends BundlerDevServer {\n  private metro: import('metro').Server | null = null;\n\n  get name(): string {\n    return 'metro';\n  }\n\n  async resolvePortAsync(options: Partial<BundlerStartOptions> = {}): Promise<number> {\n    const port =\n      // If the manually defined port is busy then an error should be thrown...\n      options.port ??\n      // Otherwise use the default port based on the runtime target.\n      (options.devClient\n        ? // Don't check if the port is busy if we're using the dev client since most clients are hardcoded to 8081.\n          Number(process.env.RCT_METRO_PORT) || DEV_CLIENT_METRO_PORT\n        : // Otherwise (running in Expo Go) use a free port that falls back on the classic 8081 port.\n          await getFreePortAsync(EXPO_GO_METRO_PORT));\n\n    return port;\n  }\n\n  async exportExpoRouterApiRoutesAsync({\n    mode,\n    outputDir,\n    prerenderManifest,\n    baseUrl,\n    routerRoot,\n  }: {\n    mode: 'development' | 'production';\n    outputDir: string;\n    // This does not contain the API routes info.\n    prerenderManifest: ExpoRouterServerManifestV1;\n    baseUrl: string;\n    routerRoot: string;\n  }): Promise<{ files: ExportAssetMap; manifest: ExpoRouterServerManifestV1<string> }> {\n    const appDir = path.join(this.projectRoot, routerRoot);\n    const manifest = await this.getExpoRouterRoutesManifestAsync({ appDir });\n\n    const files: ExportAssetMap = new Map();\n\n    for (const route of manifest.apiRoutes) {\n      const filepath = path.join(appDir, route.file);\n      const contents = await bundleApiRoute(this.projectRoot, filepath, {\n        mode,\n        routerRoot,\n        port: this.getInstance()?.location.port,\n        shouldThrow: true,\n        baseUrl,\n      });\n      const artifactFilename = path.join(\n        outputDir,\n        path.relative(appDir, filepath.replace(/\\.[tj]sx?$/, '.js'))\n      );\n      if (contents) {\n        files.set(artifactFilename, {\n          contents: contents.src,\n          targetDomain: 'server',\n        });\n      }\n      // Remap the manifest files to represent the output files.\n      route.file = artifactFilename;\n    }\n\n    return {\n      manifest: {\n        ...manifest,\n        htmlRoutes: prerenderManifest.htmlRoutes,\n      },\n      files,\n    };\n  }\n\n  async getExpoRouterRoutesManifestAsync({ appDir }: { appDir: string }) {\n    // getBuiltTimeServerManifest\n    const manifest = await fetchManifest(this.projectRoot, {\n      asJson: true,\n      appDir,\n    });\n\n    if (!manifest) {\n      throw new CommandError(\n        'EXPO_ROUTER_SERVER_MANIFEST',\n        'Unexpected error: server manifest could not be fetched.'\n      );\n    }\n\n    return manifest;\n  }\n\n  async getStaticRenderFunctionAsync({\n    mode,\n    minify = mode !== 'development',\n    baseUrl,\n    routerRoot,\n  }: {\n    mode: 'development' | 'production';\n    minify?: boolean;\n    baseUrl: string;\n    routerRoot: string;\n  }): Promise<{\n    serverManifest: ExpoRouterServerManifestV1;\n    manifest: ExpoRouterRuntimeManifest;\n    renderAsync: (path: string) => Promise<string>;\n  }> {\n    const url = this.getDevServerUrl()!;\n\n    const { getStaticContent, getManifest, getBuildTimeServerManifestAsync } =\n      await getStaticRenderFunctions(this.projectRoot, url, {\n        minify,\n        dev: mode !== 'production',\n        // Ensure the API Routes are included\n        environment: 'node',\n        baseUrl,\n        routerRoot,\n      });\n\n    return {\n      serverManifest: await getBuildTimeServerManifestAsync(),\n      // Get routes from Expo Router.\n      manifest: await getManifest({ fetchData: true, preserveApiRoutes: false }),\n      // Get route generating function\n      async renderAsync(path: string) {\n        return await getStaticContent(new URL(path, url));\n      },\n    };\n  }\n\n  async getStaticResourcesAsync({\n    mode,\n    minify = mode !== 'development',\n    includeSourceMaps,\n    baseUrl,\n    mainModuleName,\n    isExporting,\n    asyncRoutes,\n    routerRoot,\n  }: {\n    isExporting: boolean;\n    mode: string;\n    minify?: boolean;\n    includeSourceMaps?: boolean;\n    baseUrl?: string;\n    mainModuleName?: string;\n    asyncRoutes: boolean;\n    routerRoot: string;\n  }): Promise<{ artifacts: SerialAsset[]; assets?: AssetData[] }> {\n    const devBundleUrlPathname = createBundleUrlPath({\n      platform: 'web',\n      mode,\n      minify,\n      environment: 'client',\n      serializerOutput: 'static',\n      serializerIncludeMaps: includeSourceMaps,\n      mainModuleName:\n        mainModuleName ?? resolveMainModuleName(this.projectRoot, { platform: 'web' }),\n      lazy: shouldEnableAsyncImports(this.projectRoot),\n      asyncRoutes,\n      baseUrl,\n      isExporting,\n      routerRoot,\n      bytecode: false,\n    });\n\n    const bundleUrl = new URL(devBundleUrlPathname, this.getDevServerUrl()!);\n\n    // Fetch the generated HTML from our custom Metro serializer\n    const results = await fetch(bundleUrl.toString());\n\n    const txt = await results.text();\n\n    let data: any;\n    try {\n      data = JSON.parse(txt);\n    } catch (error: any) {\n      debug(txt);\n\n      // Metro can throw this error when the initial module id cannot be resolved.\n      if (!results.ok && txt.startsWith('<!DOCTYPE html>')) {\n        throw new ForwardHtmlError(\n          `Metro failed to bundle the project. Check the console for more information.`,\n          txt,\n          results.status\n        );\n      }\n\n      Log.error(\n        'Failed to generate resources with Metro, the Metro config may not be using the correct serializer. Ensure the metro.config.js is extending the expo/metro-config and is not overriding the serializer.'\n      );\n      throw error;\n    }\n\n    // NOTE: This could potentially need more validation in the future.\n    if ('artifacts' in data && Array.isArray(data.artifacts)) {\n      return data;\n    }\n\n    if (data != null && (data.errors || data.type?.match(/.*Error$/))) {\n      // {\n      //   type: 'InternalError',\n      //   errors: [],\n      //   message: 'Metro has encountered an error: While trying to resolve module `stylis` from file `/Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/@emotion/cache/dist/emotion-cache.browser.esm.js`, the package `/Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/stylis/package.json` was successfully found. However, this package itself specifies a `main` module field that could not be resolved (`/Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/stylis/dist/stylis.mjs`. Indeed, none of these files exist:\\n' +\n      //     '\\n' +\n      //     '  * /Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/stylis/dist/stylis.mjs(.web.ts|.ts|.web.tsx|.tsx|.web.js|.js|.web.jsx|.jsx|.web.json|.json|.web.cjs|.cjs|.web.scss|.scss|.web.sass|.sass|.web.css|.css)\\n' +\n      //     '  * /Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/stylis/dist/stylis.mjs/index(.web.ts|.ts|.web.tsx|.tsx|.web.js|.js|.web.jsx|.jsx|.web.json|.json|.web.cjs|.cjs|.web.scss|.scss|.web.sass|.sass|.web.css|.css): /Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/metro/src/node-haste/DependencyGraph.js (289:17)\\n' +\n      //     '\\n' +\n      //     '\\x1B[0m \\x1B[90m 287 |\\x1B[39m         }\\x1B[0m\\n' +\n      //     '\\x1B[0m \\x1B[90m 288 |\\x1B[39m         \\x1B[36mif\\x1B[39m (error \\x1B[36minstanceof\\x1B[39m \\x1B[33mInvalidPackageError\\x1B[39m) {\\x1B[0m\\n' +\n      //     '\\x1B[0m\\x1B[31m\\x1B[1m>\\x1B[22m\\x1B[39m\\x1B[90m 289 |\\x1B[39m           \\x1B[36mthrow\\x1B[39m \\x1B[36mnew\\x1B[39m \\x1B[33mPackageResolutionError\\x1B[39m({\\x1B[0m\\n' +\n      //     '\\x1B[0m \\x1B[90m     |\\x1B[39m                 \\x1B[31m\\x1B[1m^\\x1B[22m\\x1B[39m\\x1B[0m\\n' +\n      //     '\\x1B[0m \\x1B[90m 290 |\\x1B[39m             packageError\\x1B[33m:\\x1B[39m error\\x1B[33m,\\x1B[39m\\x1B[0m\\n' +\n      //     '\\x1B[0m \\x1B[90m 291 |\\x1B[39m             originModulePath\\x1B[33m:\\x1B[39m \\x1B[36mfrom\\x1B[39m\\x1B[33m,\\x1B[39m\\x1B[0m\\n' +\n      //     '\\x1B[0m \\x1B[90m 292 |\\x1B[39m             targetModuleName\\x1B[33m:\\x1B[39m to\\x1B[33m,\\x1B[39m\\x1B[0m'\n      // }\n      // The Metro logger already showed this error.\n      throw new Error(data.message);\n    }\n\n    throw new Error(\n      'Invalid resources returned from the Metro serializer. Expected array, found: ' + data\n    );\n  }\n\n  private async getStaticPageAsync(\n    pathname: string,\n    {\n      mode,\n      minify = mode !== 'development',\n      baseUrl,\n      routerRoot,\n      isExporting,\n      asyncRoutes,\n    }: {\n      isExporting: boolean;\n      mode: 'development' | 'production';\n      minify?: boolean;\n      baseUrl: string;\n      asyncRoutes: boolean;\n      routerRoot: string;\n    }\n  ) {\n    const devBundleUrlPathname = createBundleUrlPath({\n      platform: 'web',\n      mode,\n      environment: 'client',\n      mainModuleName: resolveMainModuleName(this.projectRoot, { platform: 'web' }),\n      lazy: shouldEnableAsyncImports(this.projectRoot),\n      baseUrl,\n      isExporting,\n      asyncRoutes,\n      routerRoot,\n      bytecode: false,\n    });\n\n    const bundleStaticHtml = async (): Promise<string> => {\n      const { getStaticContent } = await getStaticRenderFunctions(\n        this.projectRoot,\n        this.getDevServerUrl()!,\n        {\n          minify: false,\n          dev: mode !== 'production',\n          // Ensure the API Routes are included\n          environment: 'node',\n          baseUrl,\n          routerRoot,\n        }\n      );\n\n      const location = new URL(pathname, this.getDevServerUrl()!);\n      return await getStaticContent(location);\n    };\n\n    const [{ artifacts: resources }, staticHtml] = await Promise.all([\n      this.getStaticResourcesAsync({ isExporting, mode, minify, baseUrl, asyncRoutes, routerRoot }),\n      bundleStaticHtml(),\n    ]);\n    const content = serializeHtmlWithAssets({\n      mode,\n      resources,\n      template: staticHtml,\n      devBundleUrl: devBundleUrlPathname,\n      baseUrl,\n    });\n    return {\n      content,\n      resources,\n    };\n  }\n\n  async watchEnvironmentVariables() {\n    if (!this.instance) {\n      throw new Error(\n        'Cannot observe environment variable changes without a running Metro instance.'\n      );\n    }\n    if (!this.metro) {\n      // This can happen when the run command is used and the server is already running in another\n      // process.\n      debug('Skipping Environment Variable observation because Metro is not running (headless).');\n      return;\n    }\n\n    const envFiles = runtimeEnv\n      .getFiles(process.env.NODE_ENV)\n      .map((fileName) => path.join(this.projectRoot, fileName));\n\n    observeFileChanges(\n      {\n        metro: this.metro,\n        server: this.instance.server,\n      },\n      envFiles,\n      () => {\n        debug('Reloading environment variables...');\n        // Force reload the environment variables.\n        runtimeEnv.load(this.projectRoot, { force: true });\n      }\n    );\n  }\n\n  protected async startImplementationAsync(\n    options: BundlerStartOptions\n  ): Promise<DevServerInstance> {\n    options.port = await this.resolvePortAsync(options);\n    this.urlCreator = this.getUrlCreator(options);\n\n    const parsedOptions = {\n      port: options.port,\n      maxWorkers: options.maxWorkers,\n      resetCache: options.resetDevServer,\n    };\n\n    // Required for symbolication:\n    process.env.EXPO_DEV_SERVER_ORIGIN = `http://localhost:${options.port}`;\n\n    const { metro, server, middleware, messageSocket } = await instantiateMetroAsync(\n      this,\n      parsedOptions,\n      {\n        isExporting: !!options.isExporting,\n      }\n    );\n\n    const manifestMiddleware = await this.getManifestMiddlewareAsync(options);\n\n    // Important that we noop source maps for context modules as soon as possible.\n    prependMiddleware(middleware, new ContextModuleSourceMapsMiddleware().getHandler());\n\n    // We need the manifest handler to be the first middleware to run so our\n    // routes take precedence over static files. For example, the manifest is\n    // served from '/' and if the user has an index.html file in their project\n    // then the manifest handler will never run, the static middleware will run\n    // and serve index.html instead of the manifest.\n    // https://github.com/expo/expo/issues/13114\n    prependMiddleware(middleware, manifestMiddleware.getHandler());\n\n    middleware.use(\n      new InterstitialPageMiddleware(this.projectRoot, {\n        // TODO: Prevent this from becoming stale.\n        scheme: options.location.scheme ?? null,\n      }).getHandler()\n    );\n    middleware.use(new ReactDevToolsPageMiddleware(this.projectRoot).getHandler());\n    middleware.use(\n      new DevToolsPluginMiddleware(this.projectRoot, this.devToolsPluginManager).getHandler()\n    );\n\n    const deepLinkMiddleware = new RuntimeRedirectMiddleware(this.projectRoot, {\n      onDeepLink: getDeepLinkHandler(this.projectRoot),\n      getLocation: ({ runtime }) => {\n        if (runtime === 'custom') {\n          return this.urlCreator?.constructDevClientUrl();\n        } else {\n          return this.urlCreator?.constructUrl({\n            scheme: 'exp',\n          });\n        }\n      },\n    });\n    middleware.use(deepLinkMiddleware.getHandler());\n\n    middleware.use(new CreateFileMiddleware(this.projectRoot).getHandler());\n\n    // Append support for redirecting unhandled requests to the index.html page on web.\n    if (this.isTargetingWeb()) {\n      const config = getConfig(this.projectRoot, { skipSDKVersionRequirement: true });\n      const { exp } = config;\n      const useServerRendering = ['static', 'server'].includes(exp.web?.output ?? '');\n\n      // This MUST be after the manifest middleware so it doesn't have a chance to serve the template `public/index.html`.\n      middleware.use(new ServeStaticMiddleware(this.projectRoot).getHandler());\n\n      // This should come after the static middleware so it doesn't serve the favicon from `public/favicon.ico`.\n      middleware.use(new FaviconMiddleware(this.projectRoot).getHandler());\n\n      if (useServerRendering) {\n        const baseUrl = getBaseUrlFromExpoConfig(exp);\n        const asyncRoutes = getAsyncRoutesFromExpoConfig(exp, options.mode ?? 'development', 'web');\n        const routerRoot = getRouterDirectoryModuleIdWithManifest(this.projectRoot, exp);\n        const appDir = path.join(this.projectRoot, routerRoot);\n        middleware.use(\n          createRouteHandlerMiddleware(this.projectRoot, {\n            ...options,\n            appDir,\n            baseUrl,\n            routerRoot,\n            config,\n            getWebBundleUrl: manifestMiddleware.getWebBundleUrl.bind(manifestMiddleware),\n            getStaticPageAsync: (pathname) => {\n              return this.getStaticPageAsync(pathname, {\n                isExporting: !!options.isExporting,\n                mode: options.mode ?? 'development',\n                minify: options.minify,\n                baseUrl,\n                asyncRoutes,\n                routerRoot,\n              });\n            },\n          })\n        );\n\n        observeAnyFileChanges(\n          {\n            metro,\n            server,\n          },\n          (events) => {\n            if (exp.web?.output === 'server') {\n              // NOTE(EvanBacon): We aren't sure what files the API routes are using so we'll just invalidate\n              // aggressively to ensure we always have the latest. The only caching we really get here is for\n              // cases where the user is making subsequent requests to the same API route without changing anything.\n              // This is useful for testing but pretty suboptimal. Luckily our caching is pretty aggressive so it makes\n              // up for a lot of the overhead.\n              invalidateApiRouteCache();\n            } else if (!hasWarnedAboutApiRoutes()) {\n              for (const event of events) {\n                if (\n                  // If the user did not delete a file that matches the Expo Router API Route convention, then we should warn that\n                  // API Routes are not enabled in the project.\n                  event.metadata?.type !== 'd' &&\n                  // Ensure the file is in the project's routes directory to prevent false positives in monorepos.\n                  event.filePath.startsWith(appDir) &&\n                  isApiRouteConvention(event.filePath)\n                ) {\n                  warnInvalidWebOutput();\n                }\n              }\n            }\n          }\n        );\n      } else {\n        // This MUST run last since it's the fallback.\n        middleware.use(\n          new HistoryFallbackMiddleware(manifestMiddleware.getHandler().internal).getHandler()\n        );\n      }\n    }\n    // Extend the close method to ensure that we clean up the local info.\n    const originalClose = server.close.bind(server);\n\n    server.close = (callback?: (err?: Error) => void) => {\n      return originalClose((err?: Error) => {\n        this.instance = null;\n        this.metro = null;\n        callback?.(err);\n      });\n    };\n\n    this.metro = metro;\n    return {\n      server,\n      location: {\n        // The port is the main thing we want to send back.\n        port: options.port,\n        // localhost isn't always correct.\n        host: 'localhost',\n        // http is the only supported protocol on native.\n        url: `http://localhost:${options.port}`,\n        protocol: 'http',\n      },\n      middleware,\n      messageSocket,\n    };\n  }\n\n  public async waitForTypeScriptAsync(): Promise<boolean> {\n    if (!this.instance) {\n      throw new Error('Cannot wait for TypeScript without a running server.');\n    }\n\n    return new Promise<boolean>((resolve) => {\n      if (!this.metro) {\n        // This can happen when the run command is used and the server is already running in another\n        // process. In this case we can't wait for the TypeScript check to complete because we don't\n        // have access to the Metro server.\n        debug('Skipping TypeScript check because Metro is not running (headless).');\n        return resolve(false);\n      }\n\n      const off = metroWatchTypeScriptFiles({\n        projectRoot: this.projectRoot,\n        server: this.instance!.server,\n        metro: this.metro,\n        tsconfig: true,\n        throttle: true,\n        eventTypes: ['change', 'add'],\n        callback: async () => {\n          // Run once, this prevents the TypeScript project prerequisite from running on every file change.\n          off();\n          const { TypeScriptProjectPrerequisite } = await import(\n            '../../doctor/typescript/TypeScriptProjectPrerequisite.js'\n          );\n\n          try {\n            const req = new TypeScriptProjectPrerequisite(this.projectRoot);\n            await req.bootstrapAsync();\n            resolve(true);\n          } catch (error: any) {\n            // Ensure the process doesn't fail if the TypeScript check fails.\n            // This could happen during the install.\n            Log.log();\n            Log.error(\n              chalk.red`Failed to automatically setup TypeScript for your project. Try restarting the dev server to fix.`\n            );\n            Log.exception(error);\n            resolve(false);\n          }\n        },\n      });\n    });\n  }\n\n  public async startTypeScriptServices() {\n    return startTypescriptTypeGenerationAsync({\n      server: this.instance?.server,\n      metro: this.metro,\n      projectRoot: this.projectRoot,\n    });\n  }\n\n  protected getConfigModuleIds(): string[] {\n    return ['./metro.config.js', './metro.config.json', './rn-cli.config.js'];\n  }\n}\n\nexport function getDeepLinkHandler(projectRoot: string): DeepLinkHandler {\n  return async ({ runtime }) => {\n    if (runtime === 'expo') return;\n    const { exp } = getConfig(projectRoot);\n    await logEventAsync('dev client start command', {\n      status: 'started',\n      ...getDevClientProperties(projectRoot, exp),\n    });\n  };\n}\n"], "names": ["getDeepLinkHandler", "runtimeEnv", "ForwardHtmlError", "CommandError", "constructor", "message", "html", "statusCode", "debug", "require", "EXPO_GO_METRO_PORT", "DEV_CLIENT_METRO_PORT", "MetroBundlerDevServer", "BundlerDevServer", "metro", "name", "resolvePortAsync", "options", "port", "devClient", "Number", "process", "env", "RCT_METRO_PORT", "getFreePortAsync", "exportExpoRouterApiRoutesAsync", "mode", "outputDir", "prerenderManifest", "baseUrl", "routerRoot", "appDir", "path", "join", "projectRoot", "manifest", "getExpoRouterRoutesManifestAsync", "files", "Map", "route", "apiRoutes", "filepath", "file", "contents", "bundleApiRoute", "getInstance", "location", "shouldThrow", "artifactFilename", "relative", "replace", "set", "src", "targetDomain", "htmlRoutes", "fetchManifest", "as<PERSON><PERSON>", "getStaticRenderFunctionAsync", "minify", "url", "getDevServerUrl", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getManifest", "getBuildTimeServerManifestAsync", "getStaticRenderFunctions", "dev", "environment", "serverManifest", "fetchData", "preserveApiRoutes", "renderAsync", "URL", "getStaticResourcesAsync", "includeSourceMaps", "mainModuleName", "isExporting", "asyncRoutes", "data", "devBundleUrlPathname", "createBundleUrlPath", "platform", "serializerOutput", "serializerIncludeMaps", "resolveMainModuleName", "lazy", "shouldEnableAsyncImports", "bytecode", "bundleUrl", "results", "fetch", "toString", "txt", "text", "JSON", "parse", "error", "ok", "startsWith", "status", "Log", "Array", "isArray", "artifacts", "errors", "type", "match", "Error", "getStaticPageAsync", "pathname", "bundleStaticHtml", "resources", "staticHtml", "Promise", "all", "content", "serializeHtmlWithAssets", "template", "devBundleUrl", "watchEnvironmentVariables", "instance", "envFiles", "getFiles", "NODE_ENV", "map", "fileName", "observeFileChanges", "server", "load", "force", "startImplementationAsync", "urlCreator", "getUrlCreator", "parsedOptions", "maxWorkers", "resetCache", "resetDevServer", "EXPO_DEV_SERVER_ORIGIN", "middleware", "messageSocket", "instantiateMetroAsync", "manifestMiddleware", "getManifestMiddlewareAsync", "prependMiddleware", "ContextModuleSourceMapsMiddleware", "<PERSON><PERSON><PERSON><PERSON>", "use", "InterstitialPageMiddleware", "scheme", "ReactDevToolsPageMiddleware", "DevToolsPluginMiddleware", "devToolsPluginManager", "deepLinkMiddleware", "RuntimeRedirectMiddleware", "onDeepLink", "getLocation", "runtime", "constructDevClientUrl", "constructUrl", "CreateFileMiddleware", "isTargetingWeb", "exp", "config", "getConfig", "skipSDKVersionRequirement", "useServerRendering", "includes", "web", "output", "ServeStaticMiddleware", "FaviconMiddleware", "getBaseUrlFromExpoConfig", "getAsyncRoutesFromExpoConfig", "getRouterDirectoryModuleIdWithManifest", "createRouteHandlerMiddleware", "getWebBundleUrl", "bind", "observeAnyFileChanges", "events", "invalidateApiRouteCache", "hasWarnedAboutApiRoutes", "event", "metadata", "filePath", "isApiRouteConvention", "warnInvalidWebOutput", "HistoryFallbackMiddleware", "internal", "originalClose", "close", "callback", "err", "host", "protocol", "waitForTypeScriptAsync", "resolve", "off", "metroWatchTypeScriptFiles", "tsconfig", "throttle", "eventTypes", "TypeScriptProjectPrerequisite", "req", "bootstrapAsync", "log", "chalk", "red", "exception", "startTypeScriptServices", "startTypescriptTypeGenerationAsync", "getConfigModuleIds", "logEventAsync", "getDevClientProperties"], "mappings": "AAMA;;;;QAymBgBA,kBAAkB,GAAlBA,kBAAkB;AAzmBR,IAAA,OAAc,WAAd,cAAc,CAAA;AAC5BC,IAAAA,UAAU,mCAAM,WAAW,EAAjB;AAEJ,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEP,IAAA,UAAY,kCAAZ,YAAY,EAAA;AACb,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEiC,IAAA,gBAAmB,WAAnB,mBAAmB,CAAA;AAC9B,IAAA,4BAA+B,WAA/B,+BAA+B,CAAA;AAClB,IAAA,oBAAuB,WAAvB,uBAAuB,CAAA;AAC3C,IAAA,iBAAoB,WAApB,oBAAoB,CAAA;AAChB,IAAA,0BAA6B,WAA7B,6BAA6B,CAAA;AAMhE,IAAA,OAAU,WAAV,UAAU,CAAA;AACuB,IAAA,cAAiB,WAAjB,iBAAiB,CAAA;AACC,IAAA,oCAAuC,WAAvC,uCAAuC,CAAA;AAE7E,IAAA,IAAc,WAAd,cAAc,CAAA;AACC,IAAA,uBAAiD,kCAAjD,iDAAiD,EAAA;AACtD,IAAA,kBAA4C,WAA5C,4CAA4C,CAAA;AAC7C,IAAA,OAAuB,WAAvB,uBAAuB,CAAA;AACnB,IAAA,KAAqB,WAArB,qBAAqB,CAAA;AACmB,IAAA,iBAAqB,WAArB,qBAAqB,CAAA;AACrD,IAAA,yBAA6B,WAA7B,6BAA6B,CAAA;AACpB,IAAA,kCAAiD,WAAjD,iDAAiD,CAAA;AAC9D,IAAA,qBAAoC,WAApC,oCAAoC,CAAA;AAChC,IAAA,yBAAwC,WAAxC,wCAAwC,CAAA;AAC/C,IAAA,kBAAiC,WAAjC,iCAAiC,CAAA;AACzB,IAAA,0BAAyC,WAAzC,yCAAyC,CAAA;AACxC,IAAA,2BAA0C,WAA1C,0CAA0C,CAAA;AAC/C,IAAA,mBAAkC,WAAlC,kCAAkC,CAAA;AAC5B,IAAA,4BAA2C,WAA3C,2CAA2C,CAAA;AAIhF,IAAA,0BAAyC,WAAzC,yCAAyC,CAAA;AACV,IAAA,sBAAqC,WAArC,qCAAqC,CAAA;AAMpE,IAAA,aAA4B,WAA5B,4BAA4B,CAAA;AACD,IAAA,UAAyB,WAAzB,yBAAyB,CAAA;AACR,IAAA,8BAAkD,WAAlD,kDAAkD,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9F,MAAMC,gBAAgB,SAASC,OAAY,aAAA;IAChDC,YACEC,OAAe,EACRC,IAAY,EACZC,UAAkB,CACzB;QACA,KAAK,CAACF,OAAO,CAAC,CAAC;aAHRC,IAAY,GAAZA,IAAY;aACZC,UAAkB,GAAlBA,UAAkB;KAG1B;CACF;QARYL,gBAAgB,GAAhBA,gBAAgB;AAU7B,MAAMM,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,AAAsB,AAAC;AAEhF,uDAAuD,CACvD,MAAMC,kBAAkB,GAAG,IAAI,AAAC;AAEhC,mGAAmG,CACnG,MAAMC,qBAAqB,GAAG,IAAI,AAAC;AAE5B,MAAMC,qBAAqB,SAASC,iBAAgB,iBAAA;IACzD,AAAQC,KAAK,GAAkC,IAAI,CAAC;IAEpD,IAAIC,IAAI,GAAW;QACjB,OAAO,OAAO,CAAC;KAChB;IAED,MAAMC,gBAAgB,CAACC,OAAqC,GAAG,EAAE,EAAmB;YAEhF,yEAAyE;QACzEA,MAAY;QAFd,MAAMC,IAAI,GAERD,CAAAA,MAAY,GAAZA,OAAO,CAACC,IAAI,YAAZD,MAAY,GACZ,8DAA8D;QAC9D,CAACA,OAAO,CAACE,SAAS,GAEdC,MAAM,CAACC,OAAO,CAACC,GAAG,CAACC,cAAc,CAAC,IAAIZ,qBAAqB,GAE3D,MAAMa,CAAAA,GAAAA,KAAgB,AAAoB,CAAA,iBAApB,CAACd,kBAAkB,CAAC,CAAC,AAAC;QAElD,OAAOQ,IAAI,CAAC;KACb;IAED,MAAMO,8BAA8B,CAAC,EACnCC,IAAI,CAAA,EACJC,SAAS,CAAA,EACTC,iBAAiB,CAAA,EACjBC,OAAO,CAAA,EACPC,UAAU,CAAA,EAQX,EAAoF;QACnF,MAAMC,MAAM,GAAGC,KAAI,QAAA,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAEJ,UAAU,CAAC,AAAC;QACvD,MAAMK,QAAQ,GAAG,MAAM,IAAI,CAACC,gCAAgC,CAAC;YAAEL,MAAM;SAAE,CAAC,AAAC;QAEzE,MAAMM,KAAK,GAAmB,IAAIC,GAAG,EAAE,AAAC;QAExC,KAAK,MAAMC,KAAK,IAAIJ,QAAQ,CAACK,SAAS,CAAE;gBAK9B,GAAkB;YAJ1B,MAAMC,QAAQ,GAAGT,KAAI,QAAA,CAACC,IAAI,CAACF,MAAM,EAAEQ,KAAK,CAACG,IAAI,CAAC,AAAC;YAC/C,MAAMC,QAAQ,GAAG,MAAMC,CAAAA,GAAAA,gBAAc,AAMnC,CAAA,eANmC,CAAC,IAAI,CAACV,WAAW,EAAEO,QAAQ,EAAE;gBAChEf,IAAI;gBACJI,UAAU;gBACVZ,IAAI,EAAE,CAAA,GAAkB,GAAlB,IAAI,CAAC2B,WAAW,EAAE,SAAU,GAA5B,KAAA,CAA4B,GAA5B,GAAkB,CAAEC,QAAQ,CAAC5B,IAAI;gBACvC6B,WAAW,EAAE,IAAI;gBACjBlB,OAAO;aACR,CAAC,AAAC;YACH,MAAMmB,gBAAgB,GAAGhB,KAAI,QAAA,CAACC,IAAI,CAChCN,SAAS,EACTK,KAAI,QAAA,CAACiB,QAAQ,CAAClB,MAAM,EAAEU,QAAQ,CAACS,OAAO,eAAe,KAAK,CAAC,CAAC,CAC7D,AAAC;YACF,IAAIP,QAAQ,EAAE;gBACZN,KAAK,CAACc,GAAG,CAACH,gBAAgB,EAAE;oBAC1BL,QAAQ,EAAEA,QAAQ,CAACS,GAAG;oBACtBC,YAAY,EAAE,QAAQ;iBACvB,CAAC,CAAC;aACJ;YACD,0DAA0D;YAC1Dd,KAAK,CAACG,IAAI,GAAGM,gBAAgB,CAAC;SAC/B;QAED,OAAO;YACLb,QAAQ,EAAE;gBACR,GAAGA,QAAQ;gBACXmB,UAAU,EAAE1B,iBAAiB,CAAC0B,UAAU;aACzC;YACDjB,KAAK;SACN,CAAC;KACH;IAED,MAAMD,gCAAgC,CAAC,EAAEL,MAAM,CAAA,EAAsB,EAAE;QACrE,6BAA6B;QAC7B,MAAMI,QAAQ,GAAG,MAAMoB,CAAAA,GAAAA,oBAAa,AAGlC,CAAA,cAHkC,CAAC,IAAI,CAACrB,WAAW,EAAE;YACrDsB,MAAM,EAAE,IAAI;YACZzB,MAAM;SACP,CAAC,AAAC;QAEH,IAAI,CAACI,QAAQ,EAAE;YACb,MAAM,IAAIhC,OAAY,aAAA,CACpB,6BAA6B,EAC7B,yDAAyD,CAC1D,CAAC;SACH;QAED,OAAOgC,QAAQ,CAAC;KACjB;IAED,MAAMsB,4BAA4B,CAAC,EACjC/B,IAAI,CAAA,EACJgC,MAAM,EAAGhC,IAAI,KAAK,aAAa,CAAA,EAC/BG,OAAO,CAAA,EACPC,UAAU,CAAA,EAMX,EAIE;QACD,MAAM6B,GAAG,GAAG,IAAI,CAACC,eAAe,EAAE,AAAC,AAAC;QAEpC,MAAM,EAAEC,gBAAgB,CAAA,EAAEC,WAAW,CAAA,EAAEC,+BAA+B,CAAA,EAAE,GACtE,MAAMC,CAAAA,GAAAA,yBAAwB,AAO5B,CAAA,yBAP4B,CAAC,IAAI,CAAC9B,WAAW,EAAEyB,GAAG,EAAE;YACpDD,MAAM;YACNO,GAAG,EAAEvC,IAAI,KAAK,YAAY;YAC1B,qCAAqC;YACrCwC,WAAW,EAAE,MAAM;YACnBrC,OAAO;YACPC,UAAU;SACX,CAAC,AAAC;QAEL,OAAO;YACLqC,cAAc,EAAE,MAAMJ,+BAA+B,EAAE;YACvD,+BAA+B;YAC/B5B,QAAQ,EAAE,MAAM2B,WAAW,CAAC;gBAAEM,SAAS,EAAE,IAAI;gBAAEC,iBAAiB,EAAE,KAAK;aAAE,CAAC;YAC1E,gCAAgC;YAChC,MAAMC,WAAW,EAACtC,IAAY,EAAE;gBAC9B,OAAO,MAAM6B,gBAAgB,CAAC,IAAIU,GAAG,CAACvC,IAAI,EAAE2B,GAAG,CAAC,CAAC,CAAC;aACnD;SACF,CAAC;KACH;IAED,MAAMa,uBAAuB,CAAC,EAC5B9C,IAAI,CAAA,EACJgC,MAAM,EAAGhC,IAAI,KAAK,aAAa,CAAA,EAC/B+C,iBAAiB,CAAA,EACjB5C,OAAO,CAAA,EACP6C,cAAc,CAAA,EACdC,WAAW,CAAA,EACXC,WAAW,CAAA,EACX9C,UAAU,CAAA,EAUX,EAA+D;YAmD1B+C,GAAS;QAlD7C,MAAMC,oBAAoB,GAAGC,CAAAA,GAAAA,aAAmB,AAe9C,CAAA,oBAf8C,CAAC;YAC/CC,QAAQ,EAAE,KAAK;YACftD,IAAI;YACJgC,MAAM;YACNQ,WAAW,EAAE,QAAQ;YACrBe,gBAAgB,EAAE,QAAQ;YAC1BC,qBAAqB,EAAET,iBAAiB;YACxCC,cAAc,EACZA,cAAc,WAAdA,cAAc,GAAIS,CAAAA,GAAAA,mBAAqB,AAAuC,CAAA,sBAAvC,CAAC,IAAI,CAACjD,WAAW,EAAE;gBAAE8C,QAAQ,EAAE,KAAK;aAAE,CAAC;YAChFI,IAAI,EAAEC,CAAAA,GAAAA,aAAwB,AAAkB,CAAA,yBAAlB,CAAC,IAAI,CAACnD,WAAW,CAAC;YAChD0C,WAAW;YACX/C,OAAO;YACP8C,WAAW;YACX7C,UAAU;YACVwD,QAAQ,EAAE,KAAK;SAChB,CAAC,AAAC;QAEH,MAAMC,SAAS,GAAG,IAAIhB,GAAG,CAACO,oBAAoB,EAAE,IAAI,CAAClB,eAAe,EAAE,CAAE,AAAC;QAEzE,4DAA4D;QAC5D,MAAM4B,OAAO,GAAG,MAAMC,CAAAA,GAAAA,UAAK,AAAsB,CAAA,QAAtB,CAACF,SAAS,CAACG,QAAQ,EAAE,CAAC,AAAC;QAElD,MAAMC,GAAG,GAAG,MAAMH,OAAO,CAACI,IAAI,EAAE,AAAC;QAEjC,IAAIf,IAAI,AAAK,AAAC;QACd,IAAI;YACFA,IAAI,GAAGgB,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC,CAAC;SACxB,CAAC,OAAOI,KAAK,EAAO;YACnBvF,KAAK,CAACmF,GAAG,CAAC,CAAC;YAEX,4EAA4E;YAC5E,IAAI,CAACH,OAAO,CAACQ,EAAE,IAAIL,GAAG,CAACM,UAAU,CAAC,iBAAiB,CAAC,EAAE;gBACpD,MAAM,IAAI/F,gBAAgB,CACxB,CAAC,2EAA2E,CAAC,EAC7EyF,GAAG,EACHH,OAAO,CAACU,MAAM,CACf,CAAC;aACH;YAEDC,IAAG,IAAA,CAACJ,KAAK,CACP,wMAAwM,CACzM,CAAC;YACF,MAAMA,KAAK,CAAC;SACb;QAED,mEAAmE;QACnE,IAAI,WAAW,IAAIlB,IAAI,IAAIuB,KAAK,CAACC,OAAO,CAACxB,IAAI,CAACyB,SAAS,CAAC,EAAE;YACxD,OAAOzB,IAAI,CAAC;SACb;QAED,IAAIA,IAAI,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC0B,MAAM,KAAI1B,CAAAA,GAAS,GAATA,IAAI,CAAC2B,IAAI,SAAO,GAAhB3B,KAAAA,CAAgB,GAAhBA,GAAS,CAAE4B,KAAK,YAAY,CAAA,CAAC,EAAE;YACjE,IAAI;YACJ,2BAA2B;YAC3B,gBAAgB;YAChB,2jBAA2jB;YAC3jB,aAAa;YACb,8OAA8O;YAC9O,4WAA4W;YAC5W,aAAa;YACb,4DAA4D;YAC5D,sJAAsJ;YACtJ,8KAA8K;YAC9K,mGAAmG;YACnG,mHAAmH;YACnH,sIAAsI;YACtI,gHAAgH;YAChH,IAAI;YACJ,8CAA8C;YAC9C,MAAM,IAAIC,KAAK,CAAC7B,IAAI,CAACxE,OAAO,CAAC,CAAC;SAC/B;QAED,MAAM,IAAIqG,KAAK,CACb,+EAA+E,GAAG7B,IAAI,CACvF,CAAC;KACH;IAED,MAAc8B,kBAAkB,CAC9BC,QAAgB,EAChB,EACElF,IAAI,CAAA,EACJgC,MAAM,EAAGhC,IAAI,KAAK,aAAa,CAAA,EAC/BG,OAAO,CAAA,EACPC,UAAU,CAAA,EACV6C,WAAW,CAAA,EACXC,WAAW,CAAA,EAQZ,EACD;QACA,MAAME,oBAAoB,GAAGC,CAAAA,GAAAA,aAAmB,AAW9C,CAAA,oBAX8C,CAAC;YAC/CC,QAAQ,EAAE,KAAK;YACftD,IAAI;YACJwC,WAAW,EAAE,QAAQ;YACrBQ,cAAc,EAAES,CAAAA,GAAAA,mBAAqB,AAAuC,CAAA,sBAAvC,CAAC,IAAI,CAACjD,WAAW,EAAE;gBAAE8C,QAAQ,EAAE,KAAK;aAAE,CAAC;YAC5EI,IAAI,EAAEC,CAAAA,GAAAA,aAAwB,AAAkB,CAAA,yBAAlB,CAAC,IAAI,CAACnD,WAAW,CAAC;YAChDL,OAAO;YACP8C,WAAW;YACXC,WAAW;YACX9C,UAAU;YACVwD,QAAQ,EAAE,KAAK;SAChB,CAAC,AAAC;QAEH,MAAMuB,gBAAgB,GAAG,UAA6B;YACpD,MAAM,EAAEhD,gBAAgB,CAAA,EAAE,GAAG,MAAMG,CAAAA,GAAAA,yBAAwB,AAW1D,CAAA,yBAX0D,CACzD,IAAI,CAAC9B,WAAW,EAChB,IAAI,CAAC0B,eAAe,EAAE,EACtB;gBACEF,MAAM,EAAE,KAAK;gBACbO,GAAG,EAAEvC,IAAI,KAAK,YAAY;gBAC1B,qCAAqC;gBACrCwC,WAAW,EAAE,MAAM;gBACnBrC,OAAO;gBACPC,UAAU;aACX,CACF,AAAC;YAEF,MAAMgB,QAAQ,GAAG,IAAIyB,GAAG,CAACqC,QAAQ,EAAE,IAAI,CAAChD,eAAe,EAAE,CAAE,AAAC;YAC5D,OAAO,MAAMC,gBAAgB,CAACf,QAAQ,CAAC,CAAC;SACzC,AAAC;QAEF,MAAM,CAAC,EAAEwD,SAAS,EAAEQ,SAAS,CAAA,EAAE,EAAEC,UAAU,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC;YAC/D,IAAI,CAACzC,uBAAuB,CAAC;gBAAEG,WAAW;gBAAEjD,IAAI;gBAAEgC,MAAM;gBAAE7B,OAAO;gBAAE+C,WAAW;gBAAE9C,UAAU;aAAE,CAAC;YAC7F+E,gBAAgB,EAAE;SACnB,CAAC,AAAC;QACH,MAAMK,OAAO,GAAGC,CAAAA,GAAAA,cAAuB,AAMrC,CAAA,wBANqC,CAAC;YACtCzF,IAAI;YACJoF,SAAS;YACTM,QAAQ,EAAEL,UAAU;YACpBM,YAAY,EAAEvC,oBAAoB;YAClCjD,OAAO;SACR,CAAC,AAAC;QACH,OAAO;YACLqF,OAAO;YACPJ,SAAS;SACV,CAAC;KACH;IAED,MAAMQ,yBAAyB,GAAG;QAChC,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;YAClB,MAAM,IAAIb,KAAK,CACb,+EAA+E,CAChF,CAAC;SACH;QACD,IAAI,CAAC,IAAI,CAAC5F,KAAK,EAAE;YACf,4FAA4F;YAC5F,WAAW;YACXN,KAAK,CAAC,oFAAoF,CAAC,CAAC;YAC5F,OAAO;SACR;QAED,MAAMgH,QAAQ,GAAGvH,UAAU,CACxBwH,QAAQ,CAACpG,OAAO,CAACC,GAAG,CAACoG,QAAQ,CAAC,CAC9BC,GAAG,CAAC,CAACC,QAAQ,GAAK5F,KAAI,QAAA,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE0F,QAAQ,CAAC;QAAA,CAAC,AAAC;QAE5DC,CAAAA,GAAAA,oCAAkB,AAWjB,CAAA,mBAXiB,CAChB;YACE/G,KAAK,EAAE,IAAI,CAACA,KAAK;YACjBgH,MAAM,EAAE,IAAI,CAACP,QAAQ,CAACO,MAAM;SAC7B,EACDN,QAAQ,EACR,IAAM;YACJhH,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAC5C,0CAA0C;YAC1CP,UAAU,CAAC8H,IAAI,CAAC,IAAI,CAAC7F,WAAW,EAAE;gBAAE8F,KAAK,EAAE,IAAI;aAAE,CAAC,CAAC;SACpD,CACF,CAAC;KACH;IAED,MAAgBC,wBAAwB,CACtChH,OAA4B,EACA;QAC5BA,OAAO,CAACC,IAAI,GAAG,MAAM,IAAI,CAACF,gBAAgB,CAACC,OAAO,CAAC,CAAC;QACpD,IAAI,CAACiH,UAAU,GAAG,IAAI,CAACC,aAAa,CAAClH,OAAO,CAAC,CAAC;QAE9C,MAAMmH,aAAa,GAAG;YACpBlH,IAAI,EAAED,OAAO,CAACC,IAAI;YAClBmH,UAAU,EAAEpH,OAAO,CAACoH,UAAU;YAC9BC,UAAU,EAAErH,OAAO,CAACsH,cAAc;SACnC,AAAC;QAEF,8BAA8B;QAC9BlH,OAAO,CAACC,GAAG,CAACkH,sBAAsB,GAAG,CAAC,iBAAiB,EAAEvH,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;QAExE,MAAM,EAAEJ,KAAK,CAAA,EAAEgH,MAAM,CAAA,EAAEW,UAAU,CAAA,EAAEC,aAAa,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,iBAAqB,AAM/E,CAAA,sBAN+E,CAC9E,IAAI,EACJP,aAAa,EACb;YACEzD,WAAW,EAAE,CAAC,CAAC1D,OAAO,CAAC0D,WAAW;SACnC,CACF,AAAC;QAEF,MAAMiE,kBAAkB,GAAG,MAAM,IAAI,CAACC,0BAA0B,CAAC5H,OAAO,CAAC,AAAC;QAE1E,8EAA8E;QAC9E6H,CAAAA,GAAAA,UAAiB,AAAkE,CAAA,kBAAlE,CAACL,UAAU,EAAE,IAAIM,kCAAiC,kCAAA,EAAE,CAACC,UAAU,EAAE,CAAC,CAAC;QAEpF,wEAAwE;QACxE,yEAAyE;QACzE,0EAA0E;QAC1E,2EAA2E;QAC3E,gDAAgD;QAChD,4CAA4C;QAC5CF,CAAAA,GAAAA,UAAiB,AAA6C,CAAA,kBAA7C,CAACL,UAAU,EAAEG,kBAAkB,CAACI,UAAU,EAAE,CAAC,CAAC;YAKnD/H,OAAuB;QAHnCwH,UAAU,CAACQ,GAAG,CACZ,IAAIC,2BAA0B,2BAAA,CAAC,IAAI,CAAChH,WAAW,EAAE;YAC/C,0CAA0C;YAC1CiH,MAAM,EAAElI,CAAAA,OAAuB,GAAvBA,OAAO,CAAC6B,QAAQ,CAACqG,MAAM,YAAvBlI,OAAuB,GAAI,IAAI;SACxC,CAAC,CAAC+H,UAAU,EAAE,CAChB,CAAC;QACFP,UAAU,CAACQ,GAAG,CAAC,IAAIG,4BAA2B,4BAAA,CAAC,IAAI,CAAClH,WAAW,CAAC,CAAC8G,UAAU,EAAE,CAAC,CAAC;QAC/EP,UAAU,CAACQ,GAAG,CACZ,IAAII,yBAAwB,yBAAA,CAAC,IAAI,CAACnH,WAAW,EAAE,IAAI,CAACoH,qBAAqB,CAAC,CAACN,UAAU,EAAE,CACxF,CAAC;QAEF,MAAMO,kBAAkB,GAAG,IAAIC,0BAAyB,0BAAA,CAAC,IAAI,CAACtH,WAAW,EAAE;YACzEuH,UAAU,EAAEzJ,kBAAkB,CAAC,IAAI,CAACkC,WAAW,CAAC;YAChDwH,WAAW,EAAE,CAAC,EAAEC,OAAO,CAAA,EAAE,GAAK;gBAC5B,IAAIA,OAAO,KAAK,QAAQ,EAAE;wBACjB,GAAe;oBAAtB,OAAO,CAAA,GAAe,GAAf,IAAI,CAACzB,UAAU,SAAuB,GAAtC,KAAA,CAAsC,GAAtC,GAAe,CAAE0B,qBAAqB,EAAE,CAAC;iBACjD,MAAM;wBACE,IAAe;oBAAtB,OAAO,CAAA,IAAe,GAAf,IAAI,CAAC1B,UAAU,SAAc,GAA7B,KAAA,CAA6B,GAA7B,IAAe,CAAE2B,YAAY,CAAC;wBACnCV,MAAM,EAAE,KAAK;qBACd,CAAC,CAAC;iBACJ;aACF;SACF,CAAC,AAAC;QACHV,UAAU,CAACQ,GAAG,CAACM,kBAAkB,CAACP,UAAU,EAAE,CAAC,CAAC;QAEhDP,UAAU,CAACQ,GAAG,CAAC,IAAIa,qBAAoB,qBAAA,CAAC,IAAI,CAAC5H,WAAW,CAAC,CAAC8G,UAAU,EAAE,CAAC,CAAC;QAExE,mFAAmF;QACnF,IAAI,IAAI,CAACe,cAAc,EAAE,EAAE;gBAGgCC,IAAO;YAFhE,MAAMC,MAAM,GAAGC,CAAAA,GAAAA,OAAS,AAAuD,CAAA,UAAvD,CAAC,IAAI,CAAChI,WAAW,EAAE;gBAAEiI,yBAAyB,EAAE,IAAI;aAAE,CAAC,AAAC;YAChF,MAAM,EAAEH,GAAG,CAAA,EAAE,GAAGC,MAAM,AAAC;gBACkCD,IAAe;YAAxE,MAAMI,kBAAkB,GAAG;gBAAC,QAAQ;gBAAE,QAAQ;aAAC,CAACC,QAAQ,CAACL,CAAAA,IAAe,GAAfA,CAAAA,IAAO,GAAPA,GAAG,CAACM,GAAG,SAAQ,GAAfN,KAAAA,CAAe,GAAfA,IAAO,CAAEO,MAAM,YAAfP,IAAe,GAAI,EAAE,CAAC,AAAC;YAEhF,oHAAoH;YACpHvB,UAAU,CAACQ,GAAG,CAAC,IAAIuB,sBAAqB,sBAAA,CAAC,IAAI,CAACtI,WAAW,CAAC,CAAC8G,UAAU,EAAE,CAAC,CAAC;YAEzE,0GAA0G;YAC1GP,UAAU,CAACQ,GAAG,CAAC,IAAIwB,kBAAiB,kBAAA,CAAC,IAAI,CAACvI,WAAW,CAAC,CAAC8G,UAAU,EAAE,CAAC,CAAC;YAErE,IAAIoB,kBAAkB,EAAE;gBACtB,MAAMvI,OAAO,GAAG6I,CAAAA,GAAAA,aAAwB,AAAK,CAAA,yBAAL,CAACV,GAAG,CAAC,AAAC;oBACQ/I,MAAY;gBAAlE,MAAM2D,WAAW,GAAG+F,CAAAA,GAAAA,aAA4B,AAA2C,CAAA,6BAA3C,CAACX,GAAG,EAAE/I,CAAAA,MAAY,GAAZA,OAAO,CAACS,IAAI,YAAZT,MAAY,GAAI,aAAa,EAAE,KAAK,CAAC,AAAC;gBAC5F,MAAMa,UAAU,GAAG8I,CAAAA,GAAAA,OAAsC,AAAuB,CAAA,uCAAvB,CAAC,IAAI,CAAC1I,WAAW,EAAE8H,GAAG,CAAC,AAAC;gBACjF,MAAMjI,MAAM,GAAGC,KAAI,QAAA,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAEJ,UAAU,CAAC,AAAC;gBACvD2G,UAAU,CAACQ,GAAG,CACZ4B,CAAAA,GAAAA,4BAA4B,AAiB1B,CAAA,6BAjB0B,CAAC,IAAI,CAAC3I,WAAW,EAAE;oBAC7C,GAAGjB,OAAO;oBACVc,MAAM;oBACNF,OAAO;oBACPC,UAAU;oBACVmI,MAAM;oBACNa,eAAe,EAAElC,kBAAkB,CAACkC,eAAe,CAACC,IAAI,CAACnC,kBAAkB,CAAC;oBAC5EjC,kBAAkB,EAAE,CAACC,QAAQ,GAAK;4BAGxB3F,KAAY;wBAFpB,OAAO,IAAI,CAAC0F,kBAAkB,CAACC,QAAQ,EAAE;4BACvCjC,WAAW,EAAE,CAAC,CAAC1D,OAAO,CAAC0D,WAAW;4BAClCjD,IAAI,EAAET,CAAAA,KAAY,GAAZA,OAAO,CAACS,IAAI,YAAZT,KAAY,GAAI,aAAa;4BACnCyC,MAAM,EAAEzC,OAAO,CAACyC,MAAM;4BACtB7B,OAAO;4BACP+C,WAAW;4BACX9C,UAAU;yBACX,CAAC,CAAC;qBACJ;iBACF,CAAC,CACH,CAAC;gBAEFkJ,CAAAA,GAAAA,oCAAqB,AA4BpB,CAAA,sBA5BoB,CACnB;oBACElK,KAAK;oBACLgH,MAAM;iBACP,EACD,CAACmD,MAAM,GAAK;wBACNjB,GAAO;oBAAX,IAAIA,CAAAA,CAAAA,GAAO,GAAPA,GAAG,CAACM,GAAG,SAAQ,GAAfN,KAAAA,CAAe,GAAfA,GAAO,CAAEO,MAAM,CAAA,KAAK,QAAQ,EAAE;wBAChC,+FAA+F;wBAC/F,+FAA+F;wBAC/F,sGAAsG;wBACtG,yGAAyG;wBACzG,gCAAgC;wBAChCW,CAAAA,GAAAA,gBAAuB,AAAE,CAAA,wBAAF,EAAE,CAAC;qBAC3B,MAAM,IAAI,CAACC,CAAAA,GAAAA,OAAuB,AAAE,CAAA,wBAAF,EAAE,EAAE;wBACrC,KAAK,MAAMC,KAAK,IAAIH,MAAM,CAAE;gCAExB,gHAAgH;4BAChH,6CAA6C;4BAC7CG,IAAc;4BAHhB,IAGEA,CAAAA,CAAAA,IAAc,GAAdA,KAAK,CAACC,QAAQ,SAAM,GAApBD,KAAAA,CAAoB,GAApBA,IAAc,CAAE5E,IAAI,CAAA,KAAK,GAAG,IAC5B,gGAAgG;4BAChG4E,KAAK,CAACE,QAAQ,CAACrF,UAAU,CAAClE,MAAM,CAAC,IACjCwJ,CAAAA,GAAAA,OAAoB,AAAgB,CAAA,qBAAhB,CAACH,KAAK,CAACE,QAAQ,CAAC,EACpC;gCACAE,CAAAA,GAAAA,OAAoB,AAAE,CAAA,qBAAF,EAAE,CAAC;6BACxB;yBACF;qBACF;iBACF,CACF,CAAC;aACH,MAAM;gBACL,8CAA8C;gBAC9C/C,UAAU,CAACQ,GAAG,CACZ,IAAIwC,0BAAyB,0BAAA,CAAC7C,kBAAkB,CAACI,UAAU,EAAE,CAAC0C,QAAQ,CAAC,CAAC1C,UAAU,EAAE,CACrF,CAAC;aACH;SACF;QACD,qEAAqE;QACrE,MAAM2C,aAAa,GAAG7D,MAAM,CAAC8D,KAAK,CAACb,IAAI,CAACjD,MAAM,CAAC,AAAC;QAEhDA,MAAM,CAAC8D,KAAK,GAAG,CAACC,QAAgC,GAAK;YACnD,OAAOF,aAAa,CAAC,CAACG,GAAW,GAAK;gBACpC,IAAI,CAACvE,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAACzG,KAAK,GAAG,IAAI,CAAC;gBAClB+K,QAAQ,QAAO,GAAfA,KAAAA,CAAe,GAAfA,QAAQ,CAAGC,GAAG,CAAC,AA9hBvB,CA8hBwB;aACjB,CAAC,CAAC;SACJ,CAAC;QAEF,IAAI,CAAChL,KAAK,GAAGA,KAAK,CAAC;QACnB,OAAO;YACLgH,MAAM;YACNhF,QAAQ,EAAE;gBACR,mDAAmD;gBACnD5B,IAAI,EAAED,OAAO,CAACC,IAAI;gBAClB,kCAAkC;gBAClC6K,IAAI,EAAE,WAAW;gBACjB,iDAAiD;gBACjDpI,GAAG,EAAE,CAAC,iBAAiB,EAAE1C,OAAO,CAACC,IAAI,CAAC,CAAC;gBACvC8K,QAAQ,EAAE,MAAM;aACjB;YACDvD,UAAU;YACVC,aAAa;SACd,CAAC;KACH;IAED,MAAauD,sBAAsB,GAAqB;QACtD,IAAI,CAAC,IAAI,CAAC1E,QAAQ,EAAE;YAClB,MAAM,IAAIb,KAAK,CAAC,sDAAsD,CAAC,CAAC;SACzE;QAED,OAAO,IAAIM,OAAO,CAAU,CAACkF,OAAO,GAAK;YACvC,IAAI,CAAC,IAAI,CAACpL,KAAK,EAAE;gBACf,4FAA4F;gBAC5F,4FAA4F;gBAC5F,mCAAmC;gBACnCN,KAAK,CAAC,oEAAoE,CAAC,CAAC;gBAC5E,OAAO0L,OAAO,CAAC,KAAK,CAAC,CAAC;aACvB;YAED,MAAMC,GAAG,GAAGC,CAAAA,GAAAA,0BAAyB,AA6BnC,CAAA,0BA7BmC,CAAC;gBACpClK,WAAW,EAAE,IAAI,CAACA,WAAW;gBAC7B4F,MAAM,EAAE,IAAI,CAACP,QAAQ,CAAEO,MAAM;gBAC7BhH,KAAK,EAAE,IAAI,CAACA,KAAK;gBACjBuL,QAAQ,EAAE,IAAI;gBACdC,QAAQ,EAAE,IAAI;gBACdC,UAAU,EAAE;oBAAC,QAAQ;oBAAE,KAAK;iBAAC;gBAC7BV,QAAQ,EAAE,UAAY;oBACpB,iGAAiG;oBACjGM,GAAG,EAAE,CAAC;oBACN,MAAM,EAAEK,6BAA6B,CAAA,EAAE,GAAG,MAAM;+DAC9C,0DAA0D;sBAC3D,AAAC;oBAEF,IAAI;wBACF,MAAMC,GAAG,GAAG,IAAID,6BAA6B,CAAC,IAAI,CAACtK,WAAW,CAAC,AAAC;wBAChE,MAAMuK,GAAG,CAACC,cAAc,EAAE,CAAC;wBAC3BR,OAAO,CAAC,IAAI,CAAC,CAAC;qBACf,CAAC,OAAOnG,KAAK,EAAO;wBACnB,iEAAiE;wBACjE,wCAAwC;wBACxCI,IAAG,IAAA,CAACwG,GAAG,EAAE,CAAC;wBACVxG,IAAG,IAAA,CAACJ,KAAK,CACP6G,MAAK,QAAA,CAACC,GAAG,CAAC,gGAAgG,CAAC,CAC5G,CAAC;wBACF1G,IAAG,IAAA,CAAC2G,SAAS,CAAC/G,KAAK,CAAC,CAAC;wBACrBmG,OAAO,CAAC,KAAK,CAAC,CAAC;qBAChB;iBACF;aACF,CAAC,AAAC;SACJ,CAAC,CAAC;KACJ;IAED,MAAaa,uBAAuB,GAAG;YAE3B,GAAa;QADvB,OAAOC,CAAAA,GAAAA,8BAAkC,AAIvC,CAAA,mCAJuC,CAAC;YACxClF,MAAM,EAAE,CAAA,GAAa,GAAb,IAAI,CAACP,QAAQ,SAAQ,GAArB,KAAA,CAAqB,GAArB,GAAa,CAAEO,MAAM;YAC7BhH,KAAK,EAAE,IAAI,CAACA,KAAK;YACjBoB,WAAW,EAAE,IAAI,CAACA,WAAW;SAC9B,CAAC,CAAC;KACJ;IAED,AAAU+K,kBAAkB,GAAa;QACvC,OAAO;YAAC,mBAAmB;YAAE,qBAAqB;YAAE,oBAAoB;SAAC,CAAC;KAC3E;CACF;QA9hBYrM,qBAAqB,GAArBA,qBAAqB;AAgiB3B,SAASZ,kBAAkB,CAACkC,WAAmB,EAAmB;IACvE,OAAO,OAAO,EAAEyH,OAAO,CAAA,EAAE,GAAK;QAC5B,IAAIA,OAAO,KAAK,MAAM,EAAE,OAAO;QAC/B,MAAM,EAAEK,GAAG,CAAA,EAAE,GAAGE,CAAAA,GAAAA,OAAS,AAAa,CAAA,UAAb,CAAChI,WAAW,CAAC,AAAC;QACvC,MAAMgL,CAAAA,GAAAA,kBAAa,AAGjB,CAAA,cAHiB,CAAC,0BAA0B,EAAE;YAC9ChH,MAAM,EAAE,SAAS;YACjB,GAAGiH,CAAAA,GAAAA,uBAAsB,AAAkB,CAAA,QAAlB,CAACjL,WAAW,EAAE8H,GAAG,CAAC;SAC5C,CAAC,CAAC;KACJ,CAAC;CACH"}