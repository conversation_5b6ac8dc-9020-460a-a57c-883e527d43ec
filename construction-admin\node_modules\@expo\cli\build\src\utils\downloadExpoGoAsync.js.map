{"version": 3, "sources": ["../../../src/utils/downloadExpoGoAsync.ts"], "sourcesContent": ["import { getExpoHomeDirectory } from '@expo/config/build/getUserState';\nimport path from 'path';\nimport ProgressBar from 'progress';\nimport { gt } from 'semver';\n\nimport { downloadAppAsync } from './downloadAppAsync';\nimport { CommandError } from './errors';\nimport { ora } from './ora';\nimport { profile } from './profile';\nimport { createProgressBar } from './progress';\nimport { getVersionsAsync, SDKVersion } from '../api/getVersions';\nimport { Log } from '../log';\n\nconst debug = require('debug')('expo:utils:downloadExpoGo') as typeof console.log;\n\nconst platformSettings: Record<\n  string,\n  {\n    shouldExtractResults: boolean;\n    versionsKey: keyof SDKVersion;\n    getFilePath: (filename: string) => string;\n  }\n> = {\n  ios: {\n    versionsKey: 'iosClientUrl',\n    getFilePath: (filename) =>\n      path.join(getExpoHomeDirectory(), 'ios-simulator-app-cache', `${filename}.app`),\n    shouldExtractResults: true,\n  },\n  android: {\n    versionsKey: 'androidClientUrl',\n    getFilePath: (filename) =>\n      path.join(getExpoHomeDirectory(), 'android-apk-cache', `${filename}.apk`),\n    shouldExtractResults: false,\n  },\n};\n\n/**\n * @internal exposed for testing.\n * @returns the matching `SDKVersion` object from the Expo API.\n */\nexport async function getExpoGoVersionEntryAsync(sdkVersion: string): Promise<SDKVersion> {\n  const { sdkVersions: versions } = await getVersionsAsync();\n  let version: SDKVersion;\n\n  if (sdkVersion.toUpperCase() === 'UNVERSIONED') {\n    // find the latest version\n    const latestVersionKey = Object.keys(versions).reduce((a, b) => {\n      if (gt(b, a)) {\n        return b;\n      }\n      return a;\n    }, '0.0.0');\n\n    Log.warn(\n      `Downloading the latest Expo Go client (${latestVersionKey}). This will not fully conform to UNVERSIONED.`\n    );\n    version = versions[latestVersionKey];\n  } else {\n    version = versions[sdkVersion];\n  }\n\n  if (!version) {\n    throw new CommandError(`Unable to find a version of Expo Go for SDK ${sdkVersion}`);\n  }\n  return version;\n}\n\n/** Download the Expo Go app from the Expo servers (if only it was this easy for every app). */\nexport async function downloadExpoGoAsync(\n  platform: keyof typeof platformSettings,\n  {\n    url,\n    sdkVersion,\n  }: {\n    url?: string;\n    sdkVersion?: string;\n  }\n): Promise<string> {\n  const { getFilePath, versionsKey, shouldExtractResults } = platformSettings[platform];\n\n  const spinner = ora({ text: 'Fetching Expo Go', color: 'white' }).start();\n\n  let bar: ProgressBar | null = null;\n\n  try {\n    if (!url) {\n      if (!sdkVersion) {\n        throw new CommandError(\n          `Unable to determine which Expo Go version to install (platform: ${platform})`\n        );\n      }\n\n      const version = await getExpoGoVersionEntryAsync(sdkVersion);\n\n      debug(`Installing Expo Go version for SDK ${sdkVersion} at URL: ${version[versionsKey]}`);\n      url = version[versionsKey] as string;\n    }\n  } catch (error) {\n    spinner.fail();\n    throw error;\n  }\n\n  const filename = path.parse(url).name;\n\n  try {\n    const outputPath = getFilePath(filename);\n    debug(`Downloading Expo Go from \"${url}\" to \"${outputPath}\".`);\n    debug(\n      `The requested copy of Expo Go might already be cached in: \"${getExpoHomeDirectory()}\". You can disable the cache with EXPO_NO_CACHE=1`\n    );\n    await profile(downloadAppAsync)({\n      url,\n      // Save all encrypted cache data to `~/.expo/expo-go`\n      cacheDirectory: 'expo-go',\n      outputPath,\n      extract: shouldExtractResults,\n      onProgress({ progress, total }) {\n        if (progress && total) {\n          if (!bar) {\n            if (spinner.isSpinning) {\n              spinner.stop();\n            }\n            bar = createProgressBar('Downloading the Expo Go app [:bar] :percent :etas', {\n              width: 64,\n              total: 100,\n              // clear: true,\n              complete: '=',\n              incomplete: ' ',\n            });\n          } else {\n            bar!.update(progress, total);\n          }\n        }\n      },\n    });\n    return outputPath;\n  } finally {\n    spinner.stop();\n    // @ts-expect-error\n    bar?.terminate();\n  }\n}\n"], "names": ["getExpoGoVersionEntryAsync", "downloadExpoGoAsync", "debug", "require", "platformSettings", "ios", "versionsKey", "getFilePath", "filename", "path", "join", "getExpoHomeDirectory", "shouldExtractResults", "android", "sdkVersion", "sdkVersions", "versions", "getVersionsAsync", "version", "toUpperCase", "latestVersionKey", "Object", "keys", "reduce", "a", "b", "gt", "Log", "warn", "CommandError", "platform", "url", "spinner", "ora", "text", "color", "start", "bar", "error", "fail", "parse", "name", "outputPath", "profile", "downloadAppAsync", "cacheDirectory", "extract", "onProgress", "progress", "total", "isSpinning", "stop", "createProgressBar", "width", "complete", "incomplete", "update", "terminate"], "mappings": "AAAA;;;;QAyCsBA,0BAA0B,GAA1BA,0BAA0B;QA4B1BC,mBAAmB,GAAnBA,mBAAmB;AArEJ,IAAA,aAAiC,WAAjC,iCAAiC,CAAA;AACrD,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEJ,IAAA,OAAQ,WAAR,QAAQ,CAAA;AAEM,IAAA,iBAAoB,WAApB,oBAAoB,CAAA;AACxB,IAAA,OAAU,WAAV,UAAU,CAAA;AACnB,IAAA,IAAO,WAAP,OAAO,CAAA;AACH,IAAA,QAAW,WAAX,WAAW,CAAA;AACD,IAAA,SAAY,WAAZ,YAAY,CAAA;AACD,IAAA,YAAoB,WAApB,oBAAoB,CAAA;AAC7C,IAAA,IAAQ,WAAR,QAAQ,CAAA;;;;;;AAE5B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,2BAA2B,CAAC,AAAsB,AAAC;AAElF,MAAMC,gBAAgB,GAOlB;IACFC,GAAG,EAAE;QACHC,WAAW,EAAE,cAAc;QAC3BC,WAAW,EAAE,CAACC,QAAQ,GACpBC,KAAI,QAAA,CAACC,IAAI,CAACC,CAAAA,GAAAA,aAAoB,AAAE,CAAA,qBAAF,EAAE,EAAE,yBAAyB,EAAE,CAAC,EAAEH,QAAQ,CAAC,IAAI,CAAC,CAAC;QAAA;QACjFI,oBAAoB,EAAE,IAAI;KAC3B;IACDC,OAAO,EAAE;QACPP,WAAW,EAAE,kBAAkB;QAC/BC,WAAW,EAAE,CAACC,QAAQ,GACpBC,KAAI,QAAA,CAACC,IAAI,CAACC,CAAAA,GAAAA,aAAoB,AAAE,CAAA,qBAAF,EAAE,EAAE,mBAAmB,EAAE,CAAC,EAAEH,QAAQ,CAAC,IAAI,CAAC,CAAC;QAAA;QAC3EI,oBAAoB,EAAE,KAAK;KAC5B;CACF,AAAC;AAMK,eAAeZ,0BAA0B,CAACc,UAAkB,EAAuB;IACxF,MAAM,EAAEC,WAAW,EAAEC,QAAQ,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,YAAgB,AAAE,CAAA,iBAAF,EAAE,AAAC;IAC3D,IAAIC,OAAO,AAAY,AAAC;IAExB,IAAIJ,UAAU,CAACK,WAAW,EAAE,KAAK,aAAa,EAAE;QAC9C,0BAA0B;QAC1B,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,IAAI,CAACN,QAAQ,CAAC,CAACO,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAK;YAC9D,IAAIC,CAAAA,GAAAA,OAAE,AAAM,CAAA,GAAN,CAACD,CAAC,EAAED,CAAC,CAAC,EAAE;gBACZ,OAAOC,CAAC,CAAC;aACV;YACD,OAAOD,CAAC,CAAC;SACV,EAAE,OAAO,CAAC,AAAC;QAEZG,IAAG,IAAA,CAACC,IAAI,CACN,CAAC,uCAAuC,EAAER,gBAAgB,CAAC,8CAA8C,CAAC,CAC3G,CAAC;QACFF,OAAO,GAAGF,QAAQ,CAACI,gBAAgB,CAAC,CAAC;KACtC,MAAM;QACLF,OAAO,GAAGF,QAAQ,CAACF,UAAU,CAAC,CAAC;KAChC;IAED,IAAI,CAACI,OAAO,EAAE;QACZ,MAAM,IAAIW,OAAY,aAAA,CAAC,CAAC,4CAA4C,EAAEf,UAAU,CAAC,CAAC,CAAC,CAAC;KACrF;IACD,OAAOI,OAAO,CAAC;CAChB;AAGM,eAAejB,mBAAmB,CACvC6B,QAAuC,EACvC,EACEC,GAAG,CAAA,EACHjB,UAAU,CAAA,EAIX,EACgB;IACjB,MAAM,EAAEP,WAAW,CAAA,EAAED,WAAW,CAAA,EAAEM,oBAAoB,CAAA,EAAE,GAAGR,gBAAgB,CAAC0B,QAAQ,CAAC,AAAC;IAEtF,MAAME,OAAO,GAAGC,CAAAA,GAAAA,IAAG,AAA8C,CAAA,IAA9C,CAAC;QAAEC,IAAI,EAAE,kBAAkB;QAAEC,KAAK,EAAE,OAAO;KAAE,CAAC,CAACC,KAAK,EAAE,AAAC;IAE1E,IAAIC,GAAG,GAAuB,IAAI,AAAC;IAEnC,IAAI;QACF,IAAI,CAACN,GAAG,EAAE;YACR,IAAI,CAACjB,UAAU,EAAE;gBACf,MAAM,IAAIe,OAAY,aAAA,CACpB,CAAC,gEAAgE,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAC/E,CAAC;aACH;YAED,MAAMZ,OAAO,GAAG,MAAMlB,0BAA0B,CAACc,UAAU,CAAC,AAAC;YAE7DZ,KAAK,CAAC,CAAC,mCAAmC,EAAEY,UAAU,CAAC,SAAS,EAAEI,OAAO,CAACZ,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1FyB,GAAG,GAAGb,OAAO,CAACZ,WAAW,CAAC,AAAU,CAAC;SACtC;KACF,CAAC,OAAOgC,KAAK,EAAE;QACdN,OAAO,CAACO,IAAI,EAAE,CAAC;QACf,MAAMD,KAAK,CAAC;KACb;IAED,MAAM9B,QAAQ,GAAGC,KAAI,QAAA,CAAC+B,KAAK,CAACT,GAAG,CAAC,CAACU,IAAI,AAAC;IAEtC,IAAI;QACF,MAAMC,UAAU,GAAGnC,WAAW,CAACC,QAAQ,CAAC,AAAC;QACzCN,KAAK,CAAC,CAAC,0BAA0B,EAAE6B,GAAG,CAAC,MAAM,EAAEW,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/DxC,KAAK,CACH,CAAC,2DAA2D,EAAES,CAAAA,GAAAA,aAAoB,AAAE,CAAA,qBAAF,EAAE,CAAC,iDAAiD,CAAC,CACxI,CAAC;QACF,MAAMgC,CAAAA,GAAAA,QAAO,AAAkB,CAAA,QAAlB,CAACC,iBAAgB,iBAAA,CAAC,CAAC;YAC9Bb,GAAG;YACH,qDAAqD;YACrDc,cAAc,EAAE,SAAS;YACzBH,UAAU;YACVI,OAAO,EAAElC,oBAAoB;YAC7BmC,UAAU,EAAC,EAAEC,QAAQ,CAAA,EAAEC,KAAK,CAAA,EAAE,EAAE;gBAC9B,IAAID,QAAQ,IAAIC,KAAK,EAAE;oBACrB,IAAI,CAACZ,GAAG,EAAE;wBACR,IAAIL,OAAO,CAACkB,UAAU,EAAE;4BACtBlB,OAAO,CAACmB,IAAI,EAAE,CAAC;yBAChB;wBACDd,GAAG,GAAGe,CAAAA,GAAAA,SAAiB,AAMrB,CAAA,kBANqB,CAAC,mDAAmD,EAAE;4BAC3EC,KAAK,EAAE,EAAE;4BACTJ,KAAK,EAAE,GAAG;4BACV,eAAe;4BACfK,QAAQ,EAAE,GAAG;4BACbC,UAAU,EAAE,GAAG;yBAChB,CAAC,CAAC;qBACJ,MAAM;wBACLlB,GAAG,CAAEmB,MAAM,CAACR,QAAQ,EAAEC,KAAK,CAAC,CAAC;qBAC9B;iBACF;aACF;SACF,CAAC,CAAC;QACH,OAAOP,UAAU,CAAC;KACnB,QAAS;QACRV,OAAO,CAACmB,IAAI,EAAE,CAAC;QACf,mBAAmB;QACnBd,GAAG,QAAW,GAAdA,KAAAA,CAAc,GAAdA,GAAG,CAAEoB,SAAS,EAAE,AA5IpB,CA4IqB;KAClB;CACF"}