{"version": 3, "sources": ["../../src/log.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nexport function time(label?: string): void {\n  console.time(label);\n}\n\nexport function timeEnd(label?: string): void {\n  console.timeEnd(label);\n}\n\nexport function error(...message: string[]): void {\n  console.error(...message);\n}\n\n/** Print an error and provide additional info (the stack trace) in debug mode. */\nexport function exception(e: Error): void {\n  const { env } = require('./utils/env');\n  error(chalk.red(e.toString()) + (env.EXPO_DEBUG ? '\\n' + chalk.gray(e.stack) : ''));\n}\n\nexport function warn(...message: string[]): void {\n  console.warn(...message.map((value) => chalk.yellow(value)));\n}\n\nexport function log(...message: string[]): void {\n  console.log(...message);\n}\n\n/** @deprecated use `debug` package with the `expo:` prefix instead.  */\nexport function debug(...message: any[]): void {\n  if (require('./utils/env').env.EXPO_DEBUG) console.log(...message);\n}\n\n/** Clear the terminal of all text. */\nexport function clear(): void {\n  process.stdout.write(process.platform === 'win32' ? '\\x1B[2J\\x1B[0f' : '\\x1B[2J\\x1B[3J\\x1B[H');\n}\n\n/** Log a message and exit the current process. If the `code` is non-zero then `console.error` will be used instead of `console.log`. */\nexport function exit(message: string | Error, code: number = 1): never {\n  if (message instanceof Error) {\n    exception(message);\n    process.exit(code);\n  }\n\n  if (message) {\n    if (code === 0) {\n      log(message);\n    } else {\n      error(message);\n    }\n  }\n\n  process.exit(code);\n}\n\n// The re-export makes auto importing easier.\nexport const Log = {\n  time,\n  timeEnd,\n  error,\n  exception,\n  warn,\n  log,\n  debug,\n  clear,\n  exit,\n};\n"], "names": ["time", "timeEnd", "error", "exception", "warn", "log", "debug", "clear", "exit", "label", "console", "message", "e", "env", "require", "chalk", "red", "toString", "EXPO_DEBUG", "gray", "stack", "map", "value", "yellow", "process", "stdout", "write", "platform", "code", "Error", "Log"], "mappings": "AAAA;;;;QAEgBA,IAAI,GAAJA,IAAI;QAIJC,OAAO,GAAPA,OAAO;QAIPC,KAAK,GAALA,KAAK;QAKLC,SAAS,GAATA,SAAS;QAKTC,IAAI,GAAJA,IAAI;QAIJC,GAAG,GAAHA,GAAG;QAKHC,KAAK,GAALA,KAAK;QAKLC,KAAK,GAALA,KAAK;QAKLC,IAAI,GAAJA,IAAI;;AAvCF,IAAA,MAAO,kCAAP,OAAO,EAAA;;;;;;AAElB,SAASR,IAAI,CAACS,KAAc,EAAQ;IACzCC,OAAO,CAACV,IAAI,CAACS,KAAK,CAAC,CAAC;CACrB;AAEM,SAASR,OAAO,CAACQ,KAAc,EAAQ;IAC5CC,OAAO,CAACT,OAAO,CAACQ,KAAK,CAAC,CAAC;CACxB;AAEM,SAASP,KAAK,CAAC,GAAGS,OAAO,AAAU,EAAQ;IAChDD,OAAO,CAACR,KAAK,IAAIS,OAAO,CAAC,CAAC;CAC3B;AAGM,SAASR,SAAS,CAACS,CAAQ,EAAQ;IACxC,MAAM,EAAEC,GAAG,CAAA,EAAE,GAAGC,OAAO,CAAC,aAAa,CAAC,AAAC;IACvCZ,KAAK,CAACa,MAAK,QAAA,CAACC,GAAG,CAACJ,CAAC,CAACK,QAAQ,EAAE,CAAC,GAAG,CAACJ,GAAG,CAACK,UAAU,GAAG,IAAI,GAAGH,MAAK,QAAA,CAACI,IAAI,CAACP,CAAC,CAACQ,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;CACrF;AAEM,SAAShB,IAAI,CAAC,GAAGO,OAAO,AAAU,EAAQ;IAC/CD,OAAO,CAACN,IAAI,IAAIO,OAAO,CAACU,GAAG,CAAC,CAACC,KAAK,GAAKP,MAAK,QAAA,CAACQ,MAAM,CAACD,KAAK,CAAC;IAAA,CAAC,CAAC,CAAC;CAC9D;AAEM,SAASjB,GAAG,CAAC,GAAGM,OAAO,AAAU,EAAQ;IAC9CD,OAAO,CAACL,GAAG,IAAIM,OAAO,CAAC,CAAC;CACzB;AAGM,SAASL,KAAK,CAAC,GAAGK,OAAO,AAAO,EAAQ;IAC7C,IAAIG,OAAO,CAAC,aAAa,CAAC,CAACD,GAAG,CAACK,UAAU,EAAER,OAAO,CAACL,GAAG,IAAIM,OAAO,CAAC,CAAC;CACpE;AAGM,SAASJ,KAAK,GAAS;IAC5BiB,OAAO,CAACC,MAAM,CAACC,KAAK,CAACF,OAAO,CAACG,QAAQ,KAAK,OAAO,GAAG,gBAAgB,GAAG,sBAAsB,CAAC,CAAC;CAChG;AAGM,SAASnB,IAAI,CAACG,OAAuB,EAAEiB,IAAY,GAAG,CAAC,EAAS;IACrE,IAAIjB,OAAO,YAAYkB,KAAK,EAAE;QAC5B1B,SAAS,CAACQ,OAAO,CAAC,CAAC;QACnBa,OAAO,CAAChB,IAAI,CAACoB,IAAI,CAAC,CAAC;KACpB;IAED,IAAIjB,OAAO,EAAE;QACX,IAAIiB,IAAI,KAAK,CAAC,EAAE;YACdvB,GAAG,CAACM,OAAO,CAAC,CAAC;SACd,MAAM;YACLT,KAAK,CAACS,OAAO,CAAC,CAAC;SAChB;KACF;IAEDa,OAAO,CAAChB,IAAI,CAACoB,IAAI,CAAC,CAAC;CACpB;AAGM,MAAME,GAAG,GAAG;IACjB9B,IAAI;IACJC,OAAO;IACPC,KAAK;IACLC,SAAS;IACTC,IAAI;IACJC,GAAG;IACHC,KAAK;IACLC,KAAK;IACLC,IAAI;CACL,AAAC;QAVWsB,GAAG,GAAHA,GAAG"}