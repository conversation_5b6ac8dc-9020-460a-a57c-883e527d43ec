{"version": 3, "sources": ["../../../../../src/start/server/middleware/InterstitialPageMiddleware.ts"], "sourcesContent": ["import { getConfig, getNameFromConfig } from '@expo/config';\nimport { getRuntimeVersionNullableAsync } from '@expo/config-plugins/build/utils/Updates';\nimport { readFile } from 'fs/promises';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { disableResponseCache, ExpoMiddleware } from './ExpoMiddleware';\nimport {\n  assertMissingRuntimePlatform,\n  assertRuntimePlatform,\n  parsePlatformHeader,\n  resolvePlatformFromUserAgentHeader,\n  RuntimePlatform,\n} from './resolvePlatform';\nimport { ServerRequest, ServerResponse } from './server.types';\n\ntype ProjectVersion = {\n  type: 'sdk' | 'runtime';\n  version: string | null;\n};\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:interstitialPage'\n) as typeof console.log;\n\nexport const LoadingEndpoint = '/_expo/loading';\n\nexport class InterstitialPageMiddleware extends ExpoMiddleware {\n  constructor(\n    projectRoot: string,\n    protected options: { scheme: string | null } = { scheme: null }\n  ) {\n    super(projectRoot, [LoadingEndpoint]);\n  }\n\n  /** Get the template HTML page and inject values. */\n  async _getPageAsync({\n    appName,\n    projectVersion,\n  }: {\n    appName: string;\n    projectVersion: ProjectVersion;\n  }): Promise<string> {\n    const templatePath =\n      // Production: This will resolve when installed in the project.\n      resolveFrom.silent(this.projectRoot, 'expo/static/loading-page/index.html') ??\n      // Development: This will resolve when testing locally.\n      path.resolve(__dirname, '../../../../../static/loading-page/index.html');\n    let content = (await readFile(templatePath)).toString('utf-8');\n\n    content = content.replace(/{{\\s*AppName\\s*}}/, appName);\n    content = content.replace(/{{\\s*Path\\s*}}/, this.projectRoot);\n    content = content.replace(/{{\\s*Scheme\\s*}}/, this.options.scheme ?? 'Unknown');\n    content = content.replace(\n      /{{\\s*ProjectVersionType\\s*}}/,\n      `${projectVersion.type === 'sdk' ? 'SDK' : 'Runtime'} version`\n    );\n    content = content.replace(/{{\\s*ProjectVersion\\s*}}/, projectVersion.version ?? 'Undetected');\n\n    return content;\n  }\n\n  /** Get settings for the page from the project config. */\n  async _getProjectOptionsAsync(platform: RuntimePlatform): Promise<{\n    appName: string;\n    projectVersion: ProjectVersion;\n  }> {\n    assertRuntimePlatform(platform);\n\n    const { exp } = getConfig(this.projectRoot);\n    const { appName } = getNameFromConfig(exp);\n    const runtimeVersion = await getRuntimeVersionNullableAsync(this.projectRoot, exp, platform);\n    const sdkVersion = exp.sdkVersion ?? null;\n\n    return {\n      appName: appName ?? 'App',\n      projectVersion:\n        sdkVersion && !runtimeVersion\n          ? { type: 'sdk', version: sdkVersion }\n          : { type: 'runtime', version: runtimeVersion },\n    };\n  }\n\n  async handleRequestAsync(req: ServerRequest, res: ServerResponse): Promise<void> {\n    res = disableResponseCache(res);\n    res.setHeader('Content-Type', 'text/html');\n\n    const platform = parsePlatformHeader(req) ?? resolvePlatformFromUserAgentHeader(req);\n    assertMissingRuntimePlatform(platform);\n    assertRuntimePlatform(platform);\n\n    const { appName, projectVersion } = await this._getProjectOptionsAsync(platform);\n    debug(\n      `Create loading page. (platform: ${platform}, appName: ${appName}, projectVersion: ${projectVersion.version}, type: ${projectVersion.type})`\n    );\n    const content = await this._getPageAsync({ appName, projectVersion });\n    res.end(content);\n  }\n}\n"], "names": ["debug", "require", "LoadingEndpoint", "InterstitialPageMiddleware", "ExpoMiddleware", "constructor", "projectRoot", "options", "scheme", "_getPageAsync", "appName", "projectVersion", "resolveFrom", "templatePath", "silent", "path", "resolve", "__dirname", "content", "readFile", "toString", "replace", "type", "version", "_getProjectOptionsAsync", "platform", "assertRuntimePlatform", "exp", "getConfig", "getNameFromConfig", "runtimeVersion", "getRuntimeVersionNullableAsync", "sdkVersion", "handleRequestAsync", "req", "res", "disableResponseCache", "<PERSON><PERSON><PERSON><PERSON>", "parsePlatformHeader", "resolvePlatformFromUserAgentHeader", "assertMissingRuntimePlatform", "end"], "mappings": "AAAA;;;;;AAA6C,IAAA,OAAc,WAAd,cAAc,CAAA;AACZ,IAAA,QAA0C,WAA1C,0CAA0C,CAAA;AAChE,IAAA,SAAa,WAAb,aAAa,CAAA;AACrB,IAAA,KAAM,kCAAN,MAAM,EAAA;AACC,IAAA,YAAc,kCAAd,cAAc,EAAA;AAEe,IAAA,eAAkB,WAAlB,kBAAkB,CAAA;AAOhE,IAAA,gBAAmB,WAAnB,mBAAmB,CAAA;;;;;;AAQ1B,MAAMA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,+CAA+C,CAChD,AAAsB,AAAC;AAEjB,MAAMC,eAAe,GAAG,gBAAgB,AAAC;QAAnCA,eAAe,GAAfA,eAAe;AAErB,MAAMC,0BAA0B,SAASC,eAAc,eAAA;IAC5DC,YACEC,WAAmB,EACTC,OAAkC,GAAG;QAAEC,MAAM,EAAE,IAAI;KAAE,CAC/D;QACA,KAAK,CAACF,WAAW,EAAE;YAACJ,eAAe;SAAC,CAAC,CAAC;aAF5BK,OAAkC,GAAlCA,OAAkC;KAG7C;IAED,oDAAoD,CACpD,MAAME,aAAa,CAAC,EAClBC,OAAO,CAAA,EACPC,cAAc,CAAA,EAIf,EAAmB;YAEhB,+DAA+D;QAC/DC,GAA2E;QAF7E,MAAMC,YAAY,GAEhBD,CAAAA,GAA2E,GAA3EA,YAAW,QAAA,CAACE,MAAM,CAAC,IAAI,CAACR,WAAW,EAAE,qCAAqC,CAAC,YAA3EM,GAA2E,GAC3E,uDAAuD;QACvDG,KAAI,QAAA,CAACC,OAAO,CAACC,SAAS,EAAE,+CAA+C,CAAC,AAAC;QAC3E,IAAIC,OAAO,GAAG,CAAC,MAAMC,CAAAA,GAAAA,SAAQ,AAAc,CAAA,SAAd,CAACN,YAAY,CAAC,CAAC,CAACO,QAAQ,CAAC,OAAO,CAAC,AAAC;QAE/DF,OAAO,GAAGA,OAAO,CAACG,OAAO,sBAAsBX,OAAO,CAAC,CAAC;QACxDQ,OAAO,GAAGA,OAAO,CAACG,OAAO,mBAAmB,IAAI,CAACf,WAAW,CAAC,CAAC;YAChB,OAAmB;QAAjEY,OAAO,GAAGA,OAAO,CAACG,OAAO,qBAAqB,CAAA,OAAmB,GAAnB,IAAI,CAACd,OAAO,CAACC,MAAM,YAAnB,OAAmB,GAAI,SAAS,CAAC,CAAC;QAChFU,OAAO,GAAGA,OAAO,CAACG,OAAO,iCAEvB,CAAC,EAAEV,cAAc,CAACW,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,CAC/D,CAAC;YACoDX,QAAsB;QAA5EO,OAAO,GAAGA,OAAO,CAACG,OAAO,6BAA6BV,CAAAA,QAAsB,GAAtBA,cAAc,CAACY,OAAO,YAAtBZ,QAAsB,GAAI,YAAY,CAAC,CAAC;QAE9F,OAAOO,OAAO,CAAC;KAChB;IAED,yDAAyD,CACzD,MAAMM,uBAAuB,CAACC,QAAyB,EAGpD;QACDC,CAAAA,GAAAA,gBAAqB,AAAU,CAAA,sBAAV,CAACD,QAAQ,CAAC,CAAC;QAEhC,MAAM,EAAEE,GAAG,CAAA,EAAE,GAAGC,CAAAA,GAAAA,OAAS,AAAkB,CAAA,UAAlB,CAAC,IAAI,CAACtB,WAAW,CAAC,AAAC;QAC5C,MAAM,EAAEI,OAAO,CAAA,EAAE,GAAGmB,CAAAA,GAAAA,OAAiB,AAAK,CAAA,kBAAL,CAACF,GAAG,CAAC,AAAC;QAC3C,MAAMG,cAAc,GAAG,MAAMC,CAAAA,GAAAA,QAA8B,AAAiC,CAAA,+BAAjC,CAAC,IAAI,CAACzB,WAAW,EAAEqB,GAAG,EAAEF,QAAQ,CAAC,AAAC;YAC1EE,WAAc;QAAjC,MAAMK,UAAU,GAAGL,CAAAA,WAAc,GAAdA,GAAG,CAACK,UAAU,YAAdL,WAAc,GAAI,IAAI,AAAC;QAE1C,OAAO;YACLjB,OAAO,EAAEA,OAAO,WAAPA,OAAO,GAAI,KAAK;YACzBC,cAAc,EACZqB,UAAU,IAAI,CAACF,cAAc,GACzB;gBAAER,IAAI,EAAE,KAAK;gBAAEC,OAAO,EAAES,UAAU;aAAE,GACpC;gBAAEV,IAAI,EAAE,SAAS;gBAAEC,OAAO,EAAEO,cAAc;aAAE;SACnD,CAAC;KACH;IAED,MAAMG,kBAAkB,CAACC,GAAkB,EAAEC,GAAmB,EAAiB;QAC/EA,GAAG,GAAGC,CAAAA,GAAAA,eAAoB,AAAK,CAAA,qBAAL,CAACD,GAAG,CAAC,CAAC;QAChCA,GAAG,CAACE,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;YAE1BC,GAAwB;QAAzC,MAAMb,QAAQ,GAAGa,CAAAA,GAAwB,GAAxBA,CAAAA,GAAAA,gBAAmB,AAAK,CAAA,oBAAL,CAACJ,GAAG,CAAC,YAAxBI,GAAwB,GAAIC,CAAAA,GAAAA,gBAAkC,AAAK,CAAA,mCAAL,CAACL,GAAG,CAAC,AAAC;QACrFM,CAAAA,GAAAA,gBAA4B,AAAU,CAAA,6BAAV,CAACf,QAAQ,CAAC,CAAC;QACvCC,CAAAA,GAAAA,gBAAqB,AAAU,CAAA,sBAAV,CAACD,QAAQ,CAAC,CAAC;QAEhC,MAAM,EAAEf,OAAO,CAAA,EAAEC,cAAc,CAAA,EAAE,GAAG,MAAM,IAAI,CAACa,uBAAuB,CAACC,QAAQ,CAAC,AAAC;QACjFzB,KAAK,CACH,CAAC,gCAAgC,EAAEyB,QAAQ,CAAC,WAAW,EAAEf,OAAO,CAAC,kBAAkB,EAAEC,cAAc,CAACY,OAAO,CAAC,QAAQ,EAAEZ,cAAc,CAACW,IAAI,CAAC,CAAC,CAAC,CAC7I,CAAC;QACF,MAAMJ,OAAO,GAAG,MAAM,IAAI,CAACT,aAAa,CAAC;YAAEC,OAAO;YAAEC,cAAc;SAAE,CAAC,AAAC;QACtEwB,GAAG,CAACM,GAAG,CAACvB,OAAO,CAAC,CAAC;KAClB;CACF;QAvEYf,0BAA0B,GAA1BA,0BAA0B"}