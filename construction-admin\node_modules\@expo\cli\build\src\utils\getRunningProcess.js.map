{"version": 3, "sources": ["../../../src/utils/getRunningProcess.ts"], "sourcesContent": ["import { execFileSync, execSync, ExecSyncOptionsWithStringEncoding } from 'child_process';\nimport * as path from 'path';\n\nconst debug = require('debug')('expo:utils:getRunningProcess') as typeof console.log;\n\nconst defaultOptions: ExecSyncOptionsWithStringEncoding = {\n  encoding: 'utf8',\n  stdio: ['pipe', 'pipe', 'ignore'],\n};\n\n/** Returns a pid value for a running port like `63828` or null if nothing is running on the given port. */\nexport function getPID(port: number): number | null {\n  try {\n    const results = execFileSync('lsof', [`-i:${port}`, '-P', '-t', '-sTCP:LISTEN'], defaultOptions)\n      .split('\\n')[0]\n      .trim();\n    const pid = Number(results);\n    debug(`pid: ${pid} for port: ${port}`);\n    return pid;\n  } catch (error: any) {\n    debug(`No pid found for port: ${port}. Error: ${error}`);\n    return null;\n  }\n}\n\n/** Get `package.json` `name` field for a given directory. Returns `null` if none exist. */\nfunction getPackageName(packageRoot: string): string | null {\n  const packageJson = path.join(packageRoot, 'package.json');\n  try {\n    return require(packageJson).name || null;\n  } catch {\n    return null;\n  }\n}\n\n/** Returns a command like `node /Users/<USER>/.../bin/expo start` or the package.json name. */\nfunction getProcessCommand(pid: number, procDirectory: string): string {\n  const name = getPackageName(procDirectory);\n\n  if (name) {\n    return name;\n  }\n  return execSync(`ps -o command -p ${pid} | sed -n 2p`, defaultOptions).replace(/\\n$/, '').trim();\n}\n\n/** Get directory for a given process ID. */\nexport function getDirectoryOfProcessById(processId: number): string {\n  return execSync(\n    `lsof -p ${processId} | awk '$4==\"cwd\" {for (i=9; i<=NF; i++) printf \"%s \", $i}'`,\n    defaultOptions\n  ).trim();\n}\n\n/** Get information about a running process given a port. Returns null if no process is running on the given port. */\nexport function getRunningProcess(port: number): {\n  /** The PID value for the port. */\n  pid: number;\n  /** Get the directory for the running process. */\n  directory: string;\n  /** The command running the process like `node /Users/<USER>/.../bin/expo start` or the `package.json` name like `my-app`. */\n  command: string;\n} | null {\n  // 63828\n  const pid = getPID(port);\n  if (!pid) {\n    return null;\n  }\n\n  try {\n    // /Users/<USER>/Documents/GitHub/lab/myapp\n    const directory = getDirectoryOfProcessById(pid);\n    // /Users/<USER>/Documents/GitHub/lab/myapp/package.json\n    const command = getProcessCommand(pid, directory);\n    // TODO: Have a better message for reusing another process.\n    return { pid, directory, command };\n  } catch {\n    return null;\n  }\n}\n"], "names": ["getPID", "getDirectoryOfProcessById", "getRunningProcess", "path", "debug", "require", "defaultOptions", "encoding", "stdio", "port", "results", "execFileSync", "split", "trim", "pid", "Number", "error", "getPackageName", "packageRoot", "packageJson", "join", "name", "getProcessCommand", "procDirectory", "execSync", "replace", "processId", "directory", "command"], "mappings": "AAAA;;;;QAWgBA,MAAM,GAANA,MAAM;QAmCNC,yBAAyB,GAAzBA,yBAAyB;QAQzBC,iBAAiB,GAAjBA,iBAAiB;AAtDyC,IAAA,aAAe,WAAf,eAAe,CAAA;AAC7EC,IAAAA,IAAI,mCAAM,MAAM,EAAZ;;;;;;;;;;;;;;;;;;;;;;AAEhB,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,8BAA8B,CAAC,AAAsB,AAAC;AAErF,MAAMC,cAAc,GAAsC;IACxDC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE;QAAC,MAAM;QAAE,MAAM;QAAE,QAAQ;KAAC;CAClC,AAAC;AAGK,SAASR,MAAM,CAACS,IAAY,EAAiB;IAClD,IAAI;QACF,MAAMC,OAAO,GAAGC,CAAAA,GAAAA,aAAY,AAAoE,CAAA,aAApE,CAAC,MAAM,EAAE;YAAC,CAAC,GAAG,EAAEF,IAAI,CAAC,CAAC;YAAE,IAAI;YAAE,IAAI;YAAE,cAAc;SAAC,EAAEH,cAAc,CAAC,CAC7FM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CACdC,IAAI,EAAE,AAAC;QACV,MAAMC,GAAG,GAAGC,MAAM,CAACL,OAAO,CAAC,AAAC;QAC5BN,KAAK,CAAC,CAAC,KAAK,EAAEU,GAAG,CAAC,WAAW,EAAEL,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,OAAOK,GAAG,CAAC;KACZ,CAAC,OAAOE,KAAK,EAAO;QACnBZ,KAAK,CAAC,CAAC,uBAAuB,EAAEK,IAAI,CAAC,SAAS,EAAEO,KAAK,CAAC,CAAC,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC;KACb;CACF;AAED,2FAA2F,CAC3F,SAASC,cAAc,CAACC,WAAmB,EAAiB;IAC1D,MAAMC,WAAW,GAAGhB,IAAI,CAACiB,IAAI,CAACF,WAAW,EAAE,cAAc,CAAC,AAAC;IAC3D,IAAI;QACF,OAAOb,OAAO,CAACc,WAAW,CAAC,CAACE,IAAI,IAAI,IAAI,CAAC;KAC1C,CAAC,OAAM;QACN,OAAO,IAAI,CAAC;KACb;CACF;AAED,kGAAkG,CAClG,SAASC,iBAAiB,CAACR,GAAW,EAAES,aAAqB,EAAU;IACrE,MAAMF,IAAI,GAAGJ,cAAc,CAACM,aAAa,CAAC,AAAC;IAE3C,IAAIF,IAAI,EAAE;QACR,OAAOA,IAAI,CAAC;KACb;IACD,OAAOG,CAAAA,GAAAA,aAAQ,AAAuD,CAAA,SAAvD,CAAC,CAAC,iBAAiB,EAAEV,GAAG,CAAC,YAAY,CAAC,EAAER,cAAc,CAAC,CAACmB,OAAO,QAAQ,EAAE,CAAC,CAACZ,IAAI,EAAE,CAAC;CAClG;AAGM,SAASZ,yBAAyB,CAACyB,SAAiB,EAAU;IACnE,OAAOF,CAAAA,GAAAA,aAAQ,AAGd,CAAA,SAHc,CACb,CAAC,QAAQ,EAAEE,SAAS,CAAC,2DAA2D,CAAC,EACjFpB,cAAc,CACf,CAACO,IAAI,EAAE,CAAC;CACV;AAGM,SAASX,iBAAiB,CAACO,IAAY,EAOrC;IACP,QAAQ;IACR,MAAMK,GAAG,GAAGd,MAAM,CAACS,IAAI,CAAC,AAAC;IACzB,IAAI,CAACK,GAAG,EAAE;QACR,OAAO,IAAI,CAAC;KACb;IAED,IAAI;QACF,8CAA8C;QAC9C,MAAMa,SAAS,GAAG1B,yBAAyB,CAACa,GAAG,CAAC,AAAC;QACjD,2DAA2D;QAC3D,MAAMc,OAAO,GAAGN,iBAAiB,CAACR,GAAG,EAAEa,SAAS,CAAC,AAAC;QAClD,2DAA2D;QAC3D,OAAO;YAAEb,GAAG;YAAEa,SAAS;YAAEC,OAAO;SAAE,CAAC;KACpC,CAAC,OAAM;QACN,OAAO,IAAI,CAAC;KACb;CACF"}