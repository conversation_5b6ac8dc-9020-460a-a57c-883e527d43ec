<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture\Event;

use PHPUnit\Framework\TestCase;

final class RiskyBecauseGlobalStateModificationTest extends TestCase
{
    public function testOne(): void
    {
        $GLOBALS['variable'] = 'value';
    }
}
