{"version": 3, "sources": ["../../../../../src/start/server/metro/runServer-fork.ts"], "sourcesContent": ["// Copyright © 2023 650 Industries.\n// Copyright (c) Meta Platforms, Inc. and affiliates.\n//\n// Forks https://github.com/facebook/metro/blob/b80d9a0f638ee9fb82ff69cd3c8d9f4309ca1da2/packages/metro/src/index.flow.js#L57\n// and adds the ability to access the bundler instance.\nimport assert from 'assert';\nimport http from 'http';\nimport https from 'https';\nimport Metro, { RunServerOptions, Server } from 'metro';\nimport MetroHmrServer from 'metro/src/HmrServer';\nimport createWebsocketServer from 'metro/src/lib/createWebsocketServer';\nimport { ConfigT } from 'metro-config';\nimport { parse } from 'url';\n\nimport { MetroBundlerDevServer } from './MetroBundlerDevServer';\nimport { Log } from '../../../log';\nimport { getRunningProcess } from '../../../utils/getRunningProcess';\nimport type { ConnectAppType } from '../middleware/server.types';\n\nexport const runServer = async (\n  metroBundler: MetroBundlerDevServer,\n  config: ConfigT,\n  {\n    hasReducedPerformance = false,\n    host,\n    onError,\n    onReady,\n    secureServerOptions,\n    waitForBundler = false,\n    websocketEndpoints = {},\n    watch,\n  }: RunServerOptions\n): Promise<{ server: http.Server | https.Server; metro: Server }> => {\n  // await earlyPortCheck(host, config.server.port);\n\n  // if (secure != null || secureCert != null || secureKey != null) {\n  //   // eslint-disable-next-line no-console\n  //   console.warn(\n  //     chalk.inverse.yellow.bold(' DEPRECATED '),\n  //     'The `secure`, `secureCert`, and `secureKey` options are now deprecated. ' +\n  //       'Please use the `secureServerOptions` object instead to pass options to ' +\n  //       \"Metro's https development server.\",\n  //   );\n  // }\n\n  const { middleware, end, metroServer } = await Metro.createConnectMiddleware(config, {\n    hasReducedPerformance,\n    waitForBundler,\n    watch,\n  });\n\n  assert(typeof (middleware as any).use === 'function');\n  const serverApp = middleware as ConnectAppType;\n\n  let httpServer: http.Server | https.Server;\n\n  if (secureServerOptions != null) {\n    httpServer = https.createServer(secureServerOptions, serverApp);\n  } else {\n    httpServer = http.createServer(serverApp);\n  }\n  return new Promise<{ server: http.Server | https.Server; metro: Server }>((resolve, reject) => {\n    httpServer.on('error', (error) => {\n      if ('code' in error && error.code === 'EADDRINUSE') {\n        // If `Error: listen EADDRINUSE: address already in use :::8081` then print additional info\n        // about the process before throwing.\n        const info = getRunningProcess(config.server.port);\n        if (info) {\n          Log.error(\n            `Port ${config.server.port} is busy running ${info.command} in: ${info.directory}`\n          );\n        }\n      }\n\n      if (onError) {\n        onError(error);\n      }\n      reject(error);\n      end();\n    });\n\n    httpServer.listen(config.server.port, host, () => {\n      if (onReady) {\n        onReady(httpServer);\n      }\n\n      Object.assign(websocketEndpoints, {\n        // @ts-expect-error: incorrect types\n        '/hot': createWebsocketServer({\n          websocketServer: new MetroHmrServer(\n            metroServer.getBundler(),\n            metroServer.getCreateModuleId(),\n            config\n          ),\n        }),\n      });\n\n      httpServer.on('upgrade', (request, socket, head) => {\n        const { pathname } = parse(request.url!);\n        if (pathname != null && websocketEndpoints[pathname]) {\n          websocketEndpoints[pathname].handleUpgrade(request, socket, head, (ws) => {\n            websocketEndpoints[pathname].emit('connection', ws, request);\n          });\n        } else {\n          socket.destroy();\n        }\n      });\n\n      resolve({ server: httpServer, metro: metroServer });\n    });\n\n    // Disable any kind of automatic timeout behavior for incoming\n    // requests in case it takes the packager more than the default\n    // timeout of 120 seconds to respond to a request.\n    httpServer.timeout = 0;\n\n    httpServer.on('close', () => {\n      end();\n    });\n  });\n};\n"], "names": ["runServer", "metroBundler", "config", "hasReducedPerformance", "host", "onError", "onReady", "secureServerOptions", "waitFor<PERSON><PERSON>ler", "websocketEndpoints", "watch", "middleware", "end", "metroServer", "Metro", "createConnectMiddleware", "assert", "use", "serverApp", "httpServer", "https", "createServer", "http", "Promise", "resolve", "reject", "on", "error", "code", "info", "getRunningProcess", "server", "port", "Log", "command", "directory", "listen", "Object", "assign", "createWebsocketServer", "websocketServer", "MetroHmrServer", "getBundler", "getCreateModuleId", "request", "socket", "head", "pathname", "parse", "url", "handleUpgrade", "ws", "emit", "destroy", "metro", "timeout"], "mappings": "AAKA;;;;;AAAmB,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACV,IAAA,KAAM,kCAAN,MAAM,EAAA;AACL,IAAA,MAAO,kCAAP,OAAO,EAAA;AACuB,IAAA,MAAO,kCAAP,OAAO,EAAA;AAC5B,IAAA,UAAqB,kCAArB,qBAAqB,EAAA;AACd,IAAA,sBAAqC,kCAArC,qCAAqC,EAAA;AAEjD,IAAA,IAAK,WAAL,KAAK,CAAA;AAGP,IAAA,IAAc,WAAd,cAAc,CAAA;AACA,IAAA,kBAAkC,WAAlC,kCAAkC,CAAA;;;;;;AAG7D,MAAMA,SAAS,GAAG,OACvBC,YAAmC,EACnCC,MAAe,EACf,EACEC,qBAAqB,EAAG,KAAK,CAAA,EAC7BC,IAAI,CAAA,EACJC,OAAO,CAAA,EACPC,OAAO,CAAA,EACPC,mBAAmB,CAAA,EACnBC,cAAc,EAAG,KAAK,CAAA,EACtBC,kBAAkB,EAAG,EAAE,CAAA,EACvBC,KAAK,CAAA,EACY,GACgD;IACnE,kDAAkD;IAElD,mEAAmE;IACnE,2CAA2C;IAC3C,kBAAkB;IAClB,iDAAiD;IACjD,mFAAmF;IACnF,oFAAoF;IACpF,6CAA6C;IAC7C,OAAO;IACP,IAAI;IAEJ,MAAM,EAAEC,UAAU,CAAA,EAAEC,GAAG,CAAA,EAAEC,WAAW,CAAA,EAAE,GAAG,MAAMC,MAAK,QAAA,CAACC,uBAAuB,CAACb,MAAM,EAAE;QACnFC,qBAAqB;QACrBK,cAAc;QACdE,KAAK;KACN,CAAC,AAAC;IAEHM,CAAAA,GAAAA,OAAM,AAA+C,CAAA,QAA/C,CAAC,OAAO,AAACL,UAAU,CAASM,GAAG,KAAK,UAAU,CAAC,CAAC;IACtD,MAAMC,SAAS,GAAGP,UAAU,AAAkB,AAAC;IAE/C,IAAIQ,UAAU,AAA4B,AAAC;IAE3C,IAAIZ,mBAAmB,IAAI,IAAI,EAAE;QAC/BY,UAAU,GAAGC,MAAK,QAAA,CAACC,YAAY,CAACd,mBAAmB,EAAEW,SAAS,CAAC,CAAC;KACjE,MAAM;QACLC,UAAU,GAAGG,KAAI,QAAA,CAACD,YAAY,CAACH,SAAS,CAAC,CAAC;KAC3C;IACD,OAAO,IAAIK,OAAO,CAAwD,CAACC,OAAO,EAAEC,MAAM,GAAK;QAC7FN,UAAU,CAACO,EAAE,CAAC,OAAO,EAAE,CAACC,KAAK,GAAK;YAChC,IAAI,MAAM,IAAIA,KAAK,IAAIA,KAAK,CAACC,IAAI,KAAK,YAAY,EAAE;gBAClD,2FAA2F;gBAC3F,qCAAqC;gBACrC,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,kBAAiB,AAAoB,CAAA,kBAApB,CAAC5B,MAAM,CAAC6B,MAAM,CAACC,IAAI,CAAC,AAAC;gBACnD,IAAIH,IAAI,EAAE;oBACRI,IAAG,IAAA,CAACN,KAAK,CACP,CAAC,KAAK,EAAEzB,MAAM,CAAC6B,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAEH,IAAI,CAACK,OAAO,CAAC,KAAK,EAAEL,IAAI,CAACM,SAAS,CAAC,CAAC,CACnF,CAAC;iBACH;aACF;YAED,IAAI9B,OAAO,EAAE;gBACXA,OAAO,CAACsB,KAAK,CAAC,CAAC;aAChB;YACDF,MAAM,CAACE,KAAK,CAAC,CAAC;YACdf,GAAG,EAAE,CAAC;SACP,CAAC,CAAC;QAEHO,UAAU,CAACiB,MAAM,CAAClC,MAAM,CAAC6B,MAAM,CAACC,IAAI,EAAE5B,IAAI,EAAE,IAAM;YAChD,IAAIE,OAAO,EAAE;gBACXA,OAAO,CAACa,UAAU,CAAC,CAAC;aACrB;YAEDkB,MAAM,CAACC,MAAM,CAAC7B,kBAAkB,EAAE;gBAChC,oCAAoC;gBACpC,MAAM,EAAE8B,CAAAA,GAAAA,sBAAqB,AAM3B,CAAA,QAN2B,CAAC;oBAC5BC,eAAe,EAAE,IAAIC,UAAc,QAAA,CACjC5B,WAAW,CAAC6B,UAAU,EAAE,EACxB7B,WAAW,CAAC8B,iBAAiB,EAAE,EAC/BzC,MAAM,CACP;iBACF,CAAC;aACH,CAAC,CAAC;YAEHiB,UAAU,CAACO,EAAE,CAAC,SAAS,EAAE,CAACkB,OAAO,EAAEC,MAAM,EAAEC,IAAI,GAAK;gBAClD,MAAM,EAAEC,QAAQ,CAAA,EAAE,GAAGC,CAAAA,GAAAA,IAAK,AAAc,CAAA,MAAd,CAACJ,OAAO,CAACK,GAAG,CAAE,AAAC;gBACzC,IAAIF,QAAQ,IAAI,IAAI,IAAItC,kBAAkB,CAACsC,QAAQ,CAAC,EAAE;oBACpDtC,kBAAkB,CAACsC,QAAQ,CAAC,CAACG,aAAa,CAACN,OAAO,EAAEC,MAAM,EAAEC,IAAI,EAAE,CAACK,EAAE,GAAK;wBACxE1C,kBAAkB,CAACsC,QAAQ,CAAC,CAACK,IAAI,CAAC,YAAY,EAAED,EAAE,EAAEP,OAAO,CAAC,CAAC;qBAC9D,CAAC,CAAC;iBACJ,MAAM;oBACLC,MAAM,CAACQ,OAAO,EAAE,CAAC;iBAClB;aACF,CAAC,CAAC;YAEH7B,OAAO,CAAC;gBAAEO,MAAM,EAAEZ,UAAU;gBAAEmC,KAAK,EAAEzC,WAAW;aAAE,CAAC,CAAC;SACrD,CAAC,CAAC;QAEH,8DAA8D;QAC9D,+DAA+D;QAC/D,kDAAkD;QAClDM,UAAU,CAACoC,OAAO,GAAG,CAAC,CAAC;QAEvBpC,UAAU,CAACO,EAAE,CAAC,OAAO,EAAE,IAAM;YAC3Bd,GAAG,EAAE,CAAC;SACP,CAAC,CAAC;KACJ,CAAC,CAAC;CACJ,AAAC;QArGWZ,SAAS,GAATA,SAAS"}