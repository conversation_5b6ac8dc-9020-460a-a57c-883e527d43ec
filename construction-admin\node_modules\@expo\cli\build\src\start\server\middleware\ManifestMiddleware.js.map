{"version": 3, "sources": ["../../../../../src/start/server/middleware/ManifestMiddleware.ts"], "sourcesContent": ["import {\n  ExpoConfig,\n  ExpoGoConfig,\n  getConfig,\n  PackageJSONConfig,\n  ProjectConfig,\n} from '@expo/config';\nimport { resolveEntryPoint } from '@expo/config/paths';\nimport findWorkspaceRoot from 'find-yarn-workspace-root';\nimport path from 'path';\nimport { resolve } from 'url';\n\nimport { ExpoMiddleware } from './ExpoMiddleware';\nimport {\n  shouldEnableAsyncImports,\n  createBundleUrlPath,\n  getBaseUrlFromExpoConfig,\n  getAsyncRoutesFromExpoConfig,\n  createBundleUrlPathFromExpoConfig,\n} from './metroOptions';\nimport { resolveGoogleServicesFile, resolveManifestAssets } from './resolveAssets';\nimport { parsePlatformHeader, RuntimePlatform } from './resolvePlatform';\nimport { ServerHeaders, ServerNext, ServerRequest, ServerResponse } from './server.types';\nimport { isEnableHermesManaged } from '../../../export/exportHermes';\nimport * as Log from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { stripExtension } from '../../../utils/url';\nimport * as ProjectDevices from '../../project/devices';\nimport { UrlCreator } from '../UrlCreator';\nimport { getRouterDirectoryModuleIdWithManifest } from '../metro/router';\nimport { getPlatformBundlers, PlatformBundlers } from '../platformBundlers';\nimport { createTemplateHtmlFromExpoConfigAsync } from '../webTemplate';\n\nconst debug = require('debug')('expo:start:server:middleware:manifest') as typeof console.log;\n\n/** Wraps `findWorkspaceRoot` and guards against having an empty `package.json` file in an upper directory. */\nexport function getWorkspaceRoot(projectRoot: string): string | null {\n  try {\n    return findWorkspaceRoot(projectRoot);\n  } catch (error: any) {\n    if (error.message.includes('Unexpected end of JSON input')) {\n      return null;\n    }\n    throw error;\n  }\n}\n\nconst supportedPlatforms = ['ios', 'android', 'web', 'none'];\n\nexport function getEntryWithServerRoot(\n  projectRoot: string,\n  props: { platform: string; pkg?: PackageJSONConfig }\n) {\n  if (!supportedPlatforms.includes(props.platform)) {\n    throw new CommandError(\n      `Failed to resolve the project's entry file: The platform \"${props.platform}\" is not supported.`\n    );\n  }\n  return path.relative(getMetroServerRoot(projectRoot), resolveEntryPoint(projectRoot, props));\n}\n\nexport function getMetroServerRoot(projectRoot: string) {\n  if (env.EXPO_USE_METRO_WORKSPACE_ROOT) {\n    return getWorkspaceRoot(projectRoot) ?? projectRoot;\n  }\n\n  return projectRoot;\n}\n\n/** Get the main entry module ID (file) relative to the project root. */\nexport function resolveMainModuleName(\n  projectRoot: string,\n  props: { platform: string; pkg?: PackageJSONConfig }\n): string {\n  const entryPoint = getEntryWithServerRoot(projectRoot, props);\n\n  debug(`Resolved entry point: ${entryPoint} (project root: ${projectRoot})`);\n\n  return stripExtension(entryPoint, 'js');\n}\n\n/** Info about the computer hosting the dev server. */\nexport interface HostInfo {\n  host: string;\n  server: 'expo';\n  serverVersion: string;\n  serverDriver: string | null;\n  serverOS: NodeJS.Platform;\n  serverOSVersion: string;\n}\n\n/** Parsed values from the supported request headers. */\nexport interface ManifestRequestInfo {\n  /** Platform to serve. */\n  platform: RuntimePlatform;\n  /** Requested host name. */\n  hostname?: string | null;\n  /** The protocol used to request the manifest */\n  protocol?: 'http' | 'https';\n}\n\n/** Project related info. */\nexport type ResponseProjectSettings = {\n  expoGoConfig: ExpoGoConfig;\n  hostUri: string;\n  bundleUrl: string;\n  exp: ExpoConfig;\n};\n\nexport const DEVELOPER_TOOL = 'expo-cli';\n\nexport type ManifestMiddlewareOptions = {\n  /** Should start the dev servers in development mode (minify). */\n  mode?: 'development' | 'production';\n  /** Should instruct the bundler to create minified bundles. */\n  minify?: boolean;\n  constructUrl: UrlCreator['constructUrl'];\n  isNativeWebpack?: boolean;\n  privateKeyPath?: string;\n};\n\n/** Base middleware creator for serving the Expo manifest (like the index.html but for native runtimes). */\nexport abstract class ManifestMiddleware<\n  TManifestRequestInfo extends ManifestRequestInfo,\n> extends ExpoMiddleware {\n  private initialProjectConfig: ProjectConfig;\n  private platformBundlers: PlatformBundlers;\n\n  constructor(\n    protected projectRoot: string,\n    protected options: ManifestMiddlewareOptions\n  ) {\n    super(\n      projectRoot,\n      /**\n       * Only support `/`, `/manifest`, `/index.exp` for the manifest middleware.\n       */\n      ['/', '/manifest', '/index.exp']\n    );\n    this.initialProjectConfig = getConfig(projectRoot);\n    this.platformBundlers = getPlatformBundlers(projectRoot, this.initialProjectConfig.exp);\n  }\n\n  /** Exposed for testing. */\n  public async _resolveProjectSettingsAsync({\n    platform,\n    hostname,\n    protocol,\n  }: Pick<\n    TManifestRequestInfo,\n    'hostname' | 'platform' | 'protocol'\n  >): Promise<ResponseProjectSettings> {\n    // Read the config\n    const projectConfig = getConfig(this.projectRoot);\n\n    // Read from headers\n    const mainModuleName = this.resolveMainModuleName({\n      pkg: projectConfig.pkg,\n      platform,\n    });\n\n    const isHermesEnabled = isEnableHermesManaged(projectConfig.exp, platform);\n\n    // Create the manifest and set fields within it\n    const expoGoConfig = this.getExpoGoConfig({\n      mainModuleName,\n      hostname,\n    });\n\n    const hostUri = this.options.constructUrl({ scheme: '', hostname });\n\n    const bundleUrl = this._getBundleUrl({\n      platform,\n      mainModuleName,\n      hostname,\n      engine: isHermesEnabled ? 'hermes' : undefined,\n      baseUrl: getBaseUrlFromExpoConfig(projectConfig.exp),\n      asyncRoutes: getAsyncRoutesFromExpoConfig(\n        projectConfig.exp,\n        this.options.mode ?? 'development',\n        platform\n      ),\n      routerRoot: getRouterDirectoryModuleIdWithManifest(this.projectRoot, projectConfig.exp),\n      protocol,\n    });\n\n    // Resolve all assets and set them on the manifest as URLs\n    await this.mutateManifestWithAssetsAsync(projectConfig.exp, bundleUrl);\n\n    return {\n      expoGoConfig,\n      hostUri,\n      bundleUrl,\n      exp: projectConfig.exp,\n    };\n  }\n\n  /** Get the main entry module ID (file) relative to the project root. */\n  private resolveMainModuleName(props: { pkg: PackageJSONConfig; platform: string }): string {\n    let entryPoint = getEntryWithServerRoot(this.projectRoot, props);\n\n    debug(`Resolved entry point: ${entryPoint} (project root: ${this.projectRoot})`);\n\n    // NOTE(Bacon): Webpack is currently hardcoded to index.bundle on native\n    // in the future (TODO) we should move this logic into a Webpack plugin and use\n    // a generated file name like we do on web.\n    // const server = getDefaultDevServer();\n    // // TODO: Move this into BundlerDevServer and read this info from self.\n    // const isNativeWebpack = server instanceof WebpackBundlerDevServer && server.isTargetingNative();\n    if (this.options.isNativeWebpack) {\n      entryPoint = 'index.js';\n    }\n\n    return stripExtension(entryPoint, 'js');\n  }\n\n  /** Parse request headers into options. */\n  public abstract getParsedHeaders(req: ServerRequest): TManifestRequestInfo;\n\n  /** Store device IDs that were sent in the request headers. */\n  private async saveDevicesAsync(req: ServerRequest) {\n    const deviceIds = req.headers?.['expo-dev-client-id'];\n    if (deviceIds) {\n      await ProjectDevices.saveDevicesAsync(this.projectRoot, deviceIds).catch((e) =>\n        Log.exception(e)\n      );\n    }\n  }\n\n  /** Create the bundle URL (points to the single JS entry file). Exposed for testing. */\n  public _getBundleUrl({\n    platform,\n    mainModuleName,\n    hostname,\n    engine,\n    baseUrl,\n    isExporting,\n    asyncRoutes,\n    routerRoot,\n    protocol,\n  }: {\n    platform: string;\n    hostname?: string | null;\n    mainModuleName: string;\n    engine?: 'hermes';\n    baseUrl?: string;\n    asyncRoutes: boolean;\n    isExporting?: boolean;\n    routerRoot: string;\n    protocol?: 'http' | 'https';\n  }): string {\n    const path = createBundleUrlPath({\n      mode: this.options.mode ?? 'development',\n      minify: this.options.minify,\n      platform,\n      mainModuleName,\n      lazy: shouldEnableAsyncImports(this.projectRoot),\n      engine,\n      bytecode: engine === 'hermes',\n      baseUrl,\n      isExporting: !!isExporting,\n      asyncRoutes,\n      routerRoot,\n    });\n\n    return (\n      this.options.constructUrl({\n        scheme: protocol ?? 'http',\n        // hostType: this.options.location.hostType,\n        hostname,\n      }) + path\n    );\n  }\n\n  /** Log telemetry. */\n  protected abstract trackManifest(version?: string): void;\n\n  /** Get the manifest response to return to the runtime. This file contains info regarding where the assets can be loaded from. Exposed for testing. */\n  public abstract _getManifestResponseAsync(options: TManifestRequestInfo): Promise<{\n    body: string;\n    version: string;\n    headers: ServerHeaders;\n  }>;\n\n  private getExpoGoConfig({\n    mainModuleName,\n    hostname,\n  }: {\n    mainModuleName: string;\n    hostname?: string | null;\n  }): ExpoGoConfig {\n    return {\n      // localhost:8081\n      debuggerHost: this.options.constructUrl({ scheme: '', hostname }),\n      // Required for Expo Go to function.\n      developer: {\n        tool: DEVELOPER_TOOL,\n        projectRoot: this.projectRoot,\n      },\n      packagerOpts: {\n        // Required for dev client.\n        dev: this.options.mode !== 'production',\n      },\n      // Indicates the name of the main bundle.\n      mainModuleName,\n      // Add this string to make Flipper register React Native / Metro as \"running\".\n      // Can be tested by running:\n      // `METRO_SERVER_PORT=8081 open -a flipper.app`\n      // Where 8081 is the port where the Expo project is being hosted.\n      __flipperHack: 'React Native packager is running',\n    };\n  }\n\n  /** Resolve all assets and set them on the manifest as URLs */\n  private async mutateManifestWithAssetsAsync(manifest: ExpoConfig, bundleUrl: string) {\n    await resolveManifestAssets(this.projectRoot, {\n      manifest,\n      resolver: async (path) => {\n        if (this.options.isNativeWebpack) {\n          // When using our custom dev server, just do assets normally\n          // without the `assets/` subpath redirect.\n          return resolve(bundleUrl!.match(/^https?:\\/\\/.*?\\//)![0], path);\n        }\n        return bundleUrl!.match(/^https?:\\/\\/.*?\\//)![0] + 'assets/' + path;\n      },\n    });\n    // The server normally inserts this but if we're offline we'll do it here\n    await resolveGoogleServicesFile(this.projectRoot, manifest);\n  }\n\n  public getWebBundleUrl() {\n    const platform = 'web';\n    // Read from headers\n    const mainModuleName = this.resolveMainModuleName({\n      pkg: this.initialProjectConfig.pkg,\n      platform,\n    });\n\n    return createBundleUrlPathFromExpoConfig(this.projectRoot, this.initialProjectConfig.exp, {\n      platform,\n      mainModuleName,\n      minify: this.options.minify,\n      lazy: shouldEnableAsyncImports(this.projectRoot),\n      mode: this.options.mode ?? 'development',\n      // Hermes doesn't support more modern JS features than most, if not all, modern browser.\n      engine: 'hermes',\n      isExporting: false,\n      bytecode: false,\n    });\n  }\n\n  /**\n   * Web platforms should create an index.html response using the same script resolution as native.\n   *\n   * Instead of adding a `bundleUrl` to a `manifest.json` (native) we'll add a `<script src=\"\">`\n   * to an `index.html`, this enables the web platform to load JavaScript from the server.\n   */\n  private async handleWebRequestAsync(req: ServerRequest, res: ServerResponse) {\n    // Read from headers\n    const bundleUrl = this.getWebBundleUrl();\n\n    res.setHeader('Content-Type', 'text/html');\n\n    res.end(\n      await createTemplateHtmlFromExpoConfigAsync(this.projectRoot, {\n        exp: this.initialProjectConfig.exp,\n        scripts: [bundleUrl],\n      })\n    );\n  }\n\n  /** Exposed for testing. */\n  async checkBrowserRequestAsync(req: ServerRequest, res: ServerResponse, next: ServerNext) {\n    if (\n      this.platformBundlers.web === 'metro' &&\n      this.initialProjectConfig.exp.platforms?.includes('web')\n    ) {\n      // NOTE(EvanBacon): This effectively disables the safety check we do on custom runtimes to ensure\n      // the `expo-platform` header is included. When `web.bundler=web`, if the user has non-standard Expo\n      // code loading then they'll get a web bundle without a clear assertion of platform support.\n      const platform = parsePlatformHeader(req);\n      // On web, serve the public folder\n      if (!platform || platform === 'web') {\n        if (['static', 'server'].includes(this.initialProjectConfig.exp.web?.output ?? '')) {\n          // Skip the spa-styled index.html when static generation is enabled.\n          next();\n          return true;\n        } else {\n          await this.handleWebRequestAsync(req, res);\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n\n  async handleRequestAsync(\n    req: ServerRequest,\n    res: ServerResponse,\n    next: ServerNext\n  ): Promise<void> {\n    // First check for standard JavaScript runtimes (aka legacy browsers like Chrome).\n    if (await this.checkBrowserRequestAsync(req, res, next)) {\n      return;\n    }\n\n    // Save device IDs for dev client.\n    await this.saveDevicesAsync(req);\n\n    // Read from headers\n    const options = this.getParsedHeaders(req);\n    const { body, version, headers } = await this._getManifestResponseAsync(options);\n    for (const [headerName, headerValue] of headers) {\n      res.setHeader(headerName, headerValue);\n    }\n    res.end(body);\n\n    // Log analytics\n    this.trackManifest(version ?? null);\n  }\n}\n"], "names": ["getWorkspaceRoot", "getEntryWithServerRoot", "getMetroServerRoot", "resolveMainModuleName", "Log", "ProjectDevices", "debug", "require", "projectRoot", "findWorkspaceRoot", "error", "message", "includes", "supportedPlatforms", "props", "platform", "CommandError", "path", "relative", "resolveEntryPoint", "env", "EXPO_USE_METRO_WORKSPACE_ROOT", "entryPoint", "stripExtension", "DEVELOPER_TOOL", "ManifestMiddleware", "ExpoMiddleware", "constructor", "options", "initialProjectConfig", "getConfig", "platformBundlers", "getPlatformBundlers", "exp", "_resolveProjectSettingsAsync", "hostname", "protocol", "projectConfig", "mainModuleName", "pkg", "isHermesEnabled", "isEnableHermesManaged", "expoGoConfig", "getExpoGoConfig", "hostUri", "constructUrl", "scheme", "bundleUrl", "_getBundleUrl", "engine", "undefined", "baseUrl", "getBaseUrlFromExpoConfig", "asyncRoutes", "getAsyncRoutesFromExpoConfig", "mode", "routerRoot", "getRouterDirectoryModuleIdWithManifest", "mutateManifestWithAssetsAsync", "isNativeWebpack", "saveDevicesAsync", "req", "deviceIds", "headers", "catch", "e", "exception", "isExporting", "createBundleUrlPath", "minify", "lazy", "shouldEnableAsyncImports", "bytecode", "debuggerHost", "developer", "tool", "packagerOpts", "dev", "__flipperHack", "manifest", "resolveManifestAssets", "resolver", "resolve", "match", "resolveGoogleServicesFile", "getWebBundleUrl", "createBundleUrlPathFromExpoConfig", "handleWebRequestAsync", "res", "<PERSON><PERSON><PERSON><PERSON>", "end", "createTemplateHtmlFromExpoConfigAsync", "scripts", "checkBrowserRequestAsync", "next", "web", "platforms", "parsePlatformHeader", "output", "handleRequestAsync", "getParsedHeaders", "body", "version", "_getManifestResponseAsync", "headerName", "headerValue", "trackManifest"], "mappings": "AAAA;;;;QAqCgBA,gBAAgB,GAAhBA,gBAAgB;QAahBC,sBAAsB,GAAtBA,sBAAsB;QAYtBC,kBAAkB,GAAlBA,kBAAkB;QASlBC,qBAAqB,GAArBA,qBAAqB;;AAjE9B,IAAA,OAAc,WAAd,cAAc,CAAA;AACa,IAAA,MAAoB,WAApB,oBAAoB,CAAA;AACxB,IAAA,sBAA0B,kCAA1B,0BAA0B,EAAA;AACvC,IAAA,KAAM,kCAAN,MAAM,EAAA;AACC,IAAA,IAAK,WAAL,KAAK,CAAA;AAEE,IAAA,eAAkB,WAAlB,kBAAkB,CAAA;AAO1C,IAAA,aAAgB,WAAhB,gBAAgB,CAAA;AAC0C,IAAA,cAAiB,WAAjB,iBAAiB,CAAA;AAC7B,IAAA,gBAAmB,WAAnB,mBAAmB,CAAA;AAElC,IAAA,aAA8B,WAA9B,8BAA8B,CAAA;AACxDC,IAAAA,GAAG,mCAAM,cAAc,EAApB;AACK,IAAA,IAAoB,WAApB,oBAAoB,CAAA;AACX,IAAA,OAAuB,WAAvB,uBAAuB,CAAA;AACrB,IAAA,KAAoB,WAApB,oBAAoB,CAAA;AACvCC,IAAAA,cAAc,mCAAM,uBAAuB,EAA7B;AAE6B,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AAClB,IAAA,iBAAqB,WAArB,qBAAqB,CAAA;AACrB,IAAA,YAAgB,WAAhB,gBAAgB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,uCAAuC,CAAC,AAAsB,AAAC;AAGvF,SAASP,gBAAgB,CAACQ,WAAmB,EAAiB;IACnE,IAAI;QACF,OAAOC,CAAAA,GAAAA,sBAAiB,AAAa,CAAA,QAAb,CAACD,WAAW,CAAC,CAAC;KACvC,CAAC,OAAOE,KAAK,EAAO;QACnB,IAAIA,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,8BAA8B,CAAC,EAAE;YAC1D,OAAO,IAAI,CAAC;SACb;QACD,MAAMF,KAAK,CAAC;KACb;CACF;AAED,MAAMG,kBAAkB,GAAG;IAAC,KAAK;IAAE,SAAS;IAAE,KAAK;IAAE,MAAM;CAAC,AAAC;AAEtD,SAASZ,sBAAsB,CACpCO,WAAmB,EACnBM,KAAoD,EACpD;IACA,IAAI,CAACD,kBAAkB,CAACD,QAAQ,CAACE,KAAK,CAACC,QAAQ,CAAC,EAAE;QAChD,MAAM,IAAIC,OAAY,aAAA,CACpB,CAAC,0DAA0D,EAAEF,KAAK,CAACC,QAAQ,CAAC,mBAAmB,CAAC,CACjG,CAAC;KACH;IACD,OAAOE,KAAI,QAAA,CAACC,QAAQ,CAAChB,kBAAkB,CAACM,WAAW,CAAC,EAAEW,CAAAA,GAAAA,MAAiB,AAAoB,CAAA,kBAApB,CAACX,WAAW,EAAEM,KAAK,CAAC,CAAC,CAAC;CAC9F;AAEM,SAASZ,kBAAkB,CAACM,WAAmB,EAAE;IACtD,IAAIY,IAAG,IAAA,CAACC,6BAA6B,EAAE;YAC9BrB,GAA6B;QAApC,OAAOA,CAAAA,GAA6B,GAA7BA,gBAAgB,CAACQ,WAAW,CAAC,YAA7BR,GAA6B,GAAIQ,WAAW,CAAC;KACrD;IAED,OAAOA,WAAW,CAAC;CACpB;AAGM,SAASL,qBAAqB,CACnCK,WAAmB,EACnBM,KAAoD,EAC5C;IACR,MAAMQ,UAAU,GAAGrB,sBAAsB,CAACO,WAAW,EAAEM,KAAK,CAAC,AAAC;IAE9DR,KAAK,CAAC,CAAC,sBAAsB,EAAEgB,UAAU,CAAC,gBAAgB,EAAEd,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAE5E,OAAOe,CAAAA,GAAAA,KAAc,AAAkB,CAAA,eAAlB,CAACD,UAAU,EAAE,IAAI,CAAC,CAAC;CACzC;AA8BM,MAAME,cAAc,GAAG,UAAU,AAAC;QAA5BA,cAAc,GAAdA,cAAc;AAapB,MAAeC,kBAAkB,SAE9BC,eAAc,eAAA;IAItBC,YACYnB,WAAmB,EACnBoB,OAAkC,CAC5C;QACA,KAAK,CACHpB,WAAW,EACX;;SAEG,CACH;YAAC,GAAG;YAAE,WAAW;YAAE,YAAY;SAAC,CACjC,CAAC;aATQA,WAAmB,GAAnBA,WAAmB;aACnBoB,OAAkC,GAAlCA,OAAkC;QAS5C,IAAI,CAACC,oBAAoB,GAAGC,CAAAA,GAAAA,OAAS,AAAa,CAAA,UAAb,CAACtB,WAAW,CAAC,CAAC;QACnD,IAAI,CAACuB,gBAAgB,GAAGC,CAAAA,GAAAA,iBAAmB,AAA4C,CAAA,oBAA5C,CAACxB,WAAW,EAAE,IAAI,CAACqB,oBAAoB,CAACI,GAAG,CAAC,CAAC;KACzF;IAED,2BAA2B,CAC3B,MAAaC,4BAA4B,CAAC,EACxCnB,QAAQ,CAAA,EACRoB,QAAQ,CAAA,EACRC,QAAQ,CAAA,EAIT,EAAoC;QACnC,kBAAkB;QAClB,MAAMC,aAAa,GAAGP,CAAAA,GAAAA,OAAS,AAAkB,CAAA,UAAlB,CAAC,IAAI,CAACtB,WAAW,CAAC,AAAC;QAElD,oBAAoB;QACpB,MAAM8B,cAAc,GAAG,IAAI,CAACnC,qBAAqB,CAAC;YAChDoC,GAAG,EAAEF,aAAa,CAACE,GAAG;YACtBxB,QAAQ;SACT,CAAC,AAAC;QAEH,MAAMyB,eAAe,GAAGC,CAAAA,GAAAA,aAAqB,AAA6B,CAAA,sBAA7B,CAACJ,aAAa,CAACJ,GAAG,EAAElB,QAAQ,CAAC,AAAC;QAE3E,+CAA+C;QAC/C,MAAM2B,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC;YACxCL,cAAc;YACdH,QAAQ;SACT,CAAC,AAAC;QAEH,MAAMS,OAAO,GAAG,IAAI,CAAChB,OAAO,CAACiB,YAAY,CAAC;YAAEC,MAAM,EAAE,EAAE;YAAEX,QAAQ;SAAE,CAAC,AAAC;YAUhE,KAAiB;QARrB,MAAMY,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC;YACnCjC,QAAQ;YACRuB,cAAc;YACdH,QAAQ;YACRc,MAAM,EAAET,eAAe,GAAG,QAAQ,GAAGU,SAAS;YAC9CC,OAAO,EAAEC,CAAAA,GAAAA,aAAwB,AAAmB,CAAA,yBAAnB,CAACf,aAAa,CAACJ,GAAG,CAAC;YACpDoB,WAAW,EAAEC,CAAAA,GAAAA,aAA4B,AAIxC,CAAA,6BAJwC,CACvCjB,aAAa,CAACJ,GAAG,EACjB,CAAA,KAAiB,GAAjB,IAAI,CAACL,OAAO,CAAC2B,IAAI,YAAjB,KAAiB,GAAI,aAAa,EAClCxC,QAAQ,CACT;YACDyC,UAAU,EAAEC,CAAAA,GAAAA,OAAsC,AAAqC,CAAA,uCAArC,CAAC,IAAI,CAACjD,WAAW,EAAE6B,aAAa,CAACJ,GAAG,CAAC;YACvFG,QAAQ;SACT,CAAC,AAAC;QAEH,0DAA0D;QAC1D,MAAM,IAAI,CAACsB,6BAA6B,CAACrB,aAAa,CAACJ,GAAG,EAAEc,SAAS,CAAC,CAAC;QAEvE,OAAO;YACLL,YAAY;YACZE,OAAO;YACPG,SAAS;YACTd,GAAG,EAAEI,aAAa,CAACJ,GAAG;SACvB,CAAC;KACH;IAED,wEAAwE,CACxE,AAAQ9B,qBAAqB,CAACW,KAAmD,EAAU;QACzF,IAAIQ,UAAU,GAAGrB,sBAAsB,CAAC,IAAI,CAACO,WAAW,EAAEM,KAAK,CAAC,AAAC;QAEjER,KAAK,CAAC,CAAC,sBAAsB,EAAEgB,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAACd,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjF,wEAAwE;QACxE,+EAA+E;QAC/E,2CAA2C;QAC3C,wCAAwC;QACxC,yEAAyE;QACzE,mGAAmG;QACnG,IAAI,IAAI,CAACoB,OAAO,CAAC+B,eAAe,EAAE;YAChCrC,UAAU,GAAG,UAAU,CAAC;SACzB;QAED,OAAOC,CAAAA,GAAAA,KAAc,AAAkB,CAAA,eAAlB,CAACD,UAAU,EAAE,IAAI,CAAC,CAAC;KACzC;IAKD,8DAA8D,CAC9D,MAAcsC,gBAAgB,CAACC,GAAkB,EAAE;YAC/BA,GAAW;QAA7B,MAAMC,SAAS,GAAGD,CAAAA,GAAW,GAAXA,GAAG,CAACE,OAAO,SAAwB,GAAnCF,KAAAA,CAAmC,GAAnCA,GAAW,AAAE,CAAC,oBAAoB,CAAC,AAAC;QACtD,IAAIC,SAAS,EAAE;YACb,MAAMzD,cAAc,CAACuD,gBAAgB,CAAC,IAAI,CAACpD,WAAW,EAAEsD,SAAS,CAAC,CAACE,KAAK,CAAC,CAACC,CAAC,GACzE7D,GAAG,CAAC8D,SAAS,CAACD,CAAC,CAAC;YAAA,CACjB,CAAC;SACH;KACF;IAED,uFAAuF,CACvF,AAAOjB,aAAa,CAAC,EACnBjC,QAAQ,CAAA,EACRuB,cAAc,CAAA,EACdH,QAAQ,CAAA,EACRc,MAAM,CAAA,EACNE,OAAO,CAAA,EACPgB,WAAW,CAAA,EACXd,WAAW,CAAA,EACXG,UAAU,CAAA,EACVpB,QAAQ,CAAA,EAWT,EAAU;YAED,KAAiB;QADzB,MAAMnB,IAAI,GAAGmD,CAAAA,GAAAA,aAAmB,AAY9B,CAAA,oBAZ8B,CAAC;YAC/Bb,IAAI,EAAE,CAAA,KAAiB,GAAjB,IAAI,CAAC3B,OAAO,CAAC2B,IAAI,YAAjB,KAAiB,GAAI,aAAa;YACxCc,MAAM,EAAE,IAAI,CAACzC,OAAO,CAACyC,MAAM;YAC3BtD,QAAQ;YACRuB,cAAc;YACdgC,IAAI,EAAEC,CAAAA,GAAAA,aAAwB,AAAkB,CAAA,yBAAlB,CAAC,IAAI,CAAC/D,WAAW,CAAC;YAChDyC,MAAM;YACNuB,QAAQ,EAAEvB,MAAM,KAAK,QAAQ;YAC7BE,OAAO;YACPgB,WAAW,EAAE,CAAC,CAACA,WAAW;YAC1Bd,WAAW;YACXG,UAAU;SACX,CAAC,AAAC;QAEH,OACE,IAAI,CAAC5B,OAAO,CAACiB,YAAY,CAAC;YACxBC,MAAM,EAAEV,QAAQ,WAARA,QAAQ,GAAI,MAAM;YAC1B,4CAA4C;YAC5CD,QAAQ;SACT,CAAC,GAAGlB,IAAI,CACT;KACH;IAYD,AAAQ0B,eAAe,CAAC,EACtBL,cAAc,CAAA,EACdH,QAAQ,CAAA,EAIT,EAAgB;QACf,OAAO;YACL,iBAAiB;YACjBsC,YAAY,EAAE,IAAI,CAAC7C,OAAO,CAACiB,YAAY,CAAC;gBAAEC,MAAM,EAAE,EAAE;gBAAEX,QAAQ;aAAE,CAAC;YACjE,oCAAoC;YACpCuC,SAAS,EAAE;gBACTC,IAAI,EAAEnD,cAAc;gBACpBhB,WAAW,EAAE,IAAI,CAACA,WAAW;aAC9B;YACDoE,YAAY,EAAE;gBACZ,2BAA2B;gBAC3BC,GAAG,EAAE,IAAI,CAACjD,OAAO,CAAC2B,IAAI,KAAK,YAAY;aACxC;YACD,yCAAyC;YACzCjB,cAAc;YACd,8EAA8E;YAC9E,4BAA4B;YAC5B,+CAA+C;YAC/C,iEAAiE;YACjEwC,aAAa,EAAE,kCAAkC;SAClD,CAAC;KACH;IAED,8DAA8D,CAC9D,MAAcpB,6BAA6B,CAACqB,QAAoB,EAAEhC,SAAiB,EAAE;QACnF,MAAMiC,CAAAA,GAAAA,cAAqB,AAUzB,CAAA,sBAVyB,CAAC,IAAI,CAACxE,WAAW,EAAE;YAC5CuE,QAAQ;YACRE,QAAQ,EAAE,OAAOhE,IAAI,GAAK;gBACxB,IAAI,IAAI,CAACW,OAAO,CAAC+B,eAAe,EAAE;oBAChC,4DAA4D;oBAC5D,0CAA0C;oBAC1C,OAAOuB,CAAAA,GAAAA,IAAO,AAAiD,CAAA,QAAjD,CAACnC,SAAS,CAAEoC,KAAK,qBAAqB,AAAC,CAAC,CAAC,CAAC,EAAElE,IAAI,CAAC,CAAC;iBACjE;gBACD,OAAO8B,SAAS,CAAEoC,KAAK,qBAAqB,AAAC,CAAC,CAAC,CAAC,GAAG,SAAS,GAAGlE,IAAI,CAAC;aACrE;SACF,CAAC,CAAC;QACH,yEAAyE;QACzE,MAAMmE,CAAAA,GAAAA,cAAyB,AAA4B,CAAA,0BAA5B,CAAC,IAAI,CAAC5E,WAAW,EAAEuE,QAAQ,CAAC,CAAC;KAC7D;IAED,AAAOM,eAAe,GAAG;QACvB,MAAMtE,QAAQ,GAAG,KAAK,AAAC;QACvB,oBAAoB;QACpB,MAAMuB,cAAc,GAAG,IAAI,CAACnC,qBAAqB,CAAC;YAChDoC,GAAG,EAAE,IAAI,CAACV,oBAAoB,CAACU,GAAG;YAClCxB,QAAQ;SACT,CAAC,AAAC;YAOK,KAAiB;QALzB,OAAOuE,CAAAA,GAAAA,aAAiC,AAUtC,CAAA,kCAVsC,CAAC,IAAI,CAAC9E,WAAW,EAAE,IAAI,CAACqB,oBAAoB,CAACI,GAAG,EAAE;YACxFlB,QAAQ;YACRuB,cAAc;YACd+B,MAAM,EAAE,IAAI,CAACzC,OAAO,CAACyC,MAAM;YAC3BC,IAAI,EAAEC,CAAAA,GAAAA,aAAwB,AAAkB,CAAA,yBAAlB,CAAC,IAAI,CAAC/D,WAAW,CAAC;YAChD+C,IAAI,EAAE,CAAA,KAAiB,GAAjB,IAAI,CAAC3B,OAAO,CAAC2B,IAAI,YAAjB,KAAiB,GAAI,aAAa;YACxC,wFAAwF;YACxFN,MAAM,EAAE,QAAQ;YAChBkB,WAAW,EAAE,KAAK;YAClBK,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;KACJ;IAED;;;;;KAKG,CACH,MAAce,qBAAqB,CAAC1B,GAAkB,EAAE2B,GAAmB,EAAE;QAC3E,oBAAoB;QACpB,MAAMzC,SAAS,GAAG,IAAI,CAACsC,eAAe,EAAE,AAAC;QAEzCG,GAAG,CAACC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE3CD,GAAG,CAACE,GAAG,CACL,MAAMC,CAAAA,GAAAA,YAAqC,AAGzC,CAAA,sCAHyC,CAAC,IAAI,CAACnF,WAAW,EAAE;YAC5DyB,GAAG,EAAE,IAAI,CAACJ,oBAAoB,CAACI,GAAG;YAClC2D,OAAO,EAAE;gBAAC7C,SAAS;aAAC;SACrB,CAAC,CACH,CAAC;KACH;IAED,2BAA2B,CAC3B,MAAM8C,wBAAwB,CAAChC,GAAkB,EAAE2B,GAAmB,EAAEM,IAAgB,EAAE;YAGtF,GAAuC;QAFzC,IACE,IAAI,CAAC/D,gBAAgB,CAACgE,GAAG,KAAK,OAAO,KACrC,CAAA,GAAuC,GAAvC,IAAI,CAAClE,oBAAoB,CAACI,GAAG,CAAC+D,SAAS,SAAU,GAAjD,KAAA,CAAiD,GAAjD,GAAuC,CAAEpF,QAAQ,CAAC,KAAK,CAAC,CAAA,EACxD;YACA,iGAAiG;YACjG,oGAAoG;YACpG,4FAA4F;YAC5F,MAAMG,QAAQ,GAAGkF,CAAAA,GAAAA,gBAAmB,AAAK,CAAA,oBAAL,CAACpC,GAAG,CAAC,AAAC;YAC1C,kCAAkC;YAClC,IAAI,CAAC9C,QAAQ,IAAIA,QAAQ,KAAK,KAAK,EAAE;oBACD,IAAiC;oBAAjC,IAAyC;gBAA3E,IAAI;oBAAC,QAAQ;oBAAE,QAAQ;iBAAC,CAACH,QAAQ,CAAC,CAAA,IAAyC,GAAzC,CAAA,IAAiC,GAAjC,IAAI,CAACiB,oBAAoB,CAACI,GAAG,CAAC8D,GAAG,SAAQ,GAAzC,KAAA,CAAyC,GAAzC,IAAiC,CAAEG,MAAM,YAAzC,IAAyC,GAAI,EAAE,CAAC,EAAE;oBAClF,oEAAoE;oBACpEJ,IAAI,EAAE,CAAC;oBACP,OAAO,IAAI,CAAC;iBACb,MAAM;oBACL,MAAM,IAAI,CAACP,qBAAqB,CAAC1B,GAAG,EAAE2B,GAAG,CAAC,CAAC;oBAC3C,OAAO,IAAI,CAAC;iBACb;aACF;SACF;QACD,OAAO,KAAK,CAAC;KACd;IAED,MAAMW,kBAAkB,CACtBtC,GAAkB,EAClB2B,GAAmB,EACnBM,IAAgB,EACD;QACf,kFAAkF;QAClF,IAAI,MAAM,IAAI,CAACD,wBAAwB,CAAChC,GAAG,EAAE2B,GAAG,EAAEM,IAAI,CAAC,EAAE;YACvD,OAAO;SACR;QAED,kCAAkC;QAClC,MAAM,IAAI,CAAClC,gBAAgB,CAACC,GAAG,CAAC,CAAC;QAEjC,oBAAoB;QACpB,MAAMjC,OAAO,GAAG,IAAI,CAACwE,gBAAgB,CAACvC,GAAG,CAAC,AAAC;QAC3C,MAAM,EAAEwC,IAAI,CAAA,EAAEC,OAAO,CAAA,EAAEvC,OAAO,CAAA,EAAE,GAAG,MAAM,IAAI,CAACwC,yBAAyB,CAAC3E,OAAO,CAAC,AAAC;QACjF,KAAK,MAAM,CAAC4E,UAAU,EAAEC,WAAW,CAAC,IAAI1C,OAAO,CAAE;YAC/CyB,GAAG,CAACC,SAAS,CAACe,UAAU,EAAEC,WAAW,CAAC,CAAC;SACxC;QACDjB,GAAG,CAACE,GAAG,CAACW,IAAI,CAAC,CAAC;QAEd,gBAAgB;QAChB,IAAI,CAACK,aAAa,CAACJ,OAAO,WAAPA,OAAO,GAAI,IAAI,CAAC,CAAC;KACrC;CACF;QA1SqB7E,kBAAkB,GAAlBA,kBAAkB"}