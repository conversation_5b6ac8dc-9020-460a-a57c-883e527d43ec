{"version": 3, "sources": ["../../../../src/utils/tsconfig/matchTsConfigPathAlias.ts"], "sourcesContent": ["// From TypeScript: https://github.com/microsoft/TypeScript/blob/5b1897969769449217237aecbe364f823096c63e/src/compiler/core.ts\n// License: https://github.com/microsoft/TypeScript/blob/214df64/LICENSE.txt\n\nexport interface Pattern {\n  prefix: string;\n  suffix: string;\n}\n\nconst asterisk = 0x2a;\n\nfunction hasZeroOrOneAsteriskCharacter(str: string): boolean {\n  let seenAsterisk = false;\n  for (let i = 0; i < str.length; i++) {\n    if (str.charCodeAt(i) === asterisk) {\n      if (!seenAsterisk) {\n        seenAsterisk = true;\n      } else {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\nfunction tryParsePattern(pattern: string): Pattern | undefined {\n  // This should be verified outside of here and a proper error thrown.\n  const indexOfStar = pattern.indexOf('*');\n  return indexOfStar === -1\n    ? undefined\n    : {\n        prefix: pattern.slice(0, indexOfStar),\n        suffix: pattern.slice(indexOfStar + 1),\n      };\n}\n\nfunction isPatternMatch({ prefix, suffix }: Pattern, candidate: string) {\n  return (\n    candidate.length >= prefix.length + suffix.length &&\n    candidate.startsWith(prefix) &&\n    candidate.endsWith(suffix)\n  );\n}\n\n/**\n * Return the object corresponding to the best pattern to match `candidate`.\n *\n * @internal\n */\nfunction findBestPatternMatch<T>(\n  values: readonly T[],\n  getPattern: (value: T) => Pattern,\n  candidate: string\n): T | undefined {\n  let matchedValue: T | undefined;\n  // use length of prefix as betterness criteria\n  let longestMatchPrefixLength = -1;\n\n  for (const v of values) {\n    const pattern = getPattern(v);\n    if (isPatternMatch(pattern, candidate) && pattern.prefix.length > longestMatchPrefixLength) {\n      longestMatchPrefixLength = pattern.prefix.length;\n      matchedValue = v;\n    }\n  }\n\n  return matchedValue;\n}\n\n/**\n * patternStrings contains both pattern strings (containing \"*\") and regular strings.\n * Return an exact match if possible, or a pattern match, or undefined.\n * (These are verified by verifyCompilerOptions to have 0 or 1 \"*\" characters.)\n */\nfunction matchPatternOrExact(\n  patternStrings: readonly string[],\n  candidate: string\n): string | Pattern | undefined {\n  const patterns: Pattern[] = [];\n  for (const patternString of patternStrings) {\n    if (!hasZeroOrOneAsteriskCharacter(patternString)) continue;\n    const pattern = tryParsePattern(patternString);\n    if (pattern) {\n      patterns.push(pattern);\n    } else if (patternString === candidate) {\n      // pattern was matched as is - no need to search further\n      return patternString;\n    }\n  }\n\n  return findBestPatternMatch(patterns, (_) => _, candidate);\n}\n\n/**\n * Given that candidate matches pattern, returns the text matching the '*'.\n * E.g.: matchedText(tryParsePattern(\"foo*baz\"), \"foobarbaz\") === \"bar\"\n */\nfunction matchedText(pattern: Pattern, candidate: string): string {\n  return candidate.substring(pattern.prefix.length, candidate.length - pattern.suffix.length);\n}\n\nfunction getStar(matchedPattern: string | Pattern, moduleName: string) {\n  return typeof matchedPattern === 'string' ? undefined : matchedText(matchedPattern, moduleName);\n}\n\nexport function matchTsConfigPathAlias(pathsKeys: string[], moduleName: string) {\n  // If the module name does not match any of the patterns in `paths` we hand off resolving to webpack\n  const matchedPattern = matchPatternOrExact(pathsKeys, moduleName);\n  if (!matchedPattern) {\n    return null;\n  }\n\n  return {\n    star: getStar(matchedPattern, moduleName),\n    text:\n      typeof matchedPattern === 'string'\n        ? matchedPattern\n        : `${matchedPattern.prefix}*${matchedPattern.suffix}`,\n  };\n}\n"], "names": ["matchTsConfigPathAlias", "asterisk", "hasZeroOrOneAsteriskCharacter", "str", "seenAsterisk", "i", "length", "charCodeAt", "tryParsePattern", "pattern", "indexOfStar", "indexOf", "undefined", "prefix", "slice", "suffix", "isPatternMatch", "candidate", "startsWith", "endsWith", "findBestPatternMatch", "values", "getPattern", "matchedValue", "longestMatchPrefixLength", "v", "matchPatternOrExact", "patternStrings", "patterns", "patternString", "push", "_", "matchedText", "substring", "getStar", "matchedPattern", "moduleName", "pathsKeys", "star", "text"], "mappings": "AAGA;;;;QAqGgBA,sBAAsB,GAAtBA,sBAAsB;AAhGtC,MAAMC,QAAQ,GAAG,EAAI,AAAC;AAEtB,SAASC,6BAA6B,CAACC,GAAW,EAAW;IAC3D,IAAIC,YAAY,GAAG,KAAK,AAAC;IACzB,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAED,CAAC,EAAE,CAAE;QACnC,IAAIF,GAAG,CAACI,UAAU,CAACF,CAAC,CAAC,KAAKJ,QAAQ,EAAE;YAClC,IAAI,CAACG,YAAY,EAAE;gBACjBA,YAAY,GAAG,IAAI,CAAC;aACrB,MAAM;gBACL,OAAO,KAAK,CAAC;aACd;SACF;KACF;IACD,OAAO,IAAI,CAAC;CACb;AAED,SAASI,eAAe,CAACC,OAAe,EAAuB;IAC7D,qEAAqE;IACrE,MAAMC,WAAW,GAAGD,OAAO,CAACE,OAAO,CAAC,GAAG,CAAC,AAAC;IACzC,OAAOD,WAAW,KAAK,CAAC,CAAC,GACrBE,SAAS,GACT;QACEC,MAAM,EAAEJ,OAAO,CAACK,KAAK,CAAC,CAAC,EAAEJ,WAAW,CAAC;QACrCK,MAAM,EAAEN,OAAO,CAACK,KAAK,CAACJ,WAAW,GAAG,CAAC,CAAC;KACvC,CAAC;CACP;AAED,SAASM,cAAc,CAAC,EAAEH,MAAM,CAAA,EAAEE,MAAM,CAAA,EAAW,EAAEE,SAAiB,EAAE;IACtE,OACEA,SAAS,CAACX,MAAM,IAAIO,MAAM,CAACP,MAAM,GAAGS,MAAM,CAACT,MAAM,IACjDW,SAAS,CAACC,UAAU,CAACL,MAAM,CAAC,IAC5BI,SAAS,CAACE,QAAQ,CAACJ,MAAM,CAAC,CAC1B;CACH;AAED;;;;GAIG,CACH,SAASK,oBAAoB,CAC3BC,MAAoB,EACpBC,UAAiC,EACjCL,SAAiB,EACF;IACf,IAAIM,YAAY,AAAe,AAAC;IAChC,8CAA8C;IAC9C,IAAIC,wBAAwB,GAAG,CAAC,CAAC,AAAC;IAElC,KAAK,MAAMC,CAAC,IAAIJ,MAAM,CAAE;QACtB,MAAMZ,OAAO,GAAGa,UAAU,CAACG,CAAC,CAAC,AAAC;QAC9B,IAAIT,cAAc,CAACP,OAAO,EAAEQ,SAAS,CAAC,IAAIR,OAAO,CAACI,MAAM,CAACP,MAAM,GAAGkB,wBAAwB,EAAE;YAC1FA,wBAAwB,GAAGf,OAAO,CAACI,MAAM,CAACP,MAAM,CAAC;YACjDiB,YAAY,GAAGE,CAAC,CAAC;SAClB;KACF;IAED,OAAOF,YAAY,CAAC;CACrB;AAED;;;;GAIG,CACH,SAASG,mBAAmB,CAC1BC,cAAiC,EACjCV,SAAiB,EACa;IAC9B,MAAMW,QAAQ,GAAc,EAAE,AAAC;IAC/B,KAAK,MAAMC,aAAa,IAAIF,cAAc,CAAE;QAC1C,IAAI,CAACzB,6BAA6B,CAAC2B,aAAa,CAAC,EAAE,SAAS;QAC5D,MAAMpB,OAAO,GAAGD,eAAe,CAACqB,aAAa,CAAC,AAAC;QAC/C,IAAIpB,OAAO,EAAE;YACXmB,QAAQ,CAACE,IAAI,CAACrB,OAAO,CAAC,CAAC;SACxB,MAAM,IAAIoB,aAAa,KAAKZ,SAAS,EAAE;YACtC,wDAAwD;YACxD,OAAOY,aAAa,CAAC;SACtB;KACF;IAED,OAAOT,oBAAoB,CAACQ,QAAQ,EAAE,CAACG,CAAC,GAAKA,CAAC;IAAA,EAAEd,SAAS,CAAC,CAAC;CAC5D;AAED;;;GAGG,CACH,SAASe,WAAW,CAACvB,OAAgB,EAAEQ,SAAiB,EAAU;IAChE,OAAOA,SAAS,CAACgB,SAAS,CAACxB,OAAO,CAACI,MAAM,CAACP,MAAM,EAAEW,SAAS,CAACX,MAAM,GAAGG,OAAO,CAACM,MAAM,CAACT,MAAM,CAAC,CAAC;CAC7F;AAED,SAAS4B,OAAO,CAACC,cAAgC,EAAEC,UAAkB,EAAE;IACrE,OAAO,OAAOD,cAAc,KAAK,QAAQ,GAAGvB,SAAS,GAAGoB,WAAW,CAACG,cAAc,EAAEC,UAAU,CAAC,CAAC;CACjG;AAEM,SAASpC,sBAAsB,CAACqC,SAAmB,EAAED,UAAkB,EAAE;IAC9E,oGAAoG;IACpG,MAAMD,cAAc,GAAGT,mBAAmB,CAACW,SAAS,EAAED,UAAU,CAAC,AAAC;IAClE,IAAI,CAACD,cAAc,EAAE;QACnB,OAAO,IAAI,CAAC;KACb;IAED,OAAO;QACLG,IAAI,EAAEJ,OAAO,CAACC,cAAc,EAAEC,UAAU,CAAC;QACzCG,IAAI,EACF,OAAOJ,cAAc,KAAK,QAAQ,GAC9BA,cAAc,GACd,CAAC,EAAEA,cAAc,CAACtB,MAAM,CAAC,CAAC,EAAEsB,cAAc,CAACpB,MAAM,CAAC,CAAC;KAC1D,CAAC;CACH"}