# 🏗️ Construction Company Web Platform – Final Project Documentation

## Overview

A comprehensive platform for a construction company featuring:
- A responsive corporate website
- A full-featured admin dashboard (web & mobile)
- A real-time content management API
- Cross-platform mobile application for administration

---

## 1. 🌐 Website (Frontend)

### Features
- Responsive layout (320px – 2560px)
- Modern, clean construction-themed design
- Core pages:
  - Home
  - About
  - Projects
  - Services
  - Media Gallery
  - Contact
- Performance-optimized image loading
- Smooth navigation, animations, and interaction

### Tech Stack
- HTML5, CSS3, JavaScript (ES6+)
- Blade templating (Laravel)
- Bootstrap 5 for layout
- Custom SCSS modules

---

## 2. ⚙️ Backend (API & CMS)

### Tech Stack
- **Framework:** Laravel 11 (PHP 8.2+)
- **Database:** MySQL 8.0+
- **Auth:** Laravel Sanctum (API token-based)
- **API Architecture:** RESTful (public + admin)
- **Security:** CSRF protection, hashed passwords, admin scopes

### CMS Features
- Project management (CRUD, status, featured toggle)
- Services catalog (CRUD)
- Media library (upload, categorize, delete)
- Contact form handler (store and view messages)
- User & role management
- Analytics dashboard (via API endpoints)

### Database Schema Highlights
- `projects` (id, title, slug, description, status, location, featured)
- `services` (id, name, description)
- `media` (id, file, type, associated project/service)
- `contacts` (id, name, email, message)
- `users` (role-based access)

---

## 3. 📱 Mobile App (Admin – React Native)

### Features
- Cross-platform: Android & iOS
- Admin login with secure token
- Dashboard overview
- Real-time content management:
  - Create/edit/delete projects
  - Manage services and media
  - View messages
- Image upload via Expo Image Picker
- Offline support (data caching)
- Push notifications (via FCM – planned)

### Tech Stack
- **React Native (Expo)**
- Axios for API
- React Navigation (v6+)
- Secure Storage (planned for auth tokens)
- Expo Notifications (planned)

### Folder Structure

```
/src
├── /api         # API logic and base config
├── /components  # Shared UI elements
├── /screens     # All app views
├── /navigation  # App stack navigator
└── /utils       # Constants, helpers
```

---

## 4. 🔌 API Endpoints Overview

| Method | Endpoint                     | Auth | Description                         |
|--------|------------------------------|------|-------------------------------------|
| POST   | `/api/login`                 | ❌   | Login & return token                |
| GET    | `/api/projects`              | ✅   | List projects                       |
| POST   | `/api/projects`              | ✅   | Create new project                  |
| PUT    | `/api/projects/{id}`         | ✅   | Update project                      |
| DELETE | `/api/projects/{id}`         | ✅   | Delete project                      |
| GET    | `/api/services`              | ✅   | List services                       |
| POST   | `/api/media`                 | ✅   | Upload media                        |
| GET    | `/api/contact`               | ✅   | List contact form submissions       |
| POST   | `/api/contact`               | ❌   | Public contact form submission      |

---

## 5. 📁 Deliverables

| # | Item                           | Status       |
|---|--------------------------------|--------------|
| ✅ | Responsive Website              | Completed    |
| ✅ | REST API Backend                | Completed    |
| ✅ | Admin Panel (Web)               | In Progress  |
| ✅ | Mobile Admin App (React Native) | In Progress  |
| ✅ | API Documentation               | Completed    |
| ✅ | Developer Docs / Setup Guide    | This file ✅  |
| ✅ | User Training Material          | To be added  |

---

## 6. 🧪 Setup Instructions

### Laravel Backend

```bash
git clone https://github.com/yourorg/construction-platform.git
cd construction-platform
composer install
cp .env.example .env
php artisan key:generate
php artisan migrate
php artisan serve
```

### React Native Mobile App

```bash
cd construction-admin
npm install
npx expo start
```

---

## 7. 📈 Future Enhancements

- Role-based access control for sub-admins
- Image optimization pipeline
- Admin analytics charts (visits, uploads, contact trends)
- Firebase push notifications
- Multilingual support

---

## 8. 👨‍🏫 Training Materials

Documentation to be provided for:
- Admin usage (Web & Mobile)
- Uploading images
- Managing content
- Interpreting analytics

---

## 🔗 Reference

- Design reference: [floriconstructionltd.com](https://floriconstructionltd.com)
- API format: RESTful with JSON responses
- Authentication: Token-based using Sanctum

---

## 👨‍💻 Maintained by

**Ermir**  
Lead Developer – Web & Mobile Applications  
[Your Company or GitHub Profile]