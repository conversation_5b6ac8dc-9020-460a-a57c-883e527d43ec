{"version": 3, "sources": ["../../../../src/start/server/AsyncNgrok.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport crypto from 'crypto';\nimport * as path from 'path';\nimport slugify from 'slugify';\n\nimport UserSettings from '../../api/user/UserSettings';\nimport { getActorDisplayName, getUserAsync } from '../../api/user/user';\nimport * as Log from '../../log';\nimport { delayAsync, resolveWithTimeout } from '../../utils/delay';\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { isNgrokClientError, NgrokInstance, NgrokResolver } from '../doctor/ngrok/NgrokResolver';\nimport { hasAdbReverseAsync, startAdbReverseAsync } from '../platforms/android/adbReverse';\nimport { ProjectSettings } from '../project/settings';\n\nconst debug = require('debug')('expo:start:server:ngrok') as typeof console.log;\n\nconst NGROK_CONFIG = {\n  authToken: '5W1bR67GNbWcXqmxZzBG1_56GezNeaX6sSRvn8npeQ8',\n  domain: 'exp.direct',\n};\n\nconst TUNNEL_TIMEOUT = 10 * 1000;\n\nexport class AsyncNgrok {\n  /** Resolves the best instance of ngrok, exposed for testing. */\n  resolver: NgrokResolver;\n\n  /** Info about the currently running instance of ngrok. */\n  private serverUrl: string | null = null;\n\n  constructor(\n    private projectRoot: string,\n    private port: number\n  ) {\n    this.resolver = new NgrokResolver(projectRoot);\n  }\n\n  public getActiveUrl(): string | null {\n    return this.serverUrl;\n  }\n\n  /** Exposed for testing. */\n  async _getIdentifyingUrlSegmentsAsync(): Promise<string[]> {\n    const user = await getUserAsync();\n    if (user?.__typename === 'Robot') {\n      throw new CommandError('NGROK_ROBOT', 'Cannot use ngrok with a robot user.');\n    }\n    const username = getActorDisplayName(user);\n\n    return [\n      // NOTE: https://github.com/expo/expo/pull/16556#discussion_r822944286\n      await this.getProjectRandomnessAsync(),\n      // Strip out periods from the username to avoid subdomain issues with SSL certificates.\n      slugify(username, { remove: /\\./ }),\n      // Use the port to distinguish between multiple tunnels (webpack, metro).\n      String(this.port),\n    ];\n  }\n\n  /** Exposed for testing. */\n  async _getProjectHostnameAsync(): Promise<string> {\n    return `${(await this._getIdentifyingUrlSegmentsAsync()).join('-')}.${NGROK_CONFIG.domain}`;\n  }\n\n  /** Exposed for testing. */\n  async _getProjectSubdomainAsync(): Promise<string> {\n    return (await this._getIdentifyingUrlSegmentsAsync()).join('-');\n  }\n\n  /** Start ngrok on the given port for the project. */\n  async startAsync({ timeout }: { timeout?: number } = {}): Promise<void> {\n    // Ensure the instance is loaded first, this can linger so we should run it before the timeout.\n    await this.resolver.resolveAsync({\n      // For now, prefer global install since the package has native code (harder to install) and doesn't change very often.\n      prefersGlobalInstall: true,\n    });\n\n    // NOTE(EvanBacon): If the user doesn't have ADB installed,\n    // then skip attempting to reverse the port.\n    if (hasAdbReverseAsync()) {\n      // Ensure ADB reverse is running.\n      if (!(await startAdbReverseAsync([this.port]))) {\n        // TODO: Better error message.\n        throw new CommandError(\n          'NGROK_ADB',\n          `Cannot start tunnel URL because \\`adb reverse\\` failed for the connected Android device(s).`\n        );\n      }\n    }\n\n    this.serverUrl = await this._connectToNgrokAsync({ timeout });\n\n    debug('Tunnel URL:', this.serverUrl);\n    Log.log('Tunnel ready.');\n  }\n\n  /** Stop the ngrok process if it's running. */\n  public async stopAsync(): Promise<void> {\n    debug('Stopping Tunnel');\n\n    await this.resolver.get()?.kill?.();\n    this.serverUrl = null;\n  }\n\n  /** Exposed for testing. */\n  async _connectToNgrokAsync(\n    options: { timeout?: number } = {},\n    attempts: number = 0\n  ): Promise<string> {\n    // Attempt to stop any hanging processes, this increases the chances of a successful connection.\n    await this.stopAsync();\n\n    // Get the instance quietly or assert otherwise.\n    const instance = await this.resolver.resolveAsync({\n      shouldPrompt: false,\n      autoInstall: false,\n    });\n\n    // TODO(Bacon): Consider dropping the timeout functionality:\n    // https://github.com/expo/expo/pull/16556#discussion_r822307373\n    const results = await resolveWithTimeout(\n      () => this.connectToNgrokInternalAsync(instance, attempts),\n      {\n        timeout: options.timeout ?? TUNNEL_TIMEOUT,\n        errorMessage: 'ngrok tunnel took too long to connect.',\n      }\n    );\n    if (typeof results === 'string') {\n      return results;\n    }\n\n    // Wait 100ms and then try again\n    await delayAsync(100);\n\n    return this._connectToNgrokAsync(options, attempts + 1);\n  }\n\n  private async _getConnectionPropsAsync(): Promise<{ hostname?: string; subdomain?: string }> {\n    const userDefinedSubdomain = env.EXPO_TUNNEL_SUBDOMAIN;\n    if (userDefinedSubdomain) {\n      const subdomain =\n        typeof userDefinedSubdomain === 'string'\n          ? userDefinedSubdomain\n          : await this._getProjectSubdomainAsync();\n      debug('Subdomain:', subdomain);\n      return { subdomain };\n    } else {\n      const hostname = await this._getProjectHostnameAsync();\n      debug('Hostname:', hostname);\n      return { hostname };\n    }\n  }\n\n  private async connectToNgrokInternalAsync(\n    instance: NgrokInstance,\n    attempts: number = 0\n  ): Promise<string | false> {\n    try {\n      // Global config path.\n      const configPath = path.join(UserSettings.getDirectory(), 'ngrok.yml');\n      debug('Global config path:', configPath);\n      const urlProps = await this._getConnectionPropsAsync();\n\n      const url = await instance.connect({\n        ...urlProps,\n        authtoken: NGROK_CONFIG.authToken,\n        configPath,\n        onStatusChange(status) {\n          if (status === 'closed') {\n            Log.error(\n              chalk.red(\n                'Tunnel connection has been closed. This is often related to intermittent connection problems with the Ngrok servers. Restart the dev server to try connecting to Ngrok again.'\n              ) + chalk.gray('\\nCheck the Ngrok status page for outages: https://status.ngrok.com/')\n            );\n          } else if (status === 'connected') {\n            Log.log('Tunnel connected.');\n          }\n        },\n        port: this.port,\n      });\n      return url;\n    } catch (error: any) {\n      const assertNgrok = () => {\n        if (isNgrokClientError(error)) {\n          throw new CommandError(\n            'NGROK_CONNECT',\n            [\n              error.body.msg,\n              error.body.details?.err,\n              chalk.gray('Check the Ngrok status page for outages: https://status.ngrok.com/'),\n            ]\n              .filter(Boolean)\n              .join('\\n\\n')\n          );\n        }\n        throw new CommandError(\n          'NGROK_CONNECT',\n          error.toString() +\n            chalk.gray('\\nCheck the Ngrok status page for outages: https://status.ngrok.com/')\n        );\n      };\n\n      // Attempt to connect 3 times\n      if (attempts >= 2) {\n        assertNgrok();\n      }\n\n      // Attempt to fix the issue\n      if (isNgrokClientError(error) && error.body.error_code === 103) {\n        // Assert early if a custom subdomain is used since it cannot\n        // be changed and retried. If the tunnel subdomain is a boolean\n        // then we can reset the randomness and try again.\n        if (typeof env.EXPO_TUNNEL_SUBDOMAIN === 'string') {\n          assertNgrok();\n        }\n        // Change randomness to avoid conflict if killing ngrok doesn't help\n        await this._resetProjectRandomnessAsync();\n      }\n\n      return false;\n    }\n  }\n\n  private async getProjectRandomnessAsync() {\n    const { urlRandomness: randomness } = await ProjectSettings.readAsync(this.projectRoot);\n    if (randomness) {\n      return randomness;\n    }\n    return await this._resetProjectRandomnessAsync();\n  }\n\n  async _resetProjectRandomnessAsync() {\n    const randomness = crypto.randomBytes(5).toString('base64url');\n    await ProjectSettings.setAsync(this.projectRoot, { urlRandomness: randomness });\n    debug('Resetting project randomness:', randomness);\n    return randomness;\n  }\n}\n"], "names": ["path", "Log", "debug", "require", "NGROK_CONFIG", "authToken", "domain", "TUNNEL_TIMEOUT", "AsyncNgrok", "constructor", "projectRoot", "port", "serverUrl", "resolver", "NgrokResolver", "getActiveUrl", "_getIdentifyingUrlSegmentsAsync", "user", "getUserAsync", "__typename", "CommandError", "username", "getActorDisplayName", "getProjectRandomnessAsync", "slugify", "remove", "String", "_getProjectHostnameAsync", "join", "_getProjectSubdomainAsync", "startAsync", "timeout", "resolveAsync", "prefersGlobalInstall", "hasAdbReverseAsync", "startAdbReverseAsync", "_connectToNgrokAsync", "log", "stopAsync", "get", "kill", "options", "attempts", "instance", "should<PERSON>rompt", "autoInstall", "results", "resolveWithTimeout", "connectToNgrokInternalAsync", "errorMessage", "delayAsync", "_getConnectionPropsAsync", "userDefinedSubdomain", "env", "EXPO_TUNNEL_SUBDOMAIN", "subdomain", "hostname", "config<PERSON><PERSON>", "UserSettings", "getDirectory", "urlProps", "url", "connect", "authtoken", "onStatusChange", "status", "error", "chalk", "red", "gray", "assertNgrok", "isNgrokClientError", "body", "msg", "details", "err", "filter", "Boolean", "toString", "error_code", "_resetProjectRandomnessAsync", "url<PERSON><PERSON><PERSON><PERSON>", "randomness", "ProjectSettings", "readAsync", "crypto", "randomBytes", "setAsync"], "mappings": "AAAA;;;;AAAkB,IAAA,MAAO,kCAAP,OAAO,EAAA;AACN,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACfA,IAAAA,IAAI,mCAAM,MAAM,EAAZ;AACI,IAAA,QAAS,kCAAT,SAAS,EAAA;AAEJ,IAAA,aAA6B,kCAA7B,6BAA6B,EAAA;AACJ,IAAA,KAAqB,WAArB,qBAAqB,CAAA;AAC3DC,IAAAA,GAAG,mCAAM,WAAW,EAAjB;AACgC,IAAA,MAAmB,WAAnB,mBAAmB,CAAA;AAC9C,IAAA,IAAiB,WAAjB,iBAAiB,CAAA;AACR,IAAA,OAAoB,WAApB,oBAAoB,CAAA;AACgB,IAAA,cAA+B,WAA/B,+BAA+B,CAAA;AACvC,IAAA,WAAiC,WAAjC,iCAAiC,CAAA;AAC1D,IAAA,SAAqB,WAArB,qBAAqB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,AAAsB,AAAC;AAEhF,MAAMC,YAAY,GAAG;IACnBC,SAAS,EAAE,6CAA6C;IACxDC,MAAM,EAAE,YAAY;CACrB,AAAC;AAEF,MAAMC,cAAc,GAAG,EAAE,GAAG,IAAI,AAAC;AAE1B,MAAMC,UAAU;IAOrBC,YACUC,WAAmB,EACnBC,IAAY,CACpB;aAFQD,WAAmB,GAAnBA,WAAmB;aACnBC,IAAY,GAAZA,IAAY;aAJdC,SAAS,GAAkB,IAAI;QAMrC,IAAI,CAACC,QAAQ,GAAG,IAAIC,cAAa,cAAA,CAACJ,WAAW,CAAC,CAAC;KAChD;IAED,AAAOK,YAAY,GAAkB;QACnC,OAAO,IAAI,CAACH,SAAS,CAAC;KACvB;IAED,2BAA2B,CAC3B,MAAMI,+BAA+B,GAAsB;QACzD,MAAMC,IAAI,GAAG,MAAMC,CAAAA,GAAAA,KAAY,AAAE,CAAA,aAAF,EAAE,AAAC;QAClC,IAAID,CAAAA,IAAI,QAAY,GAAhBA,KAAAA,CAAgB,GAAhBA,IAAI,CAAEE,UAAU,CAAA,KAAK,OAAO,EAAE;YAChC,MAAM,IAAIC,OAAY,aAAA,CAAC,aAAa,EAAE,qCAAqC,CAAC,CAAC;SAC9E;QACD,MAAMC,QAAQ,GAAGC,CAAAA,GAAAA,KAAmB,AAAM,CAAA,oBAAN,CAACL,IAAI,CAAC,AAAC;QAE3C,OAAO;YACL,sEAAsE;YACtE,MAAM,IAAI,CAACM,yBAAyB,EAAE;YACtC,uFAAuF;YACvFC,CAAAA,GAAAA,QAAO,AAA4B,CAAA,QAA5B,CAACH,QAAQ,EAAE;gBAAEI,MAAM,MAAM;aAAE,CAAC;YACnC,yEAAyE;YACzEC,MAAM,CAAC,IAAI,CAACf,IAAI,CAAC;SAClB,CAAC;KACH;IAED,2BAA2B,CAC3B,MAAMgB,wBAAwB,GAAoB;QAChD,OAAO,CAAC,EAAE,CAAC,MAAM,IAAI,CAACX,+BAA+B,EAAE,CAAC,CAACY,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAExB,YAAY,CAACE,MAAM,CAAC,CAAC,CAAC;KAC7F;IAED,2BAA2B,CAC3B,MAAMuB,yBAAyB,GAAoB;QACjD,OAAO,CAAC,MAAM,IAAI,CAACb,+BAA+B,EAAE,CAAC,CAACY,IAAI,CAAC,GAAG,CAAC,CAAC;KACjE;IAED,qDAAqD,CACrD,MAAME,UAAU,CAAC,EAAEC,OAAO,CAAA,EAAwB,GAAG,EAAE,EAAiB;QACtE,+FAA+F;QAC/F,MAAM,IAAI,CAAClB,QAAQ,CAACmB,YAAY,CAAC;YAC/B,sHAAsH;YACtHC,oBAAoB,EAAE,IAAI;SAC3B,CAAC,CAAC;QAEH,2DAA2D;QAC3D,4CAA4C;QAC5C,IAAIC,CAAAA,GAAAA,WAAkB,AAAE,CAAA,mBAAF,EAAE,EAAE;YACxB,iCAAiC;YACjC,IAAI,CAAE,MAAMC,CAAAA,GAAAA,WAAoB,AAAa,CAAA,qBAAb,CAAC;gBAAC,IAAI,CAACxB,IAAI;aAAC,CAAC,AAAC,EAAE;gBAC9C,8BAA8B;gBAC9B,MAAM,IAAIS,OAAY,aAAA,CACpB,WAAW,EACX,CAAC,2FAA2F,CAAC,CAC9F,CAAC;aACH;SACF;QAED,IAAI,CAACR,SAAS,GAAG,MAAM,IAAI,CAACwB,oBAAoB,CAAC;YAAEL,OAAO;SAAE,CAAC,CAAC;QAE9D7B,KAAK,CAAC,aAAa,EAAE,IAAI,CAACU,SAAS,CAAC,CAAC;QACrCX,GAAG,CAACoC,GAAG,CAAC,eAAe,CAAC,CAAC;KAC1B;IAED,8CAA8C,CAC9C,MAAaC,SAAS,GAAkB;YAGhC,GAAmB;QAFzBpC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAEzB,OAAM,CAAA,GAAmB,GAAnB,IAAI,CAACW,QAAQ,CAAC0B,GAAG,EAAE,SAAM,GAAzB,KAAA,CAAyB,GAAzB,GAAmB,CAAEC,IAAI,QAAI,GAA7B,KAAA,CAA6B,GAA7B,GAAmB,CAAEA,IAAI,EAAI,CAAA,CAAC;QACpC,IAAI,CAAC5B,SAAS,GAAG,IAAI,CAAC;KACvB;IAED,2BAA2B,CAC3B,MAAMwB,oBAAoB,CACxBK,OAA6B,GAAG,EAAE,EAClCC,QAAgB,GAAG,CAAC,EACH;QACjB,gGAAgG;QAChG,MAAM,IAAI,CAACJ,SAAS,EAAE,CAAC;QAEvB,gDAAgD;QAChD,MAAMK,QAAQ,GAAG,MAAM,IAAI,CAAC9B,QAAQ,CAACmB,YAAY,CAAC;YAChDY,YAAY,EAAE,KAAK;YACnBC,WAAW,EAAE,KAAK;SACnB,CAAC,AAAC;YAOUJ,QAAe;QAL5B,4DAA4D;QAC5D,gEAAgE;QAChE,MAAMK,OAAO,GAAG,MAAMC,CAAAA,GAAAA,MAAkB,AAMvC,CAAA,mBANuC,CACtC,IAAM,IAAI,CAACC,2BAA2B,CAACL,QAAQ,EAAED,QAAQ,CAAC;QAAA,EAC1D;YACEX,OAAO,EAAEU,CAAAA,QAAe,GAAfA,OAAO,CAACV,OAAO,YAAfU,QAAe,GAAIlC,cAAc;YAC1C0C,YAAY,EAAE,wCAAwC;SACvD,CACF,AAAC;QACF,IAAI,OAAOH,OAAO,KAAK,QAAQ,EAAE;YAC/B,OAAOA,OAAO,CAAC;SAChB;QAED,gCAAgC;QAChC,MAAMI,CAAAA,GAAAA,MAAU,AAAK,CAAA,WAAL,CAAC,GAAG,CAAC,CAAC;QAEtB,OAAO,IAAI,CAACd,oBAAoB,CAACK,OAAO,EAAEC,QAAQ,GAAG,CAAC,CAAC,CAAC;KACzD;IAED,MAAcS,wBAAwB,GAAuD;QAC3F,MAAMC,oBAAoB,GAAGC,IAAG,IAAA,CAACC,qBAAqB,AAAC;QACvD,IAAIF,oBAAoB,EAAE;YACxB,MAAMG,SAAS,GACb,OAAOH,oBAAoB,KAAK,QAAQ,GACpCA,oBAAoB,GACpB,MAAM,IAAI,CAACvB,yBAAyB,EAAE,AAAC;YAC7C3B,KAAK,CAAC,YAAY,EAAEqD,SAAS,CAAC,CAAC;YAC/B,OAAO;gBAAEA,SAAS;aAAE,CAAC;SACtB,MAAM;YACL,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAAC7B,wBAAwB,EAAE,AAAC;YACvDzB,KAAK,CAAC,WAAW,EAAEsD,QAAQ,CAAC,CAAC;YAC7B,OAAO;gBAAEA,QAAQ;aAAE,CAAC;SACrB;KACF;IAED,MAAcR,2BAA2B,CACvCL,QAAuB,EACvBD,QAAgB,GAAG,CAAC,EACK;QACzB,IAAI;YACF,sBAAsB;YACtB,MAAMe,UAAU,GAAGzD,IAAI,CAAC4B,IAAI,CAAC8B,aAAY,QAAA,CAACC,YAAY,EAAE,EAAE,WAAW,CAAC,AAAC;YACvEzD,KAAK,CAAC,qBAAqB,EAAEuD,UAAU,CAAC,CAAC;YACzC,MAAMG,QAAQ,GAAG,MAAM,IAAI,CAACT,wBAAwB,EAAE,AAAC;YAEvD,MAAMU,GAAG,GAAG,MAAMlB,QAAQ,CAACmB,OAAO,CAAC;gBACjC,GAAGF,QAAQ;gBACXG,SAAS,EAAE3D,YAAY,CAACC,SAAS;gBACjCoD,UAAU;gBACVO,cAAc,EAACC,MAAM,EAAE;oBACrB,IAAIA,MAAM,KAAK,QAAQ,EAAE;wBACvBhE,GAAG,CAACiE,KAAK,CACPC,MAAK,QAAA,CAACC,GAAG,CACP,+KAA+K,CAChL,GAAGD,MAAK,QAAA,CAACE,IAAI,CAAC,sEAAsE,CAAC,CACvF,CAAC;qBACH,MAAM,IAAIJ,MAAM,KAAK,WAAW,EAAE;wBACjChE,GAAG,CAACoC,GAAG,CAAC,mBAAmB,CAAC,CAAC;qBAC9B;iBACF;gBACD1B,IAAI,EAAE,IAAI,CAACA,IAAI;aAChB,CAAC,AAAC;YACH,OAAOkD,GAAG,CAAC;SACZ,CAAC,OAAOK,KAAK,EAAO;YACnB,MAAMI,WAAW,GAAG,IAAM;gBACxB,IAAIC,CAAAA,GAAAA,cAAkB,AAAO,CAAA,mBAAP,CAACL,KAAK,CAAC,EAAE;wBAKzBA,GAAkB;oBAJtB,MAAM,IAAI9C,OAAY,aAAA,CACpB,eAAe,EACf;wBACE8C,KAAK,CAACM,IAAI,CAACC,GAAG;wBACdP,CAAAA,GAAkB,GAAlBA,KAAK,CAACM,IAAI,CAACE,OAAO,SAAK,GAAvBR,KAAAA,CAAuB,GAAvBA,GAAkB,CAAES,GAAG;wBACvBR,MAAK,QAAA,CAACE,IAAI,CAAC,oEAAoE,CAAC;qBACjF,CACEO,MAAM,CAACC,OAAO,CAAC,CACfjD,IAAI,CAAC,MAAM,CAAC,CAChB,CAAC;iBACH;gBACD,MAAM,IAAIR,OAAY,aAAA,CACpB,eAAe,EACf8C,KAAK,CAACY,QAAQ,EAAE,GACdX,MAAK,QAAA,CAACE,IAAI,CAAC,sEAAsE,CAAC,CACrF,CAAC;aACH,AAAC;YAEF,6BAA6B;YAC7B,IAAI3B,QAAQ,IAAI,CAAC,EAAE;gBACjB4B,WAAW,EAAE,CAAC;aACf;YAED,2BAA2B;YAC3B,IAAIC,CAAAA,GAAAA,cAAkB,AAAO,CAAA,mBAAP,CAACL,KAAK,CAAC,IAAIA,KAAK,CAACM,IAAI,CAACO,UAAU,KAAK,GAAG,EAAE;gBAC9D,6DAA6D;gBAC7D,+DAA+D;gBAC/D,kDAAkD;gBAClD,IAAI,OAAO1B,IAAG,IAAA,CAACC,qBAAqB,KAAK,QAAQ,EAAE;oBACjDgB,WAAW,EAAE,CAAC;iBACf;gBACD,oEAAoE;gBACpE,MAAM,IAAI,CAACU,4BAA4B,EAAE,CAAC;aAC3C;YAED,OAAO,KAAK,CAAC;SACd;KACF;IAED,MAAczD,yBAAyB,GAAG;QACxC,MAAM,EAAE0D,aAAa,EAAEC,UAAU,CAAA,EAAE,GAAG,MAAMC,SAAe,gBAAA,CAACC,SAAS,CAAC,IAAI,CAAC1E,WAAW,CAAC,AAAC;QACxF,IAAIwE,UAAU,EAAE;YACd,OAAOA,UAAU,CAAC;SACnB;QACD,OAAO,MAAM,IAAI,CAACF,4BAA4B,EAAE,CAAC;KAClD;IAED,MAAMA,4BAA4B,GAAG;QACnC,MAAME,UAAU,GAAGG,OAAM,QAAA,CAACC,WAAW,CAAC,CAAC,CAAC,CAACR,QAAQ,CAAC,WAAW,CAAC,AAAC;QAC/D,MAAMK,SAAe,gBAAA,CAACI,QAAQ,CAAC,IAAI,CAAC7E,WAAW,EAAE;YAAEuE,aAAa,EAAEC,UAAU;SAAE,CAAC,CAAC;QAChFhF,KAAK,CAAC,+BAA+B,EAAEgF,UAAU,CAAC,CAAC;QACnD,OAAOA,UAAU,CAAC;KACnB;CACF;QAtNY1E,UAAU,GAAVA,UAAU"}