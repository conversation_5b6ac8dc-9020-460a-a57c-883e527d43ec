{"version": 3, "sources": ["../../../../../src/start/server/middleware/CorsMiddleware.ts"], "sourcesContent": ["import type { ExpoConfig } from '@expo/config';\n\nimport type { ServerRequest, ServerResponse } from './server.types';\n\nconst DEFAULT_ALLOWED_CORS_HOSTNAMES = [\n  'localhost',\n  'chrome-devtools-frontend.appspot.com', // Support remote Chrome DevTools frontend\n  'devtools', // Support local Chrome DevTools `devtools://devtools`\n];\n\nexport function createCorsMiddleware(exp: ExpoConfig) {\n  const allowedHostnames = [...DEFAULT_ALLOWED_CORS_HOSTNAMES];\n  // Support for expo-router API routes\n  if (exp.extra?.router?.headOrigin) {\n    allowedHostnames.push(new URL(exp.extra.router.headOrigin).hostname);\n  }\n  if (exp.extra?.router?.origin) {\n    allowedHostnames.push(new URL(exp.extra.router.origin).hostname);\n  }\n\n  return (req: ServerRequest, res: ServerResponse, next: (err?: Error) => void) => {\n    if (typeof req.headers.origin === 'string') {\n      const { hostname } = new URL(req.headers.origin);\n      if (!allowedHostnames.includes(hostname)) {\n        next(\n          new Error(\n            `Unauthorized request from ${req.headers.origin}. ` +\n              'This may happen because of a conflicting browser extension to intercept HTTP requests. ' +\n              'Please try again without browser extensions or using incognito mode.'\n          )\n        );\n        return;\n      }\n\n      res.setHeader('Access-Control-Allow-Origin', req.headers.origin);\n      maybePreventMetroResetCorsHeader(req, res);\n    }\n\n    // Block MIME-type sniffing.\n    res.setHeader('X-Content-Type-Options', 'nosniff');\n\n    next();\n  };\n}\n\n// When accessing source maps,\n// metro will overwrite the `Access-Control-Allow-Origin` header with hardcoded `devtools://devtools` value.\n// https://github.com/facebook/metro/blob/a7f8955e6d2424b0d5f73d4bcdaf22560e1d5f27/packages/metro/src/Server.js#L540\n// This is a workaround to prevent this behavior.\nfunction maybePreventMetroResetCorsHeader(req: ServerRequest, res: ServerResponse) {\n  const pathname = req.url ? new URL(req.url, `http://${req.headers.host}`).pathname : '';\n  if (pathname.endsWith('.map')) {\n    const setHeader = res.setHeader.bind(res);\n    res.setHeader = (key, ...args) => {\n      if (key !== 'Access-Control-Allow-Origin') {\n        setHeader(key, ...args);\n      }\n      return res;\n    };\n  }\n}\n"], "names": ["createCorsMiddleware", "DEFAULT_ALLOWED_CORS_HOSTNAMES", "exp", "allowedHostnames", "extra", "router", "<PERSON><PERSON><PERSON><PERSON>", "push", "URL", "hostname", "origin", "req", "res", "next", "headers", "includes", "Error", "<PERSON><PERSON><PERSON><PERSON>", "maybePreventMetroResetCorsHeader", "pathname", "url", "host", "endsWith", "bind", "key", "args"], "mappings": "AAAA;;;;QAUgBA,oBAAoB,GAApBA,oBAAoB;AANpC,MAAMC,8BAA8B,GAAG;IACrC,WAAW;IACX,sCAAsC;IACtC,UAAU;CACX,AAAC;AAEK,SAASD,oBAAoB,CAACE,GAAe,EAAE;QAGhDA,GAAS,QAGTA,IAAS;IALb,MAAMC,gBAAgB,GAAG;WAAIF,8BAA8B;KAAC,AAAC;IAC7D,qCAAqC;IACrC,IAAIC,CAAAA,GAAS,GAATA,GAAG,CAACE,KAAK,SAAQ,GAAjBF,KAAAA,CAAiB,GAAjBA,QAAAA,GAAS,CAAEG,MAAM,SAAA,GAAjBH,KAAAA,CAAiB,QAAEI,UAAU,AAAZ,EAAc;QACjCH,gBAAgB,CAACI,IAAI,CAAC,IAAIC,GAAG,CAACN,GAAG,CAACE,KAAK,CAACC,MAAM,CAACC,UAAU,CAAC,CAACG,QAAQ,CAAC,CAAC;KACtE;IACD,IAAIP,CAAAA,IAAS,GAATA,GAAG,CAACE,KAAK,SAAQ,GAAjBF,KAAAA,CAAiB,GAAjBA,QAAAA,IAAS,CAAEG,MAAM,SAAA,GAAjBH,KAAAA,CAAiB,QAAEQ,MAAM,AAAR,EAAU;QAC7BP,gBAAgB,CAACI,IAAI,CAAC,IAAIC,GAAG,CAACN,GAAG,CAACE,KAAK,CAACC,MAAM,CAACK,MAAM,CAAC,CAACD,QAAQ,CAAC,CAAC;KAClE;IAED,OAAO,CAACE,GAAkB,EAAEC,GAAmB,EAAEC,IAA2B,GAAK;QAC/E,IAAI,OAAOF,GAAG,CAACG,OAAO,CAACJ,MAAM,KAAK,QAAQ,EAAE;YAC1C,MAAM,EAAED,QAAQ,CAAA,EAAE,GAAG,IAAID,GAAG,CAACG,GAAG,CAACG,OAAO,CAACJ,MAAM,CAAC,AAAC;YACjD,IAAI,CAACP,gBAAgB,CAACY,QAAQ,CAACN,QAAQ,CAAC,EAAE;gBACxCI,IAAI,CACF,IAAIG,KAAK,CACP,CAAC,0BAA0B,EAAEL,GAAG,CAACG,OAAO,CAACJ,MAAM,CAAC,EAAE,CAAC,GACjD,yFAAyF,GACzF,sEAAsE,CACzE,CACF,CAAC;gBACF,OAAO;aACR;YAEDE,GAAG,CAACK,SAAS,CAAC,6BAA6B,EAAEN,GAAG,CAACG,OAAO,CAACJ,MAAM,CAAC,CAAC;YACjEQ,gCAAgC,CAACP,GAAG,EAAEC,GAAG,CAAC,CAAC;SAC5C;QAED,4BAA4B;QAC5BA,GAAG,CAACK,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;QAEnDJ,IAAI,EAAE,CAAC;KACR,CAAC;CACH;AAED,8BAA8B;AAC9B,4GAA4G;AAC5G,oHAAoH;AACpH,iDAAiD;AACjD,SAASK,gCAAgC,CAACP,GAAkB,EAAEC,GAAmB,EAAE;IACjF,MAAMO,QAAQ,GAAGR,GAAG,CAACS,GAAG,GAAG,IAAIZ,GAAG,CAACG,GAAG,CAACS,GAAG,EAAE,CAAC,OAAO,EAAET,GAAG,CAACG,OAAO,CAACO,IAAI,CAAC,CAAC,CAAC,CAACF,QAAQ,GAAG,EAAE,AAAC;IACxF,IAAIA,QAAQ,CAACG,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC7B,MAAML,SAAS,GAAGL,GAAG,CAACK,SAAS,CAACM,IAAI,CAACX,GAAG,CAAC,AAAC;QAC1CA,GAAG,CAACK,SAAS,GAAG,CAACO,GAAG,EAAKC,GAAAA,IAAI,GAAK;YAChC,IAAID,GAAG,KAAK,6BAA6B,EAAE;gBACzCP,SAAS,CAACO,GAAG,KAAKC,IAAI,CAAC,CAAC;aACzB;YACD,OAAOb,GAAG,CAAC;SACZ,CAAC;KACH;CACF"}