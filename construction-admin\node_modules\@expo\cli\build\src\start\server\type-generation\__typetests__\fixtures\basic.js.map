{"version": 3, "sources": ["../../../../../../../src/start/server/type-generation/__typetests__/fixtures/basic.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable import/export */\n/* eslint-disable @typescript-eslint/ban-types */\n\nimport type { LinkProps as OriginalLinkProps } from 'expo-router/build/link/Link';\nimport type { Router as OriginalRouter } from 'expo-router/build/types';\nexport * from 'expo-router/build';\n\n// prettier-ignore\ntype StaticRoutes = `/apple` | `/banana`;\n// prettier-ignore\ntype DynamicRoutes<T extends string> = `/colors/${SingleRoutePart<T>}` | `/animals/${CatchAllRoutePart<T>}` | `/mix/${SingleRoutePart<T>}/${SingleRoutePart<T>}/${CatchAllRoutePart<T>}`;\n// prettier-ignore\ntype DynamicRouteTemplate = `/colors/[color]` | `/animals/[...animal]` | `/mix/[fruit]/[color]/[...animals]`;\n\ntype RelativePathString = `./${string}` | `../${string}` | '..';\ntype AbsoluteRoute = DynamicRouteTemplate | StaticRoutes;\ntype ExternalPathString = `${string}:${string}`;\n\ntype ExpoRouterRoutes = DynamicRouteTemplate | StaticRoutes | RelativePathString;\nexport type AllRoutes = ExpoRouterRoutes | ExternalPathString;\n\n/****************\n * Route Utils  *\n ****************/\n\ntype SearchOrHash = `?${string}` | `#${string}`;\ntype UnknownInputParams = Record<string, string | number | (string | number)[]>;\ntype UnknownOutputParams = Record<string, string | string[]>;\n\n/**\n * Return only the RoutePart of a string. If the string has multiple parts return never\n *\n * string   | type\n * ---------|------\n * 123      | 123\n * /123/abc | never\n * 123?abc  | never\n * ./123    | never\n * /123     | never\n * 123/../  | never\n */\ntype SingleRoutePart<S extends string> = S extends `${string}/${string}`\n  ? never\n  : S extends `${string}${SearchOrHash}`\n    ? never\n    : S extends ''\n      ? never\n      : S extends `(${string})`\n        ? never\n        : S extends `[${string}]`\n          ? never\n          : S;\n\n/**\n * Return only the CatchAll router part. If the string has search parameters or a hash return never\n */\ntype CatchAllRoutePart<S extends string> = S extends `${string}${SearchOrHash}`\n  ? never\n  : S extends ''\n    ? never\n    : S extends `${string}(${string})${string}`\n      ? never\n      : S extends `${string}[${string}]${string}`\n        ? never\n        : S;\n\n// type OptionalCatchAllRoutePart<S extends string> = S extends `${string}${SearchOrHash}` ? never : S\n\n/**\n * Return the name of a route parameter\n * '[test]'    -> 'test'\n * 'test'      -> never\n * '[...test]' -> '...test'\n */\ntype IsParameter<Part> = Part extends `[${infer ParamName}]` ? ParamName : never;\n\n/**\n * Return a union of all parameter names. If there are no names return never\n *\n * /[test]         -> 'test'\n * /[abc]/[...def] -> 'abc'|'...def'\n */\ntype ParameterNames<Path> = Path extends `${infer PartA}/${infer PartB}`\n  ? IsParameter<PartA> | ParameterNames<PartB>\n  : IsParameter<Path>;\n\n/**\n * Returns all segements of a route.\n *\n * /(group)/123/abc/[id]/[...rest] -> ['(group)', '123', 'abc', '[id]', '[...rest]'\n */\ntype RouteSegments<Path> = Path extends `${infer PartA}/${infer PartB}`\n  ? PartA extends '' | '.'\n    ? [...RouteSegments<PartB>]\n    : [PartA, ...RouteSegments<PartB>]\n  : Path extends ''\n    ? []\n    : [Path];\n\n/**\n * Returns a Record of the routes parameters as strings and CatchAll parameters\n *\n * There are two versions, input and output, as you can input 'string | number' but\n *  the output will always be 'string'\n *\n * /[id]/[...rest] -> { id: string, rest: string[] }\n * /no-params      -> {}\n */\ntype InputRouteParams<Path> = {\n  [Key in ParameterNames<Path> as Key extends `...${infer Name}`\n    ? Name\n    : Key]: Key extends `...${string}` ? (string | number)[] : string | number;\n} & UnknownInputParams;\n\ntype OutputRouteParams<Path> = {\n  [Key in ParameterNames<Path> as Key extends `...${infer Name}`\n    ? Name\n    : Key]: Key extends `...${string}` ? string[] : string;\n} & UnknownOutputParams;\n\n/**\n * Returns the search parameters for a route.\n */\nexport type SearchParams<T extends AllRoutes> = T extends DynamicRouteTemplate\n  ? OutputRouteParams<T>\n  : T extends StaticRoutes\n    ? never\n    : UnknownOutputParams;\n\n/**\n * Route is mostly used as part of Href to ensure that a valid route is provided\n *\n * Given a dynamic route, this will return never. This is helpful for conditional logic\n *\n * /test         -> /test, /test2, etc\n * /test/[abc]   -> never\n * /test/resolve -> /test, /test2, etc\n *\n * Note that if we provide a value for [abc] then the route is allowed\n *\n * This is named Route to prevent confusion, as users they will often see it in tooltips\n */\nexport type Route<T> = T extends string\n  ? T extends DynamicRouteTemplate\n    ? never\n    :\n        | StaticRoutes\n        | RelativePathString\n        | ExternalPathString\n        | (T extends `${infer P}${SearchOrHash}`\n            ? P extends DynamicRoutes<infer _>\n              ? T\n              : never\n            : T extends DynamicRoutes<infer _>\n              ? T\n              : never)\n  : never;\n\n/*********\n * Href  *\n *********/\n\nexport type Href<T> = T extends Record<'pathname', string> ? HrefObject<T> : Route<T>;\n\nexport type HrefObject<\n  R extends Record<'pathname', string>,\n  P = R['pathname'],\n> = P extends DynamicRouteTemplate\n  ? { pathname: P; params: InputRouteParams<P> }\n  : P extends Route<P>\n    ? { pathname: Route<P> | DynamicRouteTemplate; params?: never | InputRouteParams<never> }\n    : never;\n\n/***********************\n * Expo Router Exports *\n ***********************/\n\nexport type Router = Omit<OriginalRouter, 'push' | 'replace' | 'setParams'> & {\n  /** Navigate to the provided href. */\n  push: <T>(href: Href<T>) => void;\n  /** Navigate to route without appending to the history. */\n  replace: <T>(href: Href<T>) => void;\n  /** Update the current route query params. */\n  setParams: <T = ''>(params?: T extends '' ? Record<string, string> : InputRouteParams<T>) => void;\n};\n\n/** The imperative router. */\nexport declare const router: Router;\n\n/************\n * <Link /> *\n ************/\nexport interface LinkProps<T> extends OriginalLinkProps {\n  href: Href<T>;\n}\n\nexport interface LinkComponent {\n  <T>(props: React.PropsWithChildren<LinkProps<T>>): JSX.Element;\n  /** Helper method to resolve an Href object into a string. */\n  resolveHref: <T>(href: Href<T>) => string;\n}\n\n/**\n * Component to render link to another route using a path.\n * Uses an anchor tag on the web.\n *\n * @param props.href Absolute path to route (e.g. `/feeds/hot`).\n * @param props.replace Should replace the current route without adding to the history.\n * @param props.asChild Forward props to child component. Useful for custom buttons.\n * @param props.children Child elements to render the content.\n * @param props.className On web, this sets the HTML `class` directly. On native, this can be used with CSS interop tools like Nativewind.\n */\nexport declare const Link: LinkComponent;\n\n/** Redirects to the href as soon as the component is mounted. */\nexport declare const Redirect: <T>(\n  props: React.PropsWithChildren<{ href: Href<T> }>\n) => JSX.Element;\n\n/************\n * Hooks *\n ************/\nexport declare function useRouter(): Router;\n\nexport declare function useLocalSearchParams<\n  T extends AllRoutes | UnknownOutputParams = UnknownOutputParams,\n>(): T extends AllRoutes ? SearchParams<T> : T;\n\n/** @deprecated renamed to `useGlobalSearchParams` */\nexport declare function useSearchParams<\n  T extends AllRoutes | UnknownOutputParams = UnknownOutputParams,\n>(): T extends AllRoutes ? SearchParams<T> : T;\n\nexport declare function useGlobalSearchParams<\n  T extends AllRoutes | UnknownOutputParams = UnknownOutputParams,\n>(): T extends AllRoutes ? SearchParams<T> : T;\n\nexport declare function useSegments<\n  T extends AbsoluteRoute | RouteSegments<AbsoluteRoute> | RelativePathString,\n>(): T extends AbsoluteRoute ? RouteSegments<T> : T extends string ? string[] : T;\n"], "names": [], "mappings": "AAIA;;;;6CAEc,mBAAmB;AAAjC,YAAA,MAAkC;;2CAAlC,MAAkC;;;;mBAAlC,MAAkC;;;EAAA"}