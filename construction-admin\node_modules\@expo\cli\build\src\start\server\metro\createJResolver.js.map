{"version": 3, "sources": ["../../../../../src/start/server/metro/createJResolver.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Fork of the jest resolver but with additional settings for Metro and pnp removed.\n * https://github.com/jestjs/jest/blob/d1a2ed7fea4bdc19836274cd810c8360e3ab62f3/packages/jest-resolve/src/defaultResolver.ts#L1\n */\nimport type { JSONObject as PackageJSON } from '@expo/json-file';\nimport assert from 'assert';\nimport { dirname, isAbsolute, resolve as pathResolve } from 'path';\nimport { sync as resolveSync, SyncOpts as UpstreamResolveOptions } from 'resolve';\nimport * as resolve from 'resolve.exports';\n\nimport { directoryExistsSync, fileExistsSync } from '../../../utils/dir';\n\n/**\n * Allows transforming parsed `package.json` contents.\n *\n * @param pkg - Parsed `package.json` contents.\n * @param file - Path to `package.json` file.\n * @param dir - Directory that contains the `package.json`.\n *\n * @returns Transformed `package.json` contents.\n */\ntype PackageFilter = (pkg: PackageJSON, file: string, dir: string) => PackageJSON;\n\n/**\n * Allows transforming a path within a package.\n *\n * @param pkg - Parsed `package.json` contents.\n * @param path - Path being resolved.\n * @param relativePath - Path relative from the `package.json` location.\n *\n * @returns Relative path that will be joined from the `package.json` location.\n */\ntype PathFilter = (pkg: PackageJSON, path: string, relativePath: string) => string;\n\ntype ResolverOptions = {\n  /** Directory to begin resolving from. */\n  basedir: string;\n  /** List of export conditions. */\n  conditions?: string[];\n  /** Instance of default resolver. */\n  defaultResolver: typeof defaultResolver;\n  /** List of file extensions to search in order. */\n  extensions?: string[];\n  /**\n   * List of directory names to be looked up for modules recursively.\n   *\n   * @defaultValue\n   * The default is `['node_modules']`.\n   */\n  moduleDirectory?: string[];\n  /**\n   * List of `require.paths` to use if nothing is found in `node_modules`.\n   *\n   * @defaultValue\n   * The default is `undefined`.\n   */\n  paths?: string[];\n  /** Allows transforming parsed `package.json` contents. */\n  packageFilter?: PackageFilter;\n  /** Allows transforms a path within a package. */\n  pathFilter?: PathFilter;\n  /** Current root directory. */\n  rootDir?: string;\n\n  enablePackageExports?: boolean;\n\n  blockList: RegExp[];\n\n  getPackageForModule: import('metro-resolver').CustomResolutionContext['getPackageForModule'];\n} & Pick<\n  UpstreamResolveOptions,\n  | 'readPackageSync'\n  | 'realpathSync'\n  | 'moduleDirectory'\n  | 'extensions'\n  | 'preserveSymlinks'\n  | 'includeCoreModules'\n>;\n\ntype UpstreamResolveOptionsWithConditions = UpstreamResolveOptions & ResolverOptions;\n\nconst defaultResolver = (\n  path: string,\n  {\n    enablePackageExports,\n    blockList = [],\n    ...options\n  }: Omit<ResolverOptions, 'defaultResolver' | 'getPackageForModule'>\n): string => {\n  // @ts-expect-error\n  const resolveOptions: UpstreamResolveOptionsWithConditions = {\n    ...options,\n\n    isDirectory(file) {\n      if (blockList.some((regex) => regex.test(file))) {\n        return false;\n      }\n      return directoryExistsSync(file);\n    },\n    isFile(file) {\n      if (blockList.some((regex) => regex.test(file))) {\n        return false;\n      }\n      return fileExistsSync(file);\n    },\n    preserveSymlinks: options.preserveSymlinks,\n    defaultResolver,\n  };\n\n  // resolveSync dereferences symlinks to ensure we don't create a separate\n  // module instance depending on how it was referenced.\n  const result = resolveSync(enablePackageExports ? getPathInModule(path, resolveOptions) : path, {\n    ...resolveOptions,\n    preserveSymlinks: !options.preserveSymlinks,\n  });\n\n  return result;\n};\n\nexport default defaultResolver;\n\n/*\n * helper functions\n */\n\nfunction getPathInModule(path: string, options: UpstreamResolveOptionsWithConditions): string {\n  if (shouldIgnoreRequestForExports(path)) {\n    return path;\n  }\n\n  const segments = path.split('/');\n\n  let moduleName = segments.shift();\n\n  if (moduleName) {\n    if (moduleName.startsWith('@')) {\n      moduleName = `${moduleName}/${segments.shift()}`;\n    }\n\n    // Disable package exports for babel/runtime for https://github.com/facebook/metro/issues/984/\n    if (moduleName === '@babel/runtime') {\n      return path;\n    }\n\n    // self-reference\n    const closestPackageJson = findClosestPackageJson(options.basedir, options);\n    if (closestPackageJson) {\n      const pkg = options.readPackageSync!(options.readFileSync!, closestPackageJson);\n      assert(pkg, 'package.json should be read by `readPackageSync`');\n\n      if (pkg.name === moduleName) {\n        const resolved = resolve.exports(\n          pkg,\n          (segments.join('/') || '.') as resolve.Exports.Entry,\n          createResolveOptions(options.conditions)\n        );\n\n        if (resolved) {\n          return pathResolve(dirname(closestPackageJson), resolved[0]);\n        }\n\n        if (pkg.exports) {\n          throw new Error(\n            \"`exports` exists, but no results - this is a bug in Expo CLI's Metro resolver. Please report an issue\"\n          );\n        }\n      }\n    }\n\n    let packageJsonPath = '';\n\n    try {\n      packageJsonPath = resolveSync(`${moduleName}/package.json`, options);\n    } catch {\n      // ignore if package.json cannot be found\n    }\n\n    if (packageJsonPath && options.isFile!(packageJsonPath)) {\n      const pkg = options.readPackageSync!(options.readFileSync!, packageJsonPath);\n      assert(pkg, 'package.json should be read by `readPackageSync`');\n\n      const resolved = resolve.exports(\n        pkg,\n        (segments.join('/') || '.') as resolve.Exports.Entry,\n        createResolveOptions(options.conditions)\n      );\n\n      if (resolved) {\n        return pathResolve(dirname(packageJsonPath), resolved[0]);\n      }\n\n      if (pkg.exports) {\n        throw new Error(\n          \"`exports` exists, but no results - this is a bug in Expo CLI's Metro resolver. Please report an issue\"\n        );\n      }\n    }\n  }\n\n  return path;\n}\n\nfunction createResolveOptions(conditions: string[] | undefined): resolve.Options {\n  return conditions\n    ? { conditions, unsafe: true }\n    : // no conditions were passed - let's assume this is Jest internal and it should be `require`\n      { browser: false, require: true };\n}\n\n// if it's a relative import or an absolute path, imports/exports are ignored\nconst shouldIgnoreRequestForExports = (path: string) => path.startsWith('.') || isAbsolute(path);\n\n// adapted from\n// https://github.com/lukeed/escalade/blob/2477005062cdbd8407afc90d3f48f4930354252b/src/sync.js\nfunction findClosestPackageJson(\n  start: string,\n  options: UpstreamResolveOptions\n): string | undefined {\n  let dir = pathResolve('.', start);\n  if (!options.isDirectory!(dir)) {\n    dir = dirname(dir);\n  }\n\n  while (true) {\n    const pkgJsonFile = pathResolve(dir, './package.json');\n    const hasPackageJson = options.isFile!(pkgJsonFile);\n\n    if (hasPackageJson) {\n      return pkgJsonFile;\n    }\n\n    const prevDir = dir;\n    dir = dirname(dir);\n\n    if (prevDir === dir) {\n      return undefined;\n    }\n  }\n}\n"], "names": ["resolve", "defaultResolver", "path", "enablePackageExports", "blockList", "options", "resolveOptions", "isDirectory", "file", "some", "regex", "test", "directoryExistsSync", "isFile", "fileExistsSync", "preserveSymlinks", "result", "resolveSync", "getPathInModule", "shouldIgnoreRequestForExports", "segments", "split", "moduleName", "shift", "startsWith", "closestPackageJson", "findClosestPackageJson", "basedir", "pkg", "readPackageSync", "readFileSync", "assert", "name", "resolved", "exports", "join", "createResolveOptions", "conditions", "pathResolve", "dirname", "Error", "packageJsonPath", "unsafe", "browser", "require", "isAbsolute", "start", "dir", "pkgJsonFile", "hasPackageJson", "prevDir", "undefined"], "mappings": "AAUA;;;;;AACmB,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACiC,IAAA,KAAM,WAAN,MAAM,CAAA;AACM,IAAA,QAAS,WAAT,SAAS,CAAA;AACrEA,IAAAA,OAAO,mCAAM,iBAAiB,EAAvB;AAEiC,IAAA,IAAoB,WAApB,oBAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuExE,MAAMC,eAAe,GAAG,CACtBC,IAAY,EACZ,EACEC,oBAAoB,CAAA,EACpBC,SAAS,EAAG,EAAE,CAAA,EACd,GAAGC,OAAO,EACuD,GACxD;IACX,mBAAmB;IACnB,MAAMC,cAAc,GAAyC;QAC3D,GAAGD,OAAO;QAEVE,WAAW,EAACC,IAAI,EAAE;YAChB,IAAIJ,SAAS,CAACK,IAAI,CAAC,CAACC,KAAK,GAAKA,KAAK,CAACC,IAAI,CAACH,IAAI,CAAC;YAAA,CAAC,EAAE;gBAC/C,OAAO,KAAK,CAAC;aACd;YACD,OAAOI,CAAAA,GAAAA,IAAmB,AAAM,CAAA,oBAAN,CAACJ,IAAI,CAAC,CAAC;SAClC;QACDK,MAAM,EAACL,IAAI,EAAE;YACX,IAAIJ,SAAS,CAACK,IAAI,CAAC,CAACC,KAAK,GAAKA,KAAK,CAACC,IAAI,CAACH,IAAI,CAAC;YAAA,CAAC,EAAE;gBAC/C,OAAO,KAAK,CAAC;aACd;YACD,OAAOM,CAAAA,GAAAA,IAAc,AAAM,CAAA,eAAN,CAACN,IAAI,CAAC,CAAC;SAC7B;QACDO,gBAAgB,EAAEV,OAAO,CAACU,gBAAgB;QAC1Cd,eAAe;KAChB,AAAC;IAEF,yEAAyE;IACzE,sDAAsD;IACtD,MAAMe,MAAM,GAAGC,CAAAA,GAAAA,QAAW,AAGxB,CAAA,KAHwB,CAACd,oBAAoB,GAAGe,eAAe,CAAChB,IAAI,EAAEI,cAAc,CAAC,GAAGJ,IAAI,EAAE;QAC9F,GAAGI,cAAc;QACjBS,gBAAgB,EAAE,CAACV,OAAO,CAACU,gBAAgB;KAC5C,CAAC,AAAC;IAEH,OAAOC,MAAM,CAAC;CACf,AAAC;eAEaf,eAAe;;AAE9B;;GAEG,CAEH,SAASiB,eAAe,CAAChB,IAAY,EAAEG,OAA6C,EAAU;IAC5F,IAAIc,6BAA6B,CAACjB,IAAI,CAAC,EAAE;QACvC,OAAOA,IAAI,CAAC;KACb;IAED,MAAMkB,QAAQ,GAAGlB,IAAI,CAACmB,KAAK,CAAC,GAAG,CAAC,AAAC;IAEjC,IAAIC,UAAU,GAAGF,QAAQ,CAACG,KAAK,EAAE,AAAC;IAElC,IAAID,UAAU,EAAE;QACd,IAAIA,UAAU,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;YAC9BF,UAAU,GAAG,CAAC,EAAEA,UAAU,CAAC,CAAC,EAAEF,QAAQ,CAACG,KAAK,EAAE,CAAC,CAAC,CAAC;SAClD;QAED,8FAA8F;QAC9F,IAAID,UAAU,KAAK,gBAAgB,EAAE;YACnC,OAAOpB,IAAI,CAAC;SACb;QAED,iBAAiB;QACjB,MAAMuB,kBAAkB,GAAGC,sBAAsB,CAACrB,OAAO,CAACsB,OAAO,EAAEtB,OAAO,CAAC,AAAC;QAC5E,IAAIoB,kBAAkB,EAAE;YACtB,MAAMG,GAAG,GAAGvB,OAAO,CAACwB,eAAe,CAAExB,OAAO,CAACyB,YAAY,EAAGL,kBAAkB,CAAC,AAAC;YAChFM,CAAAA,GAAAA,OAAM,AAAyD,CAAA,QAAzD,CAACH,GAAG,EAAE,kDAAkD,CAAC,CAAC;YAEhE,IAAIA,GAAG,CAACI,IAAI,KAAKV,UAAU,EAAE;gBAC3B,MAAMW,QAAQ,GAAGjC,OAAO,CAACkC,OAAO,CAC9BN,GAAG,EACFR,QAAQ,CAACe,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAC1BC,oBAAoB,CAAC/B,OAAO,CAACgC,UAAU,CAAC,CACzC,AAAC;gBAEF,IAAIJ,QAAQ,EAAE;oBACZ,OAAOK,CAAAA,GAAAA,KAAW,AAA0C,CAAA,QAA1C,CAACC,CAAAA,GAAAA,KAAO,AAAoB,CAAA,QAApB,CAACd,kBAAkB,CAAC,EAAEQ,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC9D;gBAED,IAAIL,GAAG,CAACM,OAAO,EAAE;oBACf,MAAM,IAAIM,KAAK,CACb,uGAAuG,CACxG,CAAC;iBACH;aACF;SACF;QAED,IAAIC,eAAe,GAAG,EAAE,AAAC;QAEzB,IAAI;YACFA,eAAe,GAAGxB,CAAAA,GAAAA,QAAW,AAAuC,CAAA,KAAvC,CAAC,CAAC,EAAEK,UAAU,CAAC,aAAa,CAAC,EAAEjB,OAAO,CAAC,CAAC;SACtE,CAAC,OAAM;QACN,yCAAyC;SAC1C;QAED,IAAIoC,eAAe,IAAIpC,OAAO,CAACQ,MAAM,CAAE4B,eAAe,CAAC,EAAE;YACvD,MAAMb,GAAG,GAAGvB,OAAO,CAACwB,eAAe,CAAExB,OAAO,CAACyB,YAAY,EAAGW,eAAe,CAAC,AAAC;YAC7EV,CAAAA,GAAAA,OAAM,AAAyD,CAAA,QAAzD,CAACH,GAAG,EAAE,kDAAkD,CAAC,CAAC;YAEhE,MAAMK,QAAQ,GAAGjC,OAAO,CAACkC,OAAO,CAC9BN,GAAG,EACFR,QAAQ,CAACe,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAC1BC,oBAAoB,CAAC/B,OAAO,CAACgC,UAAU,CAAC,CACzC,AAAC;YAEF,IAAIJ,QAAQ,EAAE;gBACZ,OAAOK,CAAAA,GAAAA,KAAW,AAAuC,CAAA,QAAvC,CAACC,CAAAA,GAAAA,KAAO,AAAiB,CAAA,QAAjB,CAACE,eAAe,CAAC,EAAER,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3D;YAED,IAAIL,GAAG,CAACM,OAAO,EAAE;gBACf,MAAM,IAAIM,KAAK,CACb,uGAAuG,CACxG,CAAC;aACH;SACF;KACF;IAED,OAAOtC,IAAI,CAAC;CACb;AAED,SAASkC,oBAAoB,CAACC,UAAgC,EAAmB;IAC/E,OAAOA,UAAU,GACb;QAAEA,UAAU;QAAEK,MAAM,EAAE,IAAI;KAAE,GAE5B;QAAEC,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE,IAAI;KAAE,CAAC;CACvC;AAED,6EAA6E;AAC7E,MAAMzB,6BAA6B,GAAG,CAACjB,IAAY,GAAKA,IAAI,CAACsB,UAAU,CAAC,GAAG,CAAC,IAAIqB,CAAAA,GAAAA,KAAU,AAAM,CAAA,WAAN,CAAC3C,IAAI,CAAC;AAAC;AAEjG,eAAe;AACf,+FAA+F;AAC/F,SAASwB,sBAAsB,CAC7BoB,KAAa,EACbzC,OAA+B,EACX;IACpB,IAAI0C,GAAG,GAAGT,CAAAA,GAAAA,KAAW,AAAY,CAAA,QAAZ,CAAC,GAAG,EAAEQ,KAAK,CAAC,AAAC;IAClC,IAAI,CAACzC,OAAO,CAACE,WAAW,CAAEwC,GAAG,CAAC,EAAE;QAC9BA,GAAG,GAAGR,CAAAA,GAAAA,KAAO,AAAK,CAAA,QAAL,CAACQ,GAAG,CAAC,CAAC;KACpB;IAED,MAAO,IAAI,CAAE;QACX,MAAMC,WAAW,GAAGV,CAAAA,GAAAA,KAAW,AAAuB,CAAA,QAAvB,CAACS,GAAG,EAAE,gBAAgB,CAAC,AAAC;QACvD,MAAME,cAAc,GAAG5C,OAAO,CAACQ,MAAM,CAAEmC,WAAW,CAAC,AAAC;QAEpD,IAAIC,cAAc,EAAE;YAClB,OAAOD,WAAW,CAAC;SACpB;QAED,MAAME,OAAO,GAAGH,GAAG,AAAC;QACpBA,GAAG,GAAGR,CAAAA,GAAAA,KAAO,AAAK,CAAA,QAAL,CAACQ,GAAG,CAAC,CAAC;QAEnB,IAAIG,OAAO,KAAKH,GAAG,EAAE;YACnB,OAAOI,SAAS,CAAC;SAClB;KACF;CACF"}