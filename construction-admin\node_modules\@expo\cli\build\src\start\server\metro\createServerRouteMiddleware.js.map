{"version": 3, "sources": ["../../../../../src/start/server/metro/createServerRouteMiddleware.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport type { ProjectConfig } from '@expo/config';\nimport requireString from 'require-from-string';\nimport resolve from 'resolve';\nimport resolveFrom from 'resolve-from';\nimport { promisify } from 'util';\n\nimport { ForwardHtmlError } from './MetroBundlerDevServer';\nimport { bundleApiRoute } from './bundleApiRoutes';\nimport { fetchManifest } from './fetchRouterManifest';\nimport { getErrorOverlayHtmlAsync, logMetroError, logMetroErrorAsync } from './metroErrorInterface';\nimport { warnInvalidWebOutput } from './router';\nimport { Log } from '../../../log';\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')('expo:start:server:metro') as typeof console.log;\n\nconst resolveAsync = promisify(resolve) as any as (\n  id: string,\n  opts: resolve.AsyncOpts\n) => Promise<string | null>;\n\nexport function createRouteHandlerMiddleware(\n  projectRoot: string,\n  options: {\n    mode?: string;\n    appDir: string;\n    routerRoot: string;\n    port?: number;\n    baseUrl: string;\n    getWebBundleUrl: () => string;\n    getStaticPageAsync: (pathname: string) => Promise<{ content: string }>;\n    config: ProjectConfig;\n  }\n) {\n  if (!resolveFrom.silent(projectRoot, 'expo-router')) {\n    throw new CommandError(\n      'static and server rendering requires the expo-router package to be installed in your project.'\n    );\n  }\n\n  const { ExpoResponse } = require('@expo/server') as typeof import('@expo/server');\n  const { createRequestHandler } =\n    require('@expo/server/build/vendor/http') as typeof import('@expo/server/build/vendor/http');\n\n  return createRequestHandler(\n    { build: '' },\n    {\n      async getRoutesManifest() {\n        const manifest = await fetchManifest<RegExp>(projectRoot, options);\n        debug('manifest', manifest);\n        // NOTE: no app dir if null\n        // TODO: Redirect to 404 page\n        return (\n          manifest ?? {\n            // Support the onboarding screen if there's no manifest\n            htmlRoutes: [\n              {\n                file: 'index.js',\n                page: '/index',\n                routeKeys: {},\n                namedRegex: /^\\/(?:index)?\\/?$/i,\n              },\n            ],\n            apiRoutes: [],\n            notFoundRoutes: [],\n          }\n        );\n      },\n      async getHtml(request) {\n        try {\n          const { content } = await options.getStaticPageAsync(request.url);\n          return content;\n        } catch (error: any) {\n          // Forward the Metro server response as-is. It won't be pretty, but at least it will be accurate.\n          if (error instanceof ForwardHtmlError) {\n            return new ExpoResponse(error.html, {\n              status: error.statusCode,\n              headers: {\n                'Content-Type': 'text/html',\n              },\n            });\n          }\n\n          try {\n            return new ExpoResponse(\n              await getErrorOverlayHtmlAsync({\n                error,\n                projectRoot,\n                routerRoot: options.routerRoot,\n              }),\n              {\n                status: 500,\n                headers: {\n                  'Content-Type': 'text/html',\n                },\n              }\n            );\n          } catch (staticError: any) {\n            debug('Failed to render static error overlay:', staticError);\n            // Fallback error for when Expo Router is misconfigured in the project.\n            return new ExpoResponse(\n              '<span><h3>Internal Error:</h3><b>Project is not setup correctly for static rendering (check terminal for more info):</b><br/>' +\n                error.message +\n                '<br/><br/>' +\n                staticError.message +\n                '</span>',\n              {\n                status: 500,\n                headers: {\n                  'Content-Type': 'text/html',\n                },\n              }\n            );\n          }\n        }\n      },\n      logApiRouteExecutionError(error) {\n        logMetroError(projectRoot, { error });\n      },\n      async getApiRoute(route) {\n        const { exp } = options.config;\n        if (exp.web?.output !== 'server') {\n          warnInvalidWebOutput();\n        }\n\n        const resolvedFunctionPath = await resolveAsync(route.page, {\n          extensions: ['.js', '.jsx', '.ts', '.tsx'],\n          basedir: options.appDir,\n        });\n\n        const middlewareContents = await bundleApiRoute(\n          projectRoot,\n          resolvedFunctionPath!,\n          options\n        );\n        if (!middlewareContents) {\n          // TODO: Error handling\n          return null;\n        }\n\n        try {\n          debug(`Bundling middleware at: ${resolvedFunctionPath}`);\n          return requireString(middlewareContents.src, middlewareContents.filename);\n        } catch (error: any) {\n          if (error instanceof Error) {\n            await logMetroErrorAsync({ projectRoot, error });\n          } else {\n            Log.error('Failed to load middleware: ' + error);\n          }\n          return new ExpoResponse(\n            'Failed to load middleware: ' + resolvedFunctionPath + '\\n\\n' + error.message,\n            {\n              status: 500,\n              headers: {\n                'Content-Type': 'text/html',\n              },\n            }\n          );\n        }\n      },\n    }\n  );\n}\n"], "names": ["createRouteHandlerMiddleware", "debug", "require", "resolveAsync", "promisify", "resolve", "projectRoot", "options", "resolveFrom", "silent", "CommandError", "ExpoResponse", "createRequestHandler", "build", "getRoutesManifest", "manifest", "fetchManifest", "htmlRoutes", "file", "page", "routeKeys", "namedRegex", "apiRoutes", "notFoundRoutes", "getHtml", "request", "content", "getStaticPageAsync", "url", "error", "ForwardHtmlError", "html", "status", "statusCode", "headers", "getErrorOverlayHtmlAsync", "routerRoot", "staticError", "message", "logApiRouteExecutionError", "logMetroError", "getApiRoute", "route", "exp", "config", "web", "output", "warnInvalidWebOutput", "resolvedFunctionPath", "extensions", "basedir", "appDir", "middlewareContents", "bundleApiRoute", "requireString", "src", "filename", "Error", "logMetroErrorAsync", "Log"], "mappings": "AAOA;;;;QAqBgBA,4BAA4B,GAA5BA,4BAA4B;AApBlB,IAAA,kBAAqB,kCAArB,qBAAqB,EAAA;AAC3B,IAAA,QAAS,kCAAT,SAAS,EAAA;AACL,IAAA,YAAc,kCAAd,cAAc,EAAA;AACZ,IAAA,KAAM,WAAN,MAAM,CAAA;AAEC,IAAA,sBAAyB,WAAzB,yBAAyB,CAAA;AAC3B,IAAA,gBAAmB,WAAnB,mBAAmB,CAAA;AACpB,IAAA,oBAAuB,WAAvB,uBAAuB,CAAA;AACuB,IAAA,oBAAuB,WAAvB,uBAAuB,CAAA;AAC9D,IAAA,OAAU,WAAV,UAAU,CAAA;AAC3B,IAAA,IAAc,WAAd,cAAc,CAAA;AACL,IAAA,OAAuB,WAAvB,uBAAuB,CAAA;;;;;;AAEpD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,AAAsB,AAAC;AAEhF,MAAMC,YAAY,GAAGC,CAAAA,GAAAA,KAAS,AAAS,CAAA,UAAT,CAACC,QAAO,QAAA,CAAC,AAGZ,AAAC;AAErB,SAASL,4BAA4B,CAC1CM,WAAmB,EACnBC,OASC,EACD;IACA,IAAI,CAACC,YAAW,QAAA,CAACC,MAAM,CAACH,WAAW,EAAE,aAAa,CAAC,EAAE;QACnD,MAAM,IAAII,OAAY,aAAA,CACpB,+FAA+F,CAChG,CAAC;KACH;IAED,MAAM,EAAEC,YAAY,CAAA,EAAE,GAAGT,OAAO,CAAC,cAAc,CAAC,AAAiC,AAAC;IAClF,MAAM,EAAEU,oBAAoB,CAAA,EAAE,GAC5BV,OAAO,CAAC,gCAAgC,CAAC,AAAmD,AAAC;IAE/F,OAAOU,oBAAoB,CACzB;QAAEC,KAAK,EAAE,EAAE;KAAE,EACb;QACE,MAAMC,iBAAiB,IAAG;YACxB,MAAMC,QAAQ,GAAG,MAAMC,CAAAA,GAAAA,oBAAa,AAA8B,CAAA,cAA9B,CAASV,WAAW,EAAEC,OAAO,CAAC,AAAC;YACnEN,KAAK,CAAC,UAAU,EAAEc,QAAQ,CAAC,CAAC;YAC5B,2BAA2B;YAC3B,6BAA6B;YAC7B,OACEA,QAAQ,WAARA,QAAQ,GAAI;gBACV,uDAAuD;gBACvDE,UAAU,EAAE;oBACV;wBACEC,IAAI,EAAE,UAAU;wBAChBC,IAAI,EAAE,QAAQ;wBACdC,SAAS,EAAE,EAAE;wBACbC,UAAU,sBAAsB;qBACjC;iBACF;gBACDC,SAAS,EAAE,EAAE;gBACbC,cAAc,EAAE,EAAE;aACnB,CACD;SACH;QACD,MAAMC,OAAO,EAACC,OAAO,EAAE;YACrB,IAAI;gBACF,MAAM,EAAEC,OAAO,CAAA,EAAE,GAAG,MAAMnB,OAAO,CAACoB,kBAAkB,CAACF,OAAO,CAACG,GAAG,CAAC,AAAC;gBAClE,OAAOF,OAAO,CAAC;aAChB,CAAC,OAAOG,KAAK,EAAO;gBACnB,iGAAiG;gBACjG,IAAIA,KAAK,YAAYC,sBAAgB,iBAAA,EAAE;oBACrC,OAAO,IAAInB,YAAY,CAACkB,KAAK,CAACE,IAAI,EAAE;wBAClCC,MAAM,EAAEH,KAAK,CAACI,UAAU;wBACxBC,OAAO,EAAE;4BACP,cAAc,EAAE,WAAW;yBAC5B;qBACF,CAAC,CAAC;iBACJ;gBAED,IAAI;oBACF,OAAO,IAAIvB,YAAY,CACrB,MAAMwB,CAAAA,GAAAA,oBAAwB,AAI5B,CAAA,yBAJ4B,CAAC;wBAC7BN,KAAK;wBACLvB,WAAW;wBACX8B,UAAU,EAAE7B,OAAO,CAAC6B,UAAU;qBAC/B,CAAC,EACF;wBACEJ,MAAM,EAAE,GAAG;wBACXE,OAAO,EAAE;4BACP,cAAc,EAAE,WAAW;yBAC5B;qBACF,CACF,CAAC;iBACH,CAAC,OAAOG,WAAW,EAAO;oBACzBpC,KAAK,CAAC,wCAAwC,EAAEoC,WAAW,CAAC,CAAC;oBAC7D,uEAAuE;oBACvE,OAAO,IAAI1B,YAAY,CACrB,+HAA+H,GAC7HkB,KAAK,CAACS,OAAO,GACb,YAAY,GACZD,WAAW,CAACC,OAAO,GACnB,SAAS,EACX;wBACEN,MAAM,EAAE,GAAG;wBACXE,OAAO,EAAE;4BACP,cAAc,EAAE,WAAW;yBAC5B;qBACF,CACF,CAAC;iBACH;aACF;SACF;QACDK,yBAAyB,EAACV,KAAK,EAAE;YAC/BW,CAAAA,GAAAA,oBAAa,AAAwB,CAAA,cAAxB,CAAClC,WAAW,EAAE;gBAAEuB,KAAK;aAAE,CAAC,CAAC;SACvC;QACD,MAAMY,WAAW,EAACC,KAAK,EAAE;gBAEnBC,GAAO;YADX,MAAM,EAAEA,GAAG,CAAA,EAAE,GAAGpC,OAAO,CAACqC,MAAM,AAAC;YAC/B,IAAID,CAAAA,CAAAA,GAAO,GAAPA,GAAG,CAACE,GAAG,SAAQ,GAAfF,KAAAA,CAAe,GAAfA,GAAO,CAAEG,MAAM,CAAA,KAAK,QAAQ,EAAE;gBAChCC,CAAAA,GAAAA,OAAoB,AAAE,CAAA,qBAAF,EAAE,CAAC;aACxB;YAED,MAAMC,oBAAoB,GAAG,MAAM7C,YAAY,CAACuC,KAAK,CAACvB,IAAI,EAAE;gBAC1D8B,UAAU,EAAE;oBAAC,KAAK;oBAAE,MAAM;oBAAE,KAAK;oBAAE,MAAM;iBAAC;gBAC1CC,OAAO,EAAE3C,OAAO,CAAC4C,MAAM;aACxB,CAAC,AAAC;YAEH,MAAMC,kBAAkB,GAAG,MAAMC,CAAAA,GAAAA,gBAAc,AAI9C,CAAA,eAJ8C,CAC7C/C,WAAW,EACX0C,oBAAoB,EACpBzC,OAAO,CACR,AAAC;YACF,IAAI,CAAC6C,kBAAkB,EAAE;gBACvB,uBAAuB;gBACvB,OAAO,IAAI,CAAC;aACb;YAED,IAAI;gBACFnD,KAAK,CAAC,CAAC,wBAAwB,EAAE+C,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBACzD,OAAOM,CAAAA,GAAAA,kBAAa,AAAqD,CAAA,QAArD,CAACF,kBAAkB,CAACG,GAAG,EAAEH,kBAAkB,CAACI,QAAQ,CAAC,CAAC;aAC3E,CAAC,OAAO3B,KAAK,EAAO;gBACnB,IAAIA,KAAK,YAAY4B,KAAK,EAAE;oBAC1B,MAAMC,CAAAA,GAAAA,oBAAkB,AAAwB,CAAA,mBAAxB,CAAC;wBAAEpD,WAAW;wBAAEuB,KAAK;qBAAE,CAAC,CAAC;iBAClD,MAAM;oBACL8B,IAAG,IAAA,CAAC9B,KAAK,CAAC,6BAA6B,GAAGA,KAAK,CAAC,CAAC;iBAClD;gBACD,OAAO,IAAIlB,YAAY,CACrB,6BAA6B,GAAGqC,oBAAoB,GAAG,MAAM,GAAGnB,KAAK,CAACS,OAAO,EAC7E;oBACEN,MAAM,EAAE,GAAG;oBACXE,OAAO,EAAE;wBACP,cAAc,EAAE,WAAW;qBAC5B;iBACF,CACF,CAAC;aACH;SACF;KACF,CACF,CAAC;CACH"}