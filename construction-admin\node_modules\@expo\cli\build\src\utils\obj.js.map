{"version": 3, "sources": ["../../../src/utils/obj.ts"], "sourcesContent": ["/** `lodash.get` */\nexport function get(obj: any, key: string): any | null {\n  const branches = key.split('.');\n  let current: any = obj;\n  let branch: string | undefined;\n  while ((branch = branches.shift())) {\n    if (!(branch in current)) {\n      return null;\n    }\n    current = current[branch];\n  }\n  return current;\n}\n\n/** `lodash.set` */\nexport function set(obj: any, key: string, value: any): any | null {\n  const branches = key.split('.');\n  let current: any = obj;\n  let branch: string | undefined;\n  while ((branch = branches.shift())) {\n    if (branches.length === 0) {\n      current[branch] = value;\n      return obj;\n    }\n\n    if (!(branch in current)) {\n      current[branch] = {};\n    }\n\n    current = current[branch];\n  }\n  return null;\n}\n\n/** `lodash.pickBy` */\nexport function pickBy<T>(\n  obj: { [key: string]: T },\n  predicate: (value: T, key: string) => boolean | undefined\n) {\n  return Object.entries(obj).reduce(\n    (acc, [key, value]) => {\n      if (predicate(value, key)) {\n        acc[key] = value;\n      }\n      return acc;\n    },\n    {} as { [key: string]: T }\n  );\n}\n"], "names": ["get", "set", "pickBy", "obj", "key", "branches", "split", "current", "branch", "shift", "value", "length", "predicate", "Object", "entries", "reduce", "acc"], "mappings": "AACA;;;;QAAgBA,GAAG,GAAHA,GAAG;QAcHC,GAAG,GAAHA,GAAG;QAoBHC,MAAM,GAANA,MAAM;AAlCf,SAASF,GAAG,CAACG,GAAQ,EAAEC,GAAW,EAAc;IACrD,MAAMC,QAAQ,GAAGD,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC,AAAC;IAChC,IAAIC,OAAO,GAAQJ,GAAG,AAAC;IACvB,IAAIK,MAAM,AAAoB,AAAC;IAC/B,MAAQA,MAAM,GAAGH,QAAQ,CAACI,KAAK,EAAE,CAAG;QAClC,IAAI,CAAC,CAACD,MAAM,IAAID,OAAO,CAAC,EAAE;YACxB,OAAO,IAAI,CAAC;SACb;QACDA,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,CAAC;KAC3B;IACD,OAAOD,OAAO,CAAC;CAChB;AAGM,SAASN,GAAG,CAACE,GAAQ,EAAEC,GAAW,EAAEM,KAAU,EAAc;IACjE,MAAML,QAAQ,GAAGD,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC,AAAC;IAChC,IAAIC,OAAO,GAAQJ,GAAG,AAAC;IACvB,IAAIK,MAAM,AAAoB,AAAC;IAC/B,MAAQA,MAAM,GAAGH,QAAQ,CAACI,KAAK,EAAE,CAAG;QAClC,IAAIJ,QAAQ,CAACM,MAAM,KAAK,CAAC,EAAE;YACzBJ,OAAO,CAACC,MAAM,CAAC,GAAGE,KAAK,CAAC;YACxB,OAAOP,GAAG,CAAC;SACZ;QAED,IAAI,CAAC,CAACK,MAAM,IAAID,OAAO,CAAC,EAAE;YACxBA,OAAO,CAACC,MAAM,CAAC,GAAG,EAAE,CAAC;SACtB;QAEDD,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,CAAC;KAC3B;IACD,OAAO,IAAI,CAAC;CACb;AAGM,SAASN,MAAM,CACpBC,GAAyB,EACzBS,SAAyD,EACzD;IACA,OAAOC,MAAM,CAACC,OAAO,CAACX,GAAG,CAAC,CAACY,MAAM,CAC/B,CAACC,GAAG,EAAE,CAACZ,GAAG,EAAEM,KAAK,CAAC,GAAK;QACrB,IAAIE,SAAS,CAACF,KAAK,EAAEN,GAAG,CAAC,EAAE;YACzBY,GAAG,CAACZ,GAAG,CAAC,GAAGM,KAAK,CAAC;SAClB;QACD,OAAOM,GAAG,CAAC;KACZ,EACD,EAAE,CACH,CAAC;CACH"}