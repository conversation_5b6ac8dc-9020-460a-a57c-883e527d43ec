{"version": 3, "sources": ["../../../../../../src/start/server/middleware/inspector/LaunchBrowserImplWindows.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport open from 'open';\nimport path from 'path';\n\nimport {\n  LaunchBrowserTypes,\n  type LaunchBrowser,\n  type LaunchBrowserInstance,\n  LaunchBrowserTypesEnum,\n} from './LaunchBrowser.types';\n\nconst IS_WSL = require('is-wsl') && !require('is-docker')();\n\n/**\n * Browser implementation for Windows and WSL\n *\n * To minimize the difference between Windows and WSL, the implementation wraps all spawn calls through powershell.\n */\nexport default class LaunchBrowserImplWindows implements LaunchBrowser, LaunchBrowserInstance {\n  private _appId: string | undefined;\n  private _powershellEnv: { [key: string]: string } | undefined;\n\n  MAP = {\n    [LaunchBrowserTypesEnum.CHROME]: {\n      appId: 'chrome',\n    },\n    [LaunchBrowserTypesEnum.EDGE]: {\n      appId: 'msedge',\n    },\n    [LaunchBrowserTypesEnum.BRAVE]: {\n      appId: 'brave',\n    },\n  };\n\n  async isSupportedBrowser(browserType: LaunchBrowserTypes): Promise<boolean> {\n    let result = false;\n    try {\n      const env = await this.getPowershellEnv();\n      const { status } = await spawnAsync(\n        'powershell.exe',\n        ['-c', `Get-Package -Name '${browserType}'`],\n        {\n          // @ts-expect-error: Missing NODE_ENV\n          env,\n          stdio: 'ignore',\n        }\n      );\n      result = status === 0;\n    } catch {\n      result = false;\n    }\n    return result;\n  }\n\n  async createTempBrowserDir(baseDirName: string) {\n    let tmpDir;\n    if (IS_WSL) {\n      // On WSL, the browser is actually launched in host, the `temp-dir` returns the linux /tmp path where host browsers cannot reach into.\n      // We should get the temp path through the `$TEMP` windows environment variable.\n      tmpDir = (await spawnAsync('powershell.exe', ['-c', 'echo \"$Env:TEMP\"'])).stdout.trim();\n      return `${tmpDir}\\\\${baseDirName}`;\n    } else {\n      tmpDir = require('temp-dir');\n      return path.join(tmpDir, baseDirName);\n    }\n  }\n\n  async launchAsync(\n    browserType: LaunchBrowserTypes,\n    args: string[]\n  ): Promise<LaunchBrowserInstance> {\n    const appId = this.MAP[browserType].appId;\n    await openWithSystemRootEnvironment(appId, { arguments: args });\n    this._appId = appId;\n    return this;\n  }\n\n  async close(): Promise<void> {\n    if (this._appId != null) {\n      try {\n        // Since we wrap all spawn calls through powershell as well as from `open.openApp`, the returned ChildProcess is not the browser process.\n        // And we cannot just call `process.kill()` kill it.\n        // The implementation tries to find the pid of target chromium browser process (with --app=https://chrome-devtools-frontend.appspot.com in command arguments),\n        // and uses taskkill to terminate the process.\n        const env = await this.getPowershellEnv();\n        await spawnAsync(\n          'powershell.exe',\n          [\n            '-c',\n            `taskkill.exe /pid @(Get-WmiObject Win32_Process -Filter \"name = '${this._appId}.exe' AND CommandLine LIKE '%chrome-devtools-frontend.appspot.com%'\" | Select-Object -ExpandProperty ProcessId)`,\n          ],\n          {\n            // @ts-expect-error: Missing NODE_ENV\n            env,\n            stdio: 'ignore',\n          }\n        );\n      } catch {}\n      this._appId = undefined;\n    }\n  }\n\n  /**\n   * This method is used to get the powershell environment variables for `Get-Package` command.\n   * Especially for powershell 7, its default `PSModulePath` is different from powershell 5 and `Get-Package` command is not available.\n   * We need to set the PSModulePath to include the default value of powershell 5.\n   */\n  private async getPowershellEnv(): Promise<{ [key: string]: string }> {\n    if (this._powershellEnv) {\n      return this._powershellEnv;\n    }\n    const PSModulePath = (\n      await spawnAsync('powershell.exe', ['-c', 'echo \"$PSHOME\\\\Modules\"'])\n    ).stdout.trim();\n    this._powershellEnv = {\n      PSModulePath,\n    };\n    return this._powershellEnv;\n  }\n}\n\n/**\n * Due to a bug in `open` on Windows PowerShell, we need to ensure `process.env.SYSTEMROOT` is set.\n * This environment variable is set by Windows on `SystemRoot`, causing `open` to execute a command with an \"unknown\" drive letter.\n *\n * @see https://github.com/sindresorhus/open/issues/205\n */\nasync function openWithSystemRootEnvironment(\n  appId: string | Readonly<string[]>,\n  options?: open.OpenAppOptions\n): Promise<import('child_process').ChildProcess> {\n  const oldSystemRoot = process.env.SYSTEMROOT;\n  try {\n    process.env.SYSTEMROOT = process.env.SYSTEMROOT ?? process.env.SystemRoot;\n    return await open.openApp(appId, options);\n  } finally {\n    process.env.SYSTEMROOT = oldSystemRoot;\n  }\n}\n"], "names": ["LaunchBrowserImplWindows", "MAP", "LaunchBrowserTypesEnum", "CHROME", "appId", "EDGE", "BRAVE", "isSupportedBrowser", "browserType", "result", "env", "getPowershellEnv", "status", "spawnAsync", "stdio", "createTempBrowserDir", "baseDirName", "tmpDir", "IS_WSL", "stdout", "trim", "require", "path", "join", "launchAsync", "args", "openWithSystemRootEnvironment", "arguments", "_appId", "close", "undefined", "_powershellEnv", "PSModulePath", "options", "oldSystemRoot", "process", "SYSTEMROOT", "SystemRoot", "open", "openApp"], "mappings": "AAAA;;;;;AAAuB,IAAA,WAAmB,kCAAnB,mBAAmB,EAAA;AACzB,IAAA,KAAM,kCAAN,MAAM,EAAA;AACN,IAAA,KAAM,kCAAN,MAAM,EAAA;AAOhB,IAAA,mBAAuB,WAAvB,uBAAuB,CAAA;AASf,MAAMA,wBAAwB;IAI3CC,GAAG,GAAG;QACJ,CAACC,mBAAsB,uBAAA,CAACC,MAAM,CAAC,EAAE;YAC/BC,KAAK,EAAE,QAAQ;SAChB;QACD,CAACF,mBAAsB,uBAAA,CAACG,IAAI,CAAC,EAAE;YAC7BD,KAAK,EAAE,QAAQ;SAChB;QACD,CAACF,mBAAsB,uBAAA,CAACI,KAAK,CAAC,EAAE;YAC9BF,KAAK,EAAE,OAAO;SACf;KACF,CAAC;IAEF,MAAMG,kBAAkB,CAACC,WAA+B,EAAoB;QAC1E,IAAIC,MAAM,GAAG,KAAK,AAAC;QACnB,IAAI;YACF,MAAMC,GAAG,GAAG,MAAM,IAAI,CAACC,gBAAgB,EAAE,AAAC;YAC1C,MAAM,EAAEC,MAAM,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,WAAU,AAQlC,CAAA,QARkC,CACjC,gBAAgB,EAChB;gBAAC,IAAI;gBAAE,CAAC,mBAAmB,EAAEL,WAAW,CAAC,CAAC,CAAC;aAAC,EAC5C;gBACE,qCAAqC;gBACrCE,GAAG;gBACHI,KAAK,EAAE,QAAQ;aAChB,CACF,AAAC;YACFL,MAAM,GAAGG,MAAM,KAAK,CAAC,CAAC;SACvB,CAAC,OAAM;YACNH,MAAM,GAAG,KAAK,CAAC;SAChB;QACD,OAAOA,MAAM,CAAC;KACf;IAED,MAAMM,oBAAoB,CAACC,WAAmB,EAAE;QAC9C,IAAIC,MAAM,AAAC;QACX,IAAIC,MAAM,EAAE;YACV,sIAAsI;YACtI,gFAAgF;YAChFD,MAAM,GAAG,CAAC,MAAMJ,CAAAA,GAAAA,WAAU,AAA8C,CAAA,QAA9C,CAAC,gBAAgB,EAAE;gBAAC,IAAI;gBAAE,kBAAkB;aAAC,CAAC,CAAC,CAACM,MAAM,CAACC,IAAI,EAAE,CAAC;YACxF,OAAO,CAAC,EAAEH,MAAM,CAAC,EAAE,EAAED,WAAW,CAAC,CAAC,CAAC;SACpC,MAAM;YACLC,MAAM,GAAGI,OAAO,CAAC,UAAU,CAAC,CAAC;YAC7B,OAAOC,KAAI,QAAA,CAACC,IAAI,CAACN,MAAM,EAAED,WAAW,CAAC,CAAC;SACvC;KACF;IAED,MAAMQ,WAAW,CACfhB,WAA+B,EAC/BiB,IAAc,EACkB;QAChC,MAAMrB,KAAK,GAAG,IAAI,CAACH,GAAG,CAACO,WAAW,CAAC,CAACJ,KAAK,AAAC;QAC1C,MAAMsB,6BAA6B,CAACtB,KAAK,EAAE;YAAEuB,SAAS,EAAEF,IAAI;SAAE,CAAC,CAAC;QAChE,IAAI,CAACG,MAAM,GAAGxB,KAAK,CAAC;QACpB,OAAO,IAAI,CAAC;KACb;IAED,MAAMyB,KAAK,GAAkB;QAC3B,IAAI,IAAI,CAACD,MAAM,IAAI,IAAI,EAAE;YACvB,IAAI;gBACF,yIAAyI;gBACzI,oDAAoD;gBACpD,8JAA8J;gBAC9J,8CAA8C;gBAC9C,MAAMlB,GAAG,GAAG,MAAM,IAAI,CAACC,gBAAgB,EAAE,AAAC;gBAC1C,MAAME,CAAAA,GAAAA,WAAU,AAWf,CAAA,QAXe,CACd,gBAAgB,EAChB;oBACE,IAAI;oBACJ,CAAC,iEAAiE,EAAE,IAAI,CAACe,MAAM,CAAC,+GAA+G,CAAC;iBACjM,EACD;oBACE,qCAAqC;oBACrClB,GAAG;oBACHI,KAAK,EAAE,QAAQ;iBAChB,CACF,CAAC;aACH,CAAC,OAAM,EAAE;YACV,IAAI,CAACc,MAAM,GAAGE,SAAS,CAAC;SACzB;KACF;IAED;;;;KAIG,CACH,MAAcnB,gBAAgB,GAAuC;QACnE,IAAI,IAAI,CAACoB,cAAc,EAAE;YACvB,OAAO,IAAI,CAACA,cAAc,CAAC;SAC5B;QACD,MAAMC,YAAY,GAAG,CACnB,MAAMnB,CAAAA,GAAAA,WAAU,AAAqD,CAAA,QAArD,CAAC,gBAAgB,EAAE;YAAC,IAAI;YAAE,yBAAyB;SAAC,CAAC,CACtE,CAACM,MAAM,CAACC,IAAI,EAAE,AAAC;QAChB,IAAI,CAACW,cAAc,GAAG;YACpBC,YAAY;SACb,CAAC;QACF,OAAO,IAAI,CAACD,cAAc,CAAC;KAC5B;CACF;kBArGoB/B,wBAAwB;;;;;;AAP7C,MAAMkB,MAAM,GAAGG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAACA,OAAO,CAAC,WAAW,CAAC,EAAE,AAAC;AA8G5D;;;;;GAKG,CACH,eAAeK,6BAA6B,CAC1CtB,KAAkC,EAClC6B,OAA6B,EACkB;IAC/C,MAAMC,aAAa,GAAGC,OAAO,CAACzB,GAAG,CAAC0B,UAAU,AAAC;IAC7C,IAAI;YACuBD,WAAsB;QAA/CA,OAAO,CAACzB,GAAG,CAAC0B,UAAU,GAAGD,CAAAA,WAAsB,GAAtBA,OAAO,CAACzB,GAAG,CAAC0B,UAAU,YAAtBD,WAAsB,GAAIA,OAAO,CAACzB,GAAG,CAAC2B,UAAU,CAAC;QAC1E,OAAO,MAAMC,KAAI,QAAA,CAACC,OAAO,CAACnC,KAAK,EAAE6B,OAAO,CAAC,CAAC;KAC3C,QAAS;QACRE,OAAO,CAACzB,GAAG,CAAC0B,UAAU,GAAGF,aAAa,CAAC;KACxC;CACF"}