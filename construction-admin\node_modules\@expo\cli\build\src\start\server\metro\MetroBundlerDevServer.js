"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getDeepLinkHandler = getDeepLinkHandler;
var _config = require("@expo/config");
var runtimeEnv = _interopRequireWildcard(require("@expo/env"));
var _chalk = _interopRequireDefault(require("chalk"));
var _nodeFetch = _interopRequireDefault(require("node-fetch"));
var _path = _interopRequireDefault(require("path"));
var _bundleApiRoutes = require("./bundleApiRoutes");
var _createServerRouteMiddleware = require("./createServerRouteMiddleware");
var _fetchRouterManifest = require("./fetchRouterManifest");
var _instantiateMetro = require("./instantiateMetro");
var _metroWatchTypeScriptFiles = require("./metroWatchTypeScriptFiles");
var _router = require("./router");
var _serializeHtml = require("./serializeHtml");
var _waitForMetroToObserveTypeScriptFile = require("./waitForMetroToObserveTypeScriptFile");
var _log = require("../../../log");
var _getDevClientProperties = _interopRequireDefault(require("../../../utils/analytics/getDevClientProperties"));
var _rudderstackClient = require("../../../utils/analytics/rudderstackClient");
var _errors = require("../../../utils/errors");
var _port = require("../../../utils/port");
var _bundlerDevServer = require("../BundlerDevServer");
var _getStaticRenderFunctions = require("../getStaticRenderFunctions");
var _contextModuleSourceMapsMiddleware = require("../middleware/ContextModuleSourceMapsMiddleware");
var _createFileMiddleware = require("../middleware/CreateFileMiddleware");
var _devToolsPluginMiddleware = require("../middleware/DevToolsPluginMiddleware");
var _faviconMiddleware = require("../middleware/FaviconMiddleware");
var _historyFallbackMiddleware = require("../middleware/HistoryFallbackMiddleware");
var _interstitialPageMiddleware = require("../middleware/InterstitialPageMiddleware");
var _manifestMiddleware = require("../middleware/ManifestMiddleware");
var _reactDevToolsPageMiddleware = require("../middleware/ReactDevToolsPageMiddleware");
var _runtimeRedirectMiddleware = require("../middleware/RuntimeRedirectMiddleware");
var _serveStaticMiddleware = require("../middleware/ServeStaticMiddleware");
var _metroOptions = require("../middleware/metroOptions");
var _mutations = require("../middleware/mutations");
var _startTypescriptTypeGeneration = require("../type-generation/startTypescriptTypeGeneration");
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _interopRequireWildcard(obj) {
    if (obj && obj.__esModule) {
        return obj;
    } else {
        var newObj = {};
        if (obj != null) {
            for(var key in obj){
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {};
                    if (desc.get || desc.set) {
                        Object.defineProperty(newObj, key, desc);
                    } else {
                        newObj[key] = obj[key];
                    }
                }
            }
        }
        newObj.default = obj;
        return newObj;
    }
}
class ForwardHtmlError extends _errors.CommandError {
    constructor(message, html, statusCode){
        super(message);
        this.html = html;
        this.statusCode = statusCode;
    }
}
exports.ForwardHtmlError = ForwardHtmlError;
const debug = require("debug")("expo:start:server:metro");
/** Default port to use for apps running in Expo Go. */ const EXPO_GO_METRO_PORT = 8081;
/** Default port to use for apps that run in standard React Native projects or Expo Dev Clients. */ const DEV_CLIENT_METRO_PORT = 8081;
class MetroBundlerDevServer extends _bundlerDevServer.BundlerDevServer {
    metro = null;
    get name() {
        return "metro";
    }
    async resolvePortAsync(options = {}) {
        var // If the manually defined port is busy then an error should be thrown...
        _port1;
        const port = (_port1 = options.port) != null ? _port1 : // Otherwise use the default port based on the runtime target.
        (options.devClient ? Number(process.env.RCT_METRO_PORT) || DEV_CLIENT_METRO_PORT : await (0, _port).getFreePortAsync(EXPO_GO_METRO_PORT));
        return port;
    }
    async exportExpoRouterApiRoutesAsync({ mode , outputDir , prerenderManifest , baseUrl , routerRoot  }) {
        const appDir = _path.default.join(this.projectRoot, routerRoot);
        const manifest = await this.getExpoRouterRoutesManifestAsync({
            appDir
        });
        const files = new Map();
        for (const route of manifest.apiRoutes){
            var ref;
            const filepath = _path.default.join(appDir, route.file);
            const contents = await (0, _bundleApiRoutes).bundleApiRoute(this.projectRoot, filepath, {
                mode,
                routerRoot,
                port: (ref = this.getInstance()) == null ? void 0 : ref.location.port,
                shouldThrow: true,
                baseUrl
            });
            const artifactFilename = _path.default.join(outputDir, _path.default.relative(appDir, filepath.replace(/\.[tj]sx?$/, ".js")));
            if (contents) {
                files.set(artifactFilename, {
                    contents: contents.src,
                    targetDomain: "server"
                });
            }
            // Remap the manifest files to represent the output files.
            route.file = artifactFilename;
        }
        return {
            manifest: {
                ...manifest,
                htmlRoutes: prerenderManifest.htmlRoutes
            },
            files
        };
    }
    async getExpoRouterRoutesManifestAsync({ appDir  }) {
        // getBuiltTimeServerManifest
        const manifest = await (0, _fetchRouterManifest).fetchManifest(this.projectRoot, {
            asJson: true,
            appDir
        });
        if (!manifest) {
            throw new _errors.CommandError("EXPO_ROUTER_SERVER_MANIFEST", "Unexpected error: server manifest could not be fetched.");
        }
        return manifest;
    }
    async getStaticRenderFunctionAsync({ mode , minify =mode !== "development" , baseUrl , routerRoot  }) {
        const url = this.getDevServerUrl();
        const { getStaticContent , getManifest , getBuildTimeServerManifestAsync  } = await (0, _getStaticRenderFunctions).getStaticRenderFunctions(this.projectRoot, url, {
            minify,
            dev: mode !== "production",
            // Ensure the API Routes are included
            environment: "node",
            baseUrl,
            routerRoot
        });
        return {
            serverManifest: await getBuildTimeServerManifestAsync(),
            // Get routes from Expo Router.
            manifest: await getManifest({
                fetchData: true,
                preserveApiRoutes: false
            }),
            // Get route generating function
            async renderAsync (path) {
                return await getStaticContent(new URL(path, url));
            }
        };
    }
    async getStaticResourcesAsync({ mode , minify =mode !== "development" , includeSourceMaps , baseUrl , mainModuleName , isExporting , asyncRoutes , routerRoot  }) {
        var ref;
        const devBundleUrlPathname = (0, _metroOptions).createBundleUrlPath({
            platform: "web",
            mode,
            minify,
            environment: "client",
            serializerOutput: "static",
            serializerIncludeMaps: includeSourceMaps,
            mainModuleName: mainModuleName != null ? mainModuleName : (0, _manifestMiddleware).resolveMainModuleName(this.projectRoot, {
                platform: "web"
            }),
            lazy: (0, _metroOptions).shouldEnableAsyncImports(this.projectRoot),
            asyncRoutes,
            baseUrl,
            isExporting,
            routerRoot,
            bytecode: false
        });
        const bundleUrl = new URL(devBundleUrlPathname, this.getDevServerUrl());
        // Fetch the generated HTML from our custom Metro serializer
        const results = await (0, _nodeFetch).default(bundleUrl.toString());
        const txt = await results.text();
        let data;
        try {
            data = JSON.parse(txt);
        } catch (error) {
            debug(txt);
            // Metro can throw this error when the initial module id cannot be resolved.
            if (!results.ok && txt.startsWith("<!DOCTYPE html>")) {
                throw new ForwardHtmlError(`Metro failed to bundle the project. Check the console for more information.`, txt, results.status);
            }
            _log.Log.error("Failed to generate resources with Metro, the Metro config may not be using the correct serializer. Ensure the metro.config.js is extending the expo/metro-config and is not overriding the serializer.");
            throw error;
        }
        // NOTE: This could potentially need more validation in the future.
        if ("artifacts" in data && Array.isArray(data.artifacts)) {
            return data;
        }
        if (data != null && (data.errors || ((ref = data.type) == null ? void 0 : ref.match(/.*Error$/)))) {
            // {
            //   type: 'InternalError',
            //   errors: [],
            //   message: 'Metro has encountered an error: While trying to resolve module `stylis` from file `/Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/@emotion/cache/dist/emotion-cache.browser.esm.js`, the package `/Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/stylis/package.json` was successfully found. However, this package itself specifies a `main` module field that could not be resolved (`/Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/stylis/dist/stylis.mjs`. Indeed, none of these files exist:\n' +
            //     '\n' +
            //     '  * /Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/stylis/dist/stylis.mjs(.web.ts|.ts|.web.tsx|.tsx|.web.js|.js|.web.jsx|.jsx|.web.json|.json|.web.cjs|.cjs|.web.scss|.scss|.web.sass|.sass|.web.css|.css)\n' +
            //     '  * /Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/stylis/dist/stylis.mjs/index(.web.ts|.ts|.web.tsx|.tsx|.web.js|.js|.web.jsx|.jsx|.web.json|.json|.web.cjs|.cjs|.web.scss|.scss|.web.sass|.sass|.web.css|.css): /Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/metro/src/node-haste/DependencyGraph.js (289:17)\n' +
            //     '\n' +
            //     '\x1B[0m \x1B[90m 287 |\x1B[39m         }\x1B[0m\n' +
            //     '\x1B[0m \x1B[90m 288 |\x1B[39m         \x1B[36mif\x1B[39m (error \x1B[36minstanceof\x1B[39m \x1B[33mInvalidPackageError\x1B[39m) {\x1B[0m\n' +
            //     '\x1B[0m\x1B[31m\x1B[1m>\x1B[22m\x1B[39m\x1B[90m 289 |\x1B[39m           \x1B[36mthrow\x1B[39m \x1B[36mnew\x1B[39m \x1B[33mPackageResolutionError\x1B[39m({\x1B[0m\n' +
            //     '\x1B[0m \x1B[90m     |\x1B[39m                 \x1B[31m\x1B[1m^\x1B[22m\x1B[39m\x1B[0m\n' +
            //     '\x1B[0m \x1B[90m 290 |\x1B[39m             packageError\x1B[33m:\x1B[39m error\x1B[33m,\x1B[39m\x1B[0m\n' +
            //     '\x1B[0m \x1B[90m 291 |\x1B[39m             originModulePath\x1B[33m:\x1B[39m \x1B[36mfrom\x1B[39m\x1B[33m,\x1B[39m\x1B[0m\n' +
            //     '\x1B[0m \x1B[90m 292 |\x1B[39m             targetModuleName\x1B[33m:\x1B[39m to\x1B[33m,\x1B[39m\x1B[0m'
            // }
            // The Metro logger already showed this error.
            throw new Error(data.message);
        }
        throw new Error("Invalid resources returned from the Metro serializer. Expected array, found: " + data);
    }
    async getStaticPageAsync(pathname, { mode , minify =mode !== "development" , baseUrl , routerRoot , isExporting , asyncRoutes  }) {
        const devBundleUrlPathname = (0, _metroOptions).createBundleUrlPath({
            platform: "web",
            mode,
            environment: "client",
            mainModuleName: (0, _manifestMiddleware).resolveMainModuleName(this.projectRoot, {
                platform: "web"
            }),
            lazy: (0, _metroOptions).shouldEnableAsyncImports(this.projectRoot),
            baseUrl,
            isExporting,
            asyncRoutes,
            routerRoot,
            bytecode: false
        });
        const bundleStaticHtml = async ()=>{
            const { getStaticContent  } = await (0, _getStaticRenderFunctions).getStaticRenderFunctions(this.projectRoot, this.getDevServerUrl(), {
                minify: false,
                dev: mode !== "production",
                // Ensure the API Routes are included
                environment: "node",
                baseUrl,
                routerRoot
            });
            const location = new URL(pathname, this.getDevServerUrl());
            return await getStaticContent(location);
        };
        const [{ artifacts: resources  }, staticHtml] = await Promise.all([
            this.getStaticResourcesAsync({
                isExporting,
                mode,
                minify,
                baseUrl,
                asyncRoutes,
                routerRoot
            }),
            bundleStaticHtml(), 
        ]);
        const content = (0, _serializeHtml).serializeHtmlWithAssets({
            mode,
            resources,
            template: staticHtml,
            devBundleUrl: devBundleUrlPathname,
            baseUrl
        });
        return {
            content,
            resources
        };
    }
    async watchEnvironmentVariables() {
        if (!this.instance) {
            throw new Error("Cannot observe environment variable changes without a running Metro instance.");
        }
        if (!this.metro) {
            // This can happen when the run command is used and the server is already running in another
            // process.
            debug("Skipping Environment Variable observation because Metro is not running (headless).");
            return;
        }
        const envFiles = runtimeEnv.getFiles(process.env.NODE_ENV).map((fileName)=>_path.default.join(this.projectRoot, fileName)
        );
        (0, _waitForMetroToObserveTypeScriptFile).observeFileChanges({
            metro: this.metro,
            server: this.instance.server
        }, envFiles, ()=>{
            debug("Reloading environment variables...");
            // Force reload the environment variables.
            runtimeEnv.load(this.projectRoot, {
                force: true
            });
        });
    }
    async startImplementationAsync(options) {
        options.port = await this.resolvePortAsync(options);
        this.urlCreator = this.getUrlCreator(options);
        const parsedOptions = {
            port: options.port,
            maxWorkers: options.maxWorkers,
            resetCache: options.resetDevServer
        };
        // Required for symbolication:
        process.env.EXPO_DEV_SERVER_ORIGIN = `http://localhost:${options.port}`;
        const { metro , server , middleware , messageSocket  } = await (0, _instantiateMetro).instantiateMetroAsync(this, parsedOptions, {
            isExporting: !!options.isExporting
        });
        const manifestMiddleware = await this.getManifestMiddlewareAsync(options);
        // Important that we noop source maps for context modules as soon as possible.
        (0, _mutations).prependMiddleware(middleware, new _contextModuleSourceMapsMiddleware.ContextModuleSourceMapsMiddleware().getHandler());
        // We need the manifest handler to be the first middleware to run so our
        // routes take precedence over static files. For example, the manifest is
        // served from '/' and if the user has an index.html file in their project
        // then the manifest handler will never run, the static middleware will run
        // and serve index.html instead of the manifest.
        // https://github.com/expo/expo/issues/13114
        (0, _mutations).prependMiddleware(middleware, manifestMiddleware.getHandler());
        var _scheme;
        middleware.use(new _interstitialPageMiddleware.InterstitialPageMiddleware(this.projectRoot, {
            // TODO: Prevent this from becoming stale.
            scheme: (_scheme = options.location.scheme) != null ? _scheme : null
        }).getHandler());
        middleware.use(new _reactDevToolsPageMiddleware.ReactDevToolsPageMiddleware(this.projectRoot).getHandler());
        middleware.use(new _devToolsPluginMiddleware.DevToolsPluginMiddleware(this.projectRoot, this.devToolsPluginManager).getHandler());
        const deepLinkMiddleware = new _runtimeRedirectMiddleware.RuntimeRedirectMiddleware(this.projectRoot, {
            onDeepLink: getDeepLinkHandler(this.projectRoot),
            getLocation: ({ runtime  })=>{
                if (runtime === "custom") {
                    var ref;
                    return (ref = this.urlCreator) == null ? void 0 : ref.constructDevClientUrl();
                } else {
                    var ref2;
                    return (ref2 = this.urlCreator) == null ? void 0 : ref2.constructUrl({
                        scheme: "exp"
                    });
                }
            }
        });
        middleware.use(deepLinkMiddleware.getHandler());
        middleware.use(new _createFileMiddleware.CreateFileMiddleware(this.projectRoot).getHandler());
        // Append support for redirecting unhandled requests to the index.html page on web.
        if (this.isTargetingWeb()) {
            var ref3;
            const config = (0, _config).getConfig(this.projectRoot, {
                skipSDKVersionRequirement: true
            });
            const { exp  } = config;
            var ref1;
            const useServerRendering = [
                "static",
                "server"
            ].includes((ref1 = (ref3 = exp.web) == null ? void 0 : ref3.output) != null ? ref1 : "");
            // This MUST be after the manifest middleware so it doesn't have a chance to serve the template `public/index.html`.
            middleware.use(new _serveStaticMiddleware.ServeStaticMiddleware(this.projectRoot).getHandler());
            // This should come after the static middleware so it doesn't serve the favicon from `public/favicon.ico`.
            middleware.use(new _faviconMiddleware.FaviconMiddleware(this.projectRoot).getHandler());
            if (useServerRendering) {
                const baseUrl = (0, _metroOptions).getBaseUrlFromExpoConfig(exp);
                var _mode1;
                const asyncRoutes = (0, _metroOptions).getAsyncRoutesFromExpoConfig(exp, (_mode1 = options.mode) != null ? _mode1 : "development", "web");
                const routerRoot = (0, _router).getRouterDirectoryModuleIdWithManifest(this.projectRoot, exp);
                const appDir = _path.default.join(this.projectRoot, routerRoot);
                middleware.use((0, _createServerRouteMiddleware).createRouteHandlerMiddleware(this.projectRoot, {
                    ...options,
                    appDir,
                    baseUrl,
                    routerRoot,
                    config,
                    getWebBundleUrl: manifestMiddleware.getWebBundleUrl.bind(manifestMiddleware),
                    getStaticPageAsync: (pathname)=>{
                        var _mode;
                        return this.getStaticPageAsync(pathname, {
                            isExporting: !!options.isExporting,
                            mode: (_mode = options.mode) != null ? _mode : "development",
                            minify: options.minify,
                            baseUrl,
                            asyncRoutes,
                            routerRoot
                        });
                    }
                }));
                (0, _waitForMetroToObserveTypeScriptFile).observeAnyFileChanges({
                    metro,
                    server
                }, (events)=>{
                    var ref;
                    if (((ref = exp.web) == null ? void 0 : ref.output) === "server") {
                        // NOTE(EvanBacon): We aren't sure what files the API routes are using so we'll just invalidate
                        // aggressively to ensure we always have the latest. The only caching we really get here is for
                        // cases where the user is making subsequent requests to the same API route without changing anything.
                        // This is useful for testing but pretty suboptimal. Luckily our caching is pretty aggressive so it makes
                        // up for a lot of the overhead.
                        (0, _bundleApiRoutes).invalidateApiRouteCache();
                    } else if (!(0, _router).hasWarnedAboutApiRoutes()) {
                        for (const event of events){
                            var // If the user did not delete a file that matches the Expo Router API Route convention, then we should warn that
                            // API Routes are not enabled in the project.
                            ref4;
                            if (((ref4 = event.metadata) == null ? void 0 : ref4.type) !== "d" && // Ensure the file is in the project's routes directory to prevent false positives in monorepos.
                            event.filePath.startsWith(appDir) && (0, _router).isApiRouteConvention(event.filePath)) {
                                (0, _router).warnInvalidWebOutput();
                            }
                        }
                    }
                });
            } else {
                // This MUST run last since it's the fallback.
                middleware.use(new _historyFallbackMiddleware.HistoryFallbackMiddleware(manifestMiddleware.getHandler().internal).getHandler());
            }
        }
        // Extend the close method to ensure that we clean up the local info.
        const originalClose = server.close.bind(server);
        server.close = (callback)=>{
            return originalClose((err)=>{
                this.instance = null;
                this.metro = null;
                callback == null ? void 0 : callback(err);
            });
        };
        this.metro = metro;
        return {
            server,
            location: {
                // The port is the main thing we want to send back.
                port: options.port,
                // localhost isn't always correct.
                host: "localhost",
                // http is the only supported protocol on native.
                url: `http://localhost:${options.port}`,
                protocol: "http"
            },
            middleware,
            messageSocket
        };
    }
    async waitForTypeScriptAsync() {
        if (!this.instance) {
            throw new Error("Cannot wait for TypeScript without a running server.");
        }
        return new Promise((resolve)=>{
            if (!this.metro) {
                // This can happen when the run command is used and the server is already running in another
                // process. In this case we can't wait for the TypeScript check to complete because we don't
                // have access to the Metro server.
                debug("Skipping TypeScript check because Metro is not running (headless).");
                return resolve(false);
            }
            const off = (0, _metroWatchTypeScriptFiles).metroWatchTypeScriptFiles({
                projectRoot: this.projectRoot,
                server: this.instance.server,
                metro: this.metro,
                tsconfig: true,
                throttle: true,
                eventTypes: [
                    "change",
                    "add"
                ],
                callback: async ()=>{
                    // Run once, this prevents the TypeScript project prerequisite from running on every file change.
                    off();
                    const { TypeScriptProjectPrerequisite  } = await Promise.resolve().then(function() {
                        return _interopRequireWildcard(require("../../doctor/typescript/TypeScriptProjectPrerequisite.js"));
                    });
                    try {
                        const req = new TypeScriptProjectPrerequisite(this.projectRoot);
                        await req.bootstrapAsync();
                        resolve(true);
                    } catch (error) {
                        // Ensure the process doesn't fail if the TypeScript check fails.
                        // This could happen during the install.
                        _log.Log.log();
                        _log.Log.error(_chalk.default.red`Failed to automatically setup TypeScript for your project. Try restarting the dev server to fix.`);
                        _log.Log.exception(error);
                        resolve(false);
                    }
                }
            });
        });
    }
    async startTypeScriptServices() {
        var ref;
        return (0, _startTypescriptTypeGeneration).startTypescriptTypeGenerationAsync({
            server: (ref = this.instance) == null ? void 0 : ref.server,
            metro: this.metro,
            projectRoot: this.projectRoot
        });
    }
    getConfigModuleIds() {
        return [
            "./metro.config.js",
            "./metro.config.json",
            "./rn-cli.config.js"
        ];
    }
}
exports.MetroBundlerDevServer = MetroBundlerDevServer;
function getDeepLinkHandler(projectRoot) {
    return async ({ runtime  })=>{
        if (runtime === "expo") return;
        const { exp  } = (0, _config).getConfig(projectRoot);
        await (0, _rudderstackClient).logEventAsync("dev client start command", {
            status: "started",
            ...(0, _getDevClientProperties).default(projectRoot, exp)
        });
    };
}

//# sourceMappingURL=MetroBundlerDevServer.js.map