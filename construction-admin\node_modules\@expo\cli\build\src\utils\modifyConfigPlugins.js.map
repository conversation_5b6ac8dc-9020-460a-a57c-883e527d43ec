{"version": 3, "sources": ["../../../src/utils/modifyConfigPlugins.ts"], "sourcesContent": ["import { ExpoConfig, modifyConfigAsync } from '@expo/config';\n\nimport { warnAboutConfigAndThrow } from './modifyConfigAsync';\nimport * as Log from '../log';\n\nexport async function attemptAddingPluginsAsync(\n  projectRoot: string,\n  exp: Pick<ExpoConfig, 'plugins'>,\n  plugins: string[]\n): Promise<void> {\n  if (!plugins.length) return;\n\n  const edits = {\n    plugins: [...new Set((exp.plugins || []).concat(plugins))],\n  };\n  const modification = await modifyConfigAsync(projectRoot, edits, {\n    skipSDKVersionRequirement: true,\n    skipPlugins: true,\n  });\n  if (modification.type === 'success') {\n    Log.log(`\\u203A Added config plugin${plugins.length === 1 ? '' : 's'}: ${plugins.join(', ')}`);\n  } else {\n    const exactEdits = {\n      plugins,\n    };\n    warnAboutConfigAndThrow(modification.type, modification.message!, exactEdits);\n  }\n}\n"], "names": ["attemptAddingPluginsAsync", "Log", "projectRoot", "exp", "plugins", "length", "edits", "Set", "concat", "modification", "modifyConfigAsync", "skipSDKVersionRequirement", "skip<PERSON>lug<PERSON>", "type", "log", "join", "exactEdits", "warnAboutConfigAndThrow", "message"], "mappings": "AAAA;;;;QAKsBA,yBAAyB,GAAzBA,yBAAyB;AALD,IAAA,OAAc,WAAd,cAAc,CAAA;AAEpB,IAAA,kBAAqB,WAArB,qBAAqB,CAAA;AACjDC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;;;;;;;;;;;;;;;;;;;;;;AAER,eAAeD,yBAAyB,CAC7CE,WAAmB,EACnBC,GAAgC,EAChCC,OAAiB,EACF;IACf,IAAI,CAACA,OAAO,CAACC,MAAM,EAAE,OAAO;IAE5B,MAAMC,KAAK,GAAG;QACZF,OAAO,EAAE;eAAI,IAAIG,GAAG,CAAC,CAACJ,GAAG,CAACC,OAAO,IAAI,EAAE,CAAC,CAACI,MAAM,CAACJ,OAAO,CAAC,CAAC;SAAC;KAC3D,AAAC;IACF,MAAMK,YAAY,GAAG,MAAMC,CAAAA,GAAAA,OAAiB,AAG1C,CAAA,kBAH0C,CAACR,WAAW,EAAEI,KAAK,EAAE;QAC/DK,yBAAyB,EAAE,IAAI;QAC/BC,WAAW,EAAE,IAAI;KAClB,CAAC,AAAC;IACH,IAAIH,YAAY,CAACI,IAAI,KAAK,SAAS,EAAE;QACnCZ,GAAG,CAACa,GAAG,CAAC,CAAC,0BAA0B,EAAEV,OAAO,CAACC,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,EAAED,OAAO,CAACW,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;KAChG,MAAM;QACL,MAAMC,UAAU,GAAG;YACjBZ,OAAO;SACR,AAAC;QACFa,CAAAA,GAAAA,kBAAuB,AAAsD,CAAA,wBAAtD,CAACR,YAAY,CAACI,IAAI,EAAEJ,YAAY,CAACS,OAAO,EAAGF,UAAU,CAAC,CAAC;KAC/E;CACF"}