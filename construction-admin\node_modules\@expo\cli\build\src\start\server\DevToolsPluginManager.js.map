{"version": 3, "sources": ["../../../../src/start/server/DevToolsPluginManager.ts"], "sourcesContent": ["import type { ModuleDescriptorDevTools } from 'expo-modules-autolinking/exports';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nconst debug = require('debug')('expo:start:server:devtools');\n\nexport const DevToolsPluginEndpoint = '/_expo/plugins';\n\ninterface AutolinkingPlugin {\n  packageName: string;\n  packageRoot: string;\n  webpageRoot: string;\n}\n\nexport interface DevToolsPlugin extends AutolinkingPlugin {\n  webpageEndpoint: string;\n}\n\nexport default class DevToolsPluginManager {\n  private plugins: DevToolsPlugin[] | null = null;\n\n  constructor(private projectRoot: string) {}\n\n  public async queryPluginsAsync(): Promise<DevToolsPlugin[]> {\n    if (this.plugins) {\n      return this.plugins;\n    }\n    const plugins = (await this.queryAutolinkedPluginsAsync(this.projectRoot)).map((plugin) => ({\n      ...plugin,\n      webpageEndpoint: `${DevToolsPluginEndpoint}/${plugin.packageName}`,\n    }));\n    this.plugins = plugins;\n    return this.plugins;\n  }\n\n  public async queryPluginWebpageRootAsync(pluginName: string): Promise<string | null> {\n    const plugins = await this.queryPluginsAsync();\n    const plugin = plugins.find((p) => p.packageName === pluginName);\n    return plugin?.webpageRoot ?? null;\n  }\n\n  private async queryAutolinkedPluginsAsync(projectRoot: string): Promise<AutolinkingPlugin[]> {\n    const expoPackagePath = resolveFrom.silent(projectRoot, 'expo/package.json');\n    if (!expoPackagePath) {\n      return [];\n    }\n    const resolvedPath = resolveFrom.silent(\n      path.dirname(expoPackagePath),\n      'expo-modules-autolinking/exports'\n    );\n    if (!resolvedPath) {\n      return [];\n    }\n    const autolinkingModule = require(\n      resolvedPath\n    ) as typeof import('expo-modules-autolinking/exports');\n    if (!autolinkingModule.queryAutolinkingModulesFromProjectAsync) {\n      throw new Error(\n        'Missing exported `queryAutolinkingModulesFromProjectAsync()` function from `expo-modules-autolinking`'\n      );\n    }\n    const plugins = (await autolinkingModule.queryAutolinkingModulesFromProjectAsync(projectRoot, {\n      platform: 'devtools',\n      onlyProjectDeps: false,\n    })) as ModuleDescriptorDevTools[];\n    debug('Found autolinked plugins', this.plugins);\n    return plugins;\n  }\n}\n"], "names": ["DevToolsPluginManager", "constructor", "projectRoot", "plugins", "queryPluginsAsync", "queryAutolinkedPluginsAsync", "map", "plugin", "webpageEndpoint", "DevToolsPluginEndpoint", "packageName", "queryPluginWebpageRootAsync", "pluginName", "find", "p", "webpageRoot", "expoPackagePath", "resolveFrom", "silent", "<PERSON><PERSON><PERSON>", "path", "dirname", "autolinkingModule", "require", "queryAutolinkingModulesFromProjectAsync", "Error", "platform", "onlyProjectDeps", "debug"], "mappings": "AAAA;;;;;AACiB,IAAA,KAAM,kCAAN,MAAM,EAAA;AACC,IAAA,YAAc,kCAAd,cAAc,EAAA;AAgBvB,MAAMA,qBAAqB;IAGxCC,YAAoBC,WAAmB,CAAE;aAArBA,WAAmB,GAAnBA,WAAmB;aAF/BC,OAAO,GAA4B,IAAI;KAEJ;IAE3C,MAAaC,iBAAiB,GAA8B;QAC1D,IAAI,IAAI,CAACD,OAAO,EAAE;YAChB,OAAO,IAAI,CAACA,OAAO,CAAC;SACrB;QACD,MAAMA,OAAO,GAAG,CAAC,MAAM,IAAI,CAACE,2BAA2B,CAAC,IAAI,CAACH,WAAW,CAAC,CAAC,CAACI,GAAG,CAAC,CAACC,MAAM,GAAK,CAAC;gBAC1F,GAAGA,MAAM;gBACTC,eAAe,EAAE,CAAC,EAAEC,sBAAsB,CAAC,CAAC,EAAEF,MAAM,CAACG,WAAW,CAAC,CAAC;aACnE,CAAC;QAAA,CAAC,AAAC;QACJ,IAAI,CAACP,OAAO,GAAGA,OAAO,CAAC;QACvB,OAAO,IAAI,CAACA,OAAO,CAAC;KACrB;IAED,MAAaQ,2BAA2B,CAACC,UAAkB,EAA0B;QACnF,MAAMT,OAAO,GAAG,MAAM,IAAI,CAACC,iBAAiB,EAAE,AAAC;QAC/C,MAAMG,MAAM,GAAGJ,OAAO,CAACU,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACJ,WAAW,KAAKE,UAAU;QAAA,CAAC,AAAC;YAC1DL,GAAmB;QAA1B,OAAOA,CAAAA,GAAmB,GAAnBA,MAAM,QAAa,GAAnBA,KAAAA,CAAmB,GAAnBA,MAAM,CAAEQ,WAAW,YAAnBR,GAAmB,GAAI,IAAI,CAAC;KACpC;IAED,MAAcF,2BAA2B,CAACH,WAAmB,EAAgC;QAC3F,MAAMc,eAAe,GAAGC,YAAW,QAAA,CAACC,MAAM,CAAChB,WAAW,EAAE,mBAAmB,CAAC,AAAC;QAC7E,IAAI,CAACc,eAAe,EAAE;YACpB,OAAO,EAAE,CAAC;SACX;QACD,MAAMG,YAAY,GAAGF,YAAW,QAAA,CAACC,MAAM,CACrCE,KAAI,QAAA,CAACC,OAAO,CAACL,eAAe,CAAC,EAC7B,kCAAkC,CACnC,AAAC;QACF,IAAI,CAACG,YAAY,EAAE;YACjB,OAAO,EAAE,CAAC;SACX;QACD,MAAMG,iBAAiB,GAAGC,OAAO,CAC/BJ,YAAY,CACb,AAAqD,AAAC;QACvD,IAAI,CAACG,iBAAiB,CAACE,uCAAuC,EAAE;YAC9D,MAAM,IAAIC,KAAK,CACb,uGAAuG,CACxG,CAAC;SACH;QACD,MAAMtB,OAAO,GAAI,MAAMmB,iBAAiB,CAACE,uCAAuC,CAACtB,WAAW,EAAE;YAC5FwB,QAAQ,EAAE,UAAU;YACpBC,eAAe,EAAE,KAAK;SACvB,CAAC,AAA+B,AAAC;QAClCC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAACzB,OAAO,CAAC,CAAC;QAChD,OAAOA,OAAO,CAAC;KAChB;CACF;kBAlDoBH,qBAAqB;;;;;;AAd1C,MAAM4B,KAAK,GAAGL,OAAO,CAAC,OAAO,CAAC,CAAC,4BAA4B,CAAC,AAAC;AAEtD,MAAMd,sBAAsB,GAAG,gBAAgB,AAAC;QAA1CA,sBAAsB,GAAtBA,sBAAsB"}