{"version": 3, "sources": ["../../../../../../src/start/server/middleware/inspector/LaunchBrowser.ts"], "sourcesContent": ["import os from 'os';\n\nimport {\n  LaunchBrowserTypesEnum,\n  type LaunchBrowser,\n  type LaunchBrowserInstance,\n  type LaunchBrowserTypes,\n} from './LaunchBrowser.types';\nimport LaunchBrowserImplLinux from './LaunchBrowserImplLinux';\nimport LaunchBrowserImplMacOS from './LaunchBrowserImplMacOS';\nimport LaunchBrowserImplWindows from './LaunchBrowserImplWindows';\n\nexport type { LaunchBrowserInstance };\n\nconst IS_WSL = require('is-wsl') && !require('is-docker')();\n\n/**\n * A factory to create a LaunchBrowser instance based on the host platform\n */\nexport function createLaunchBrowser(): LaunchBrowser {\n  let launchBrowser: LaunchBrowser;\n  if (os.platform() === 'darwin') {\n    launchBrowser = new LaunchBrowserImplMacOS();\n  } else if (os.platform() === 'win32' || IS_WSL) {\n    launchBrowser = new LaunchBrowserImplWindows();\n  } else if (os.platform() === 'linux') {\n    launchBrowser = new LaunchBrowserImplLinux();\n  } else {\n    throw new Error('[createLaunchBrowser] Unsupported host platform');\n  }\n  return launchBrowser;\n}\n\n/**\n * Find a supported browser type on the host\n */\nexport async function findSupportedBrowserTypeAsync(\n  launchBrowser: LaunchBrowser\n): Promise<LaunchBrowserTypes> {\n  const supportedBrowsers = Object.values(LaunchBrowserTypesEnum);\n  for (const browserType of supportedBrowsers) {\n    if (await launchBrowser.isSupportedBrowser(browserType)) {\n      return browserType;\n    }\n  }\n\n  throw new Error(\n    `[findSupportedBrowserTypeAsync] Unable to find a browser on the host to open the inspector. Supported browsers: ${supportedBrowsers.join(\n      ', '\n    )}`\n  );\n}\n\n/**\n * Launch a browser for inspector\n */\nexport async function launchInspectorBrowserAsync(\n  url: string,\n  browser?: LaunchBrowser,\n  browserType?: LaunchBrowserTypes\n): Promise<LaunchBrowserInstance> {\n  const launchBrowser = browser ?? createLaunchBrowser();\n  const launchBrowserType = browserType ?? (await findSupportedBrowserTypeAsync(launchBrowser));\n\n  const tempBrowserDir = await launchBrowser.createTempBrowserDir('expo-inspector');\n\n  // For dev-client connecting metro in LAN, the request to fetch sourcemaps may be blocked by Chromium\n  // with insecure-content (https page send xhr for http resource).\n  // Adding `--allow-running-insecure-content` to overcome this limitation\n  // without users manually allow insecure-content in site settings.\n  // However, if there is existing chromium browser process, the argument will not take effect.\n  // We also pass a `--user-data-dir=` as temporary profile and force chromium to create new browser process.\n  const launchArgs = [\n    `--app=${url}`,\n    '--allow-running-insecure-content',\n    `--user-data-dir=${tempBrowserDir}`,\n    '--no-first-run',\n    '--no-default-browser-check',\n  ];\n\n  return launchBrowser.launchAsync(launchBrowserType, launchArgs);\n}\n"], "names": ["createLaunchBrowser", "findSupportedBrowserTypeAsync", "launchInspectorBrowserAsync", "IS_WSL", "require", "launchBrowser", "os", "platform", "LaunchBrowserImplMacOS", "LaunchBrowserImplWindows", "LaunchBrowserImplLinux", "Error", "supportedBrowsers", "Object", "values", "LaunchBrowserTypesEnum", "browserType", "isSupportedBrowser", "join", "url", "browser", "launchBrowserType", "tempBrowserDir", "createTempBrowserDir", "launchArgs", "launchAsync"], "mappings": "AAAA;;;;QAmBgBA,mBAAmB,GAAnBA,mBAAmB;QAiBbC,6BAA6B,GAA7BA,6BAA6B;QAoB7BC,2BAA2B,GAA3BA,2BAA2B;AAxDlC,IAAA,GAAI,kCAAJ,IAAI,EAAA;AAOZ,IAAA,mBAAuB,WAAvB,uBAAuB,CAAA;AACK,IAAA,uBAA0B,kCAA1B,0BAA0B,EAAA;AAC1B,IAAA,uBAA0B,kCAA1B,0BAA0B,EAAA;AACxB,IAAA,yBAA4B,kCAA5B,4BAA4B,EAAA;;;;;;AAIjE,MAAMC,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAACA,OAAO,CAAC,WAAW,CAAC,EAAE,AAAC;AAKrD,SAASJ,mBAAmB,GAAkB;IACnD,IAAIK,aAAa,AAAe,AAAC;IACjC,IAAIC,GAAE,QAAA,CAACC,QAAQ,EAAE,KAAK,QAAQ,EAAE;QAC9BF,aAAa,GAAG,IAAIG,uBAAsB,QAAA,EAAE,CAAC;KAC9C,MAAM,IAAIF,GAAE,QAAA,CAACC,QAAQ,EAAE,KAAK,OAAO,IAAIJ,MAAM,EAAE;QAC9CE,aAAa,GAAG,IAAII,yBAAwB,QAAA,EAAE,CAAC;KAChD,MAAM,IAAIH,GAAE,QAAA,CAACC,QAAQ,EAAE,KAAK,OAAO,EAAE;QACpCF,aAAa,GAAG,IAAIK,uBAAsB,QAAA,EAAE,CAAC;KAC9C,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,iDAAiD,CAAC,CAAC;KACpE;IACD,OAAON,aAAa,CAAC;CACtB;AAKM,eAAeJ,6BAA6B,CACjDI,aAA4B,EACC;IAC7B,MAAMO,iBAAiB,GAAGC,MAAM,CAACC,MAAM,CAACC,mBAAsB,uBAAA,CAAC,AAAC;IAChE,KAAK,MAAMC,WAAW,IAAIJ,iBAAiB,CAAE;QAC3C,IAAI,MAAMP,aAAa,CAACY,kBAAkB,CAACD,WAAW,CAAC,EAAE;YACvD,OAAOA,WAAW,CAAC;SACpB;KACF;IAED,MAAM,IAAIL,KAAK,CACb,CAAC,gHAAgH,EAAEC,iBAAiB,CAACM,IAAI,CACvI,IAAI,CACL,CAAC,CAAC,CACJ,CAAC;CACH;AAKM,eAAehB,2BAA2B,CAC/CiB,GAAW,EACXC,OAAuB,EACvBJ,WAAgC,EACA;IAChC,MAAMX,aAAa,GAAGe,OAAO,WAAPA,OAAO,GAAIpB,mBAAmB,EAAE,AAAC;IACvD,MAAMqB,iBAAiB,GAAGL,WAAW,WAAXA,WAAW,GAAK,MAAMf,6BAA6B,CAACI,aAAa,CAAC,AAAC,AAAC;IAE9F,MAAMiB,cAAc,GAAG,MAAMjB,aAAa,CAACkB,oBAAoB,CAAC,gBAAgB,CAAC,AAAC;IAElF,qGAAqG;IACrG,iEAAiE;IACjE,wEAAwE;IACxE,kEAAkE;IAClE,6FAA6F;IAC7F,2GAA2G;IAC3G,MAAMC,UAAU,GAAG;QACjB,CAAC,MAAM,EAAEL,GAAG,CAAC,CAAC;QACd,kCAAkC;QAClC,CAAC,gBAAgB,EAAEG,cAAc,CAAC,CAAC;QACnC,gBAAgB;QAChB,4BAA4B;KAC7B,AAAC;IAEF,OAAOjB,aAAa,CAACoB,WAAW,CAACJ,iBAAiB,EAAEG,UAAU,CAAC,CAAC;CACjE"}