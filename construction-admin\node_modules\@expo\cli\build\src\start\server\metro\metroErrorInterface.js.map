{"version": 3, "sources": ["../../../../../src/start/server/metro/metroErrorInterface.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport chalk from 'chalk';\nimport resolveFrom from 'resolve-from';\nimport { StackFrame } from 'stacktrace-parser';\nimport terminalLink from 'terminal-link';\n\nimport { Log } from '../../../log';\nimport { SilentError } from '../../../utils/errors';\nimport { createMetroEndpointAsync } from '../getStaticRenderFunctions';\n\ntype CodeFrame = {\n  content: string;\n  location?: {\n    row: number;\n    column: number;\n    [key: string]: any;\n  };\n  fileName: string;\n};\n\ntype MetroStackFrame = StackFrame & { collapse?: boolean };\n\nfunction fill(width: number): string {\n  return Array(width).join(' ');\n}\n\nfunction formatPaths(config: { filePath: string | null; line?: number; col?: number }) {\n  const filePath = chalk.reset(config.filePath);\n  return (\n    chalk.dim('(') +\n    filePath +\n    chalk.dim(`:${[config.line, config.col].filter(Boolean).join(':')})`)\n  );\n}\n\nexport async function logMetroErrorWithStack(\n  projectRoot: string,\n  {\n    stack,\n    codeFrame,\n    error,\n  }: {\n    stack: MetroStackFrame[];\n    codeFrame: CodeFrame;\n    error: Error;\n  }\n) {\n  if (error instanceof SilentError) {\n    return;\n  }\n\n  // process.stdout.write('\\u001b[0m'); // Reset attributes\n  // process.stdout.write('\\u001bc'); // Reset the terminal\n\n  const { getStackFormattedLocation } = require(\n    resolveFrom(projectRoot, '@expo/metro-runtime/symbolicate')\n  );\n\n  Log.log();\n  Log.log(chalk.red('Metro error: ') + error.message);\n  Log.log();\n\n  if (codeFrame) {\n    const maxWarningLineLength = Math.max(200, process.stdout.columns);\n\n    const lineText = codeFrame.content;\n    const isPreviewTooLong = codeFrame.content\n      .split('\\n')\n      .some((line) => line.length > maxWarningLineLength);\n    const column = codeFrame.location?.column;\n    // When the preview is too long, we skip reading the file and attempting to apply\n    // code coloring, this is because it can get very slow.\n    if (isPreviewTooLong) {\n      let previewLine = '';\n      let cursorLine = '';\n\n      const formattedPath = formatPaths({\n        filePath: codeFrame.fileName,\n        line: codeFrame.location?.row,\n        col: codeFrame.location?.column,\n      });\n      // Create a curtailed preview line like:\n      // `...transition:'fade'},k._updatePropsStack=function(){clearImmediate(k._updateImmediate),k._updateImmediate...`\n      // If there is no text preview or column number, we can't do anything.\n      if (lineText && column != null) {\n        const rangeWindow = Math.round(\n          Math.max(codeFrame.fileName?.length ?? 0, Math.max(80, process.stdout.columns)) / 2\n        );\n        let minBounds = Math.max(0, column - rangeWindow);\n        const maxBounds = Math.min(minBounds + rangeWindow * 2, lineText.length);\n        previewLine = lineText.slice(minBounds, maxBounds);\n\n        // If we splice content off the start, then we should append `...`.\n        // This is unlikely to happen since we limit the activation size.\n        if (minBounds > 0) {\n          // Adjust the min bounds so the cursor is aligned after we add the \"...\"\n          minBounds -= 3;\n          previewLine = chalk.dim('...') + previewLine;\n        }\n        if (maxBounds < lineText.length) {\n          previewLine += chalk.dim('...');\n        }\n\n        // If the column property could be found, then use that to fix the cursor location which is often broken in regex.\n        cursorLine = (column == null ? '' : fill(column) + chalk.reset('^')).slice(minBounds);\n\n        Log.log(\n          [formattedPath, '', previewLine, cursorLine, chalk.dim('(error truncated)')].join('\\n')\n        );\n      }\n    } else {\n      Log.log(codeFrame.content);\n    }\n  }\n\n  if (stack?.length) {\n    Log.log();\n    Log.log(chalk.bold`Call Stack`);\n\n    const stackProps = stack.map((frame) => {\n      return {\n        title: frame.methodName,\n        subtitle: getStackFormattedLocation(projectRoot, frame),\n        collapse: frame.collapse,\n      };\n    });\n\n    stackProps.forEach((frame) => {\n      const position = terminalLink.isSupported\n        ? terminalLink(frame.subtitle, frame.subtitle)\n        : frame.subtitle;\n      let lineItem = chalk.gray(`  ${frame.title} (${position})`);\n      if (frame.collapse) {\n        lineItem = chalk.dim(lineItem);\n      }\n      Log.log(lineItem);\n    });\n  } else {\n    Log.log(chalk.gray(`  ${error.stack}`));\n  }\n}\n\nexport async function logMetroError(projectRoot: string, { error }: { error: Error }) {\n  if (error instanceof SilentError) {\n    return;\n  }\n\n  const { LogBoxLog, parseErrorStack } = require(\n    resolveFrom(projectRoot, '@expo/metro-runtime/symbolicate')\n  );\n\n  const stack = parseErrorStack(error.stack);\n\n  const log = new LogBoxLog({\n    level: 'static',\n    message: {\n      content: error.message,\n      substitutions: [],\n    },\n    isComponentError: false,\n    stack,\n    category: 'static',\n    componentStack: [],\n  });\n\n  await new Promise((res) => log.symbolicate('stack', res));\n\n  logMetroErrorWithStack(projectRoot, {\n    stack: log.symbolicated?.stack?.stack ?? [],\n    codeFrame: log.codeFrame,\n    error,\n  });\n}\n\n/** @returns the html required to render the static metro error as an SPA. */\nfunction logFromError({ error, projectRoot }: { error: Error; projectRoot: string }): {\n  symbolicated: any;\n  symbolicate: (type: string, callback: () => void) => void;\n  codeFrame: CodeFrame;\n} {\n  const { LogBoxLog, parseErrorStack } = require(\n    resolveFrom(projectRoot, '@expo/metro-runtime/symbolicate')\n  );\n\n  const stack = parseErrorStack(error.stack);\n\n  return new LogBoxLog({\n    level: 'static',\n    message: {\n      content: error.message,\n      substitutions: [],\n    },\n    isComponentError: false,\n    stack,\n    category: 'static',\n    componentStack: [],\n  });\n}\n\n/** @returns the html required to render the static metro error as an SPA. */\nexport async function logMetroErrorAsync({\n  error,\n  projectRoot,\n}: {\n  error: Error;\n  projectRoot: string;\n}) {\n  const log = logFromError({ projectRoot, error });\n\n  await new Promise<void>((res) => log.symbolicate('stack', res));\n\n  logMetroErrorWithStack(projectRoot, {\n    stack: log.symbolicated?.stack?.stack ?? [],\n    codeFrame: log.codeFrame,\n    error,\n  });\n}\n\n/** @returns the html required to render the static metro error as an SPA. */\nexport async function getErrorOverlayHtmlAsync({\n  error,\n  projectRoot,\n  routerRoot,\n}: {\n  error: Error;\n  projectRoot: string;\n  routerRoot: string;\n}) {\n  const log = logFromError({ projectRoot, error });\n\n  await new Promise<void>((res) => log.symbolicate('stack', res));\n\n  logMetroErrorWithStack(projectRoot, {\n    stack: log.symbolicated?.stack?.stack ?? [],\n    codeFrame: log.codeFrame,\n    error,\n  });\n\n  const logBoxContext = {\n    selectedLogIndex: 0,\n    isDisabled: false,\n    logs: [log],\n  };\n  const html = `<html><head><style>#root,body,html{height:100%}body{overflow:hidden}#root{display:flex}</style></head><body><div id=\"root\"></div><script id=\"_expo-static-error\" type=\"application/json\">${JSON.stringify(\n    logBoxContext\n  )}</script></body></html>`;\n\n  const errorOverlayEntry = await createMetroEndpointAsync(\n    projectRoot,\n    // Keep the URL relative\n    '',\n    resolveFrom(projectRoot, 'expo-router/_error'),\n    {\n      dev: true,\n      platform: 'web',\n      minify: false,\n      baseUrl: '',\n      routerRoot,\n    }\n  );\n\n  const htmlWithJs = html.replace('</body>', `<script src=${errorOverlayEntry}></script></body>`);\n  return htmlWithJs;\n}\n"], "names": ["logMetroErrorWithStack", "logMetroError", "logMetroErrorAsync", "getErrorOverlayHtmlAsync", "fill", "width", "Array", "join", "formatPaths", "config", "filePath", "chalk", "reset", "dim", "line", "col", "filter", "Boolean", "projectRoot", "stack", "codeFrame", "error", "SilentError", "getStackFormattedLocation", "require", "resolveFrom", "Log", "log", "red", "message", "maxWarning<PERSON>ine<PERSON><PERSON><PERSON>", "Math", "max", "process", "stdout", "columns", "lineText", "content", "isPreviewTooLong", "split", "some", "length", "column", "location", "previewLine", "cursorLine", "formattedPath", "fileName", "row", "rangeWindow", "round", "minBounds", "maxBounds", "min", "slice", "bold", "stackProps", "map", "frame", "title", "methodName", "subtitle", "collapse", "for<PERSON>ach", "position", "terminalLink", "isSupported", "lineItem", "gray", "LogBoxLog", "parseError<PERSON>tack", "level", "substitutions", "isComponentError", "category", "componentStack", "Promise", "res", "symbolicate", "symbolicated", "logFromError", "routerRoot", "logBoxContext", "selectedLogIndex", "isDisabled", "logs", "html", "JSON", "stringify", "errorOverlayEntry", "createMetroEndpointAsync", "dev", "platform", "minify", "baseUrl", "htmlWithJs", "replace"], "mappings": "AAMA;;;;QAkCsBA,sBAAsB,GAAtBA,sBAAsB;QA2GtBC,aAAa,GAAbA,aAAa;QA0DbC,kBAAkB,GAAlBA,kBAAkB;QAmBlBC,wBAAwB,GAAxBA,wBAAwB;AA1N5B,IAAA,MAAO,kCAAP,OAAO,EAAA;AACD,IAAA,YAAc,kCAAd,cAAc,EAAA;AAEb,IAAA,aAAe,kCAAf,eAAe,EAAA;AAEpB,IAAA,IAAc,WAAd,cAAc,CAAA;AACN,IAAA,OAAuB,WAAvB,uBAAuB,CAAA;AACV,IAAA,yBAA6B,WAA7B,6BAA6B,CAAA;;;;;;AActE,SAASC,IAAI,CAACC,KAAa,EAAU;IACnC,OAAOC,KAAK,CAACD,KAAK,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC;CAC/B;AAED,SAASC,WAAW,CAACC,MAAgE,EAAE;IACrF,MAAMC,QAAQ,GAAGC,MAAK,QAAA,CAACC,KAAK,CAACH,MAAM,CAACC,QAAQ,CAAC,AAAC;IAC9C,OACEC,MAAK,QAAA,CAACE,GAAG,CAAC,GAAG,CAAC,GACdH,QAAQ,GACRC,MAAK,QAAA,CAACE,GAAG,CAAC,CAAC,CAAC,EAAE;QAACJ,MAAM,CAACK,IAAI;QAAEL,MAAM,CAACM,GAAG;KAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACV,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CACrE;CACH;AAEM,eAAeP,sBAAsB,CAC1CkB,WAAmB,EACnB,EACEC,KAAK,CAAA,EACLC,SAAS,CAAA,EACTC,KAAK,CAAA,EAKN,EACD;IACA,IAAIA,KAAK,YAAYC,OAAW,YAAA,EAAE;QAChC,OAAO;KACR;IAED,yDAAyD;IACzD,yDAAyD;IAEzD,MAAM,EAAEC,yBAAyB,CAAA,EAAE,GAAGC,OAAO,CAC3CC,CAAAA,GAAAA,YAAW,AAAgD,CAAA,QAAhD,CAACP,WAAW,EAAE,iCAAiC,CAAC,CAC5D,AAAC;IAEFQ,IAAG,IAAA,CAACC,GAAG,EAAE,CAAC;IACVD,IAAG,IAAA,CAACC,GAAG,CAAChB,MAAK,QAAA,CAACiB,GAAG,CAAC,eAAe,CAAC,GAAGP,KAAK,CAACQ,OAAO,CAAC,CAAC;IACpDH,IAAG,IAAA,CAACC,GAAG,EAAE,CAAC;IAEV,IAAIP,SAAS,EAAE;YAOEA,GAAkB;QANjC,MAAMU,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEC,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,AAAC;QAEnE,MAAMC,QAAQ,GAAGhB,SAAS,CAACiB,OAAO,AAAC;QACnC,MAAMC,gBAAgB,GAAGlB,SAAS,CAACiB,OAAO,CACvCE,KAAK,CAAC,IAAI,CAAC,CACXC,IAAI,CAAC,CAAC1B,IAAI,GAAKA,IAAI,CAAC2B,MAAM,GAAGX,oBAAoB;QAAA,CAAC,AAAC;QACtD,MAAMY,MAAM,GAAGtB,CAAAA,GAAkB,GAAlBA,SAAS,CAACuB,QAAQ,SAAQ,GAA1BvB,KAAAA,CAA0B,GAA1BA,GAAkB,CAAEsB,MAAM,AAAC;QAC1C,iFAAiF;QACjF,uDAAuD;QACvD,IAAIJ,gBAAgB,EAAE;gBAMZlB,IAAkB,EACnBA,IAAkB;YANzB,IAAIwB,WAAW,GAAG,EAAE,AAAC;YACrB,IAAIC,UAAU,GAAG,EAAE,AAAC;YAEpB,MAAMC,aAAa,GAAGtC,WAAW,CAAC;gBAChCE,QAAQ,EAAEU,SAAS,CAAC2B,QAAQ;gBAC5BjC,IAAI,EAAEM,CAAAA,IAAkB,GAAlBA,SAAS,CAACuB,QAAQ,SAAK,GAAvBvB,KAAAA,CAAuB,GAAvBA,IAAkB,CAAE4B,GAAG;gBAC7BjC,GAAG,EAAEK,CAAAA,IAAkB,GAAlBA,SAAS,CAACuB,QAAQ,SAAQ,GAA1BvB,KAAAA,CAA0B,GAA1BA,IAAkB,CAAEsB,MAAM;aAChC,CAAC,AAAC;YACH,wCAAwC;YACxC,kHAAkH;YAClH,sEAAsE;YACtE,IAAIN,QAAQ,IAAIM,MAAM,IAAI,IAAI,EAAE;oBAEnBtB,IAAkB;oBAAlBA,IAA0B;gBADrC,MAAM6B,WAAW,GAAGlB,IAAI,CAACmB,KAAK,CAC5BnB,IAAI,CAACC,GAAG,CAACZ,CAAAA,IAA0B,GAA1BA,CAAAA,IAAkB,GAAlBA,SAAS,CAAC2B,QAAQ,SAAQ,GAA1B3B,KAAAA,CAA0B,GAA1BA,IAAkB,CAAEqB,MAAM,YAA1BrB,IAA0B,GAAI,CAAC,EAAEW,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEC,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC,GAAG,CAAC,CACpF,AAAC;gBACF,IAAIgB,SAAS,GAAGpB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEU,MAAM,GAAGO,WAAW,CAAC,AAAC;gBAClD,MAAMG,SAAS,GAAGrB,IAAI,CAACsB,GAAG,CAACF,SAAS,GAAGF,WAAW,GAAG,CAAC,EAAEb,QAAQ,CAACK,MAAM,CAAC,AAAC;gBACzEG,WAAW,GAAGR,QAAQ,CAACkB,KAAK,CAACH,SAAS,EAAEC,SAAS,CAAC,CAAC;gBAEnD,mEAAmE;gBACnE,iEAAiE;gBACjE,IAAID,SAAS,GAAG,CAAC,EAAE;oBACjB,wEAAwE;oBACxEA,SAAS,IAAI,CAAC,CAAC;oBACfP,WAAW,GAAGjC,MAAK,QAAA,CAACE,GAAG,CAAC,KAAK,CAAC,GAAG+B,WAAW,CAAC;iBAC9C;gBACD,IAAIQ,SAAS,GAAGhB,QAAQ,CAACK,MAAM,EAAE;oBAC/BG,WAAW,IAAIjC,MAAK,QAAA,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC;iBACjC;gBAED,kHAAkH;gBAClHgC,UAAU,GAAG,CAACH,MAAM,IAAI,IAAI,GAAG,EAAE,GAAGtC,IAAI,CAACsC,MAAM,CAAC,GAAG/B,MAAK,QAAA,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC0C,KAAK,CAACH,SAAS,CAAC,CAAC;gBAEtFzB,IAAG,IAAA,CAACC,GAAG,CACL;oBAACmB,aAAa;oBAAE,EAAE;oBAAEF,WAAW;oBAAEC,UAAU;oBAAElC,MAAK,QAAA,CAACE,GAAG,CAAC,mBAAmB,CAAC;iBAAC,CAACN,IAAI,CAAC,IAAI,CAAC,CACxF,CAAC;aACH;SACF,MAAM;YACLmB,IAAG,IAAA,CAACC,GAAG,CAACP,SAAS,CAACiB,OAAO,CAAC,CAAC;SAC5B;KACF;IAED,IAAIlB,KAAK,QAAQ,GAAbA,KAAAA,CAAa,GAAbA,KAAK,CAAEsB,MAAM,EAAE;QACjBf,IAAG,IAAA,CAACC,GAAG,EAAE,CAAC;QACVD,IAAG,IAAA,CAACC,GAAG,CAAChB,MAAK,QAAA,CAAC4C,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QAEhC,MAAMC,UAAU,GAAGrC,KAAK,CAACsC,GAAG,CAAC,CAACC,KAAK,GAAK;YACtC,OAAO;gBACLC,KAAK,EAAED,KAAK,CAACE,UAAU;gBACvBC,QAAQ,EAAEtC,yBAAyB,CAACL,WAAW,EAAEwC,KAAK,CAAC;gBACvDI,QAAQ,EAAEJ,KAAK,CAACI,QAAQ;aACzB,CAAC;SACH,CAAC,AAAC;QAEHN,UAAU,CAACO,OAAO,CAAC,CAACL,KAAK,GAAK;YAC5B,MAAMM,QAAQ,GAAGC,aAAY,QAAA,CAACC,WAAW,GACrCD,CAAAA,GAAAA,aAAY,AAAgC,CAAA,QAAhC,CAACP,KAAK,CAACG,QAAQ,EAAEH,KAAK,CAACG,QAAQ,CAAC,GAC5CH,KAAK,CAACG,QAAQ,AAAC;YACnB,IAAIM,QAAQ,GAAGxD,MAAK,QAAA,CAACyD,IAAI,CAAC,CAAC,EAAE,EAAEV,KAAK,CAACC,KAAK,CAAC,EAAE,EAAEK,QAAQ,CAAC,CAAC,CAAC,CAAC,AAAC;YAC5D,IAAIN,KAAK,CAACI,QAAQ,EAAE;gBAClBK,QAAQ,GAAGxD,MAAK,QAAA,CAACE,GAAG,CAACsD,QAAQ,CAAC,CAAC;aAChC;YACDzC,IAAG,IAAA,CAACC,GAAG,CAACwC,QAAQ,CAAC,CAAC;SACnB,CAAC,CAAC;KACJ,MAAM;QACLzC,IAAG,IAAA,CAACC,GAAG,CAAChB,MAAK,QAAA,CAACyD,IAAI,CAAC,CAAC,EAAE,EAAE/C,KAAK,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KACzC;CACF;AAEM,eAAelB,aAAa,CAACiB,WAAmB,EAAE,EAAEG,KAAK,CAAA,EAAoB,EAAE;QA0B3EM,GAAgB;IAzBzB,IAAIN,KAAK,YAAYC,OAAW,YAAA,EAAE;QAChC,OAAO;KACR;IAED,MAAM,EAAE+C,SAAS,CAAA,EAAEC,eAAe,CAAA,EAAE,GAAG9C,OAAO,CAC5CC,CAAAA,GAAAA,YAAW,AAAgD,CAAA,QAAhD,CAACP,WAAW,EAAE,iCAAiC,CAAC,CAC5D,AAAC;IAEF,MAAMC,KAAK,GAAGmD,eAAe,CAACjD,KAAK,CAACF,KAAK,CAAC,AAAC;IAE3C,MAAMQ,GAAG,GAAG,IAAI0C,SAAS,CAAC;QACxBE,KAAK,EAAE,QAAQ;QACf1C,OAAO,EAAE;YACPQ,OAAO,EAAEhB,KAAK,CAACQ,OAAO;YACtB2C,aAAa,EAAE,EAAE;SAClB;QACDC,gBAAgB,EAAE,KAAK;QACvBtD,KAAK;QACLuD,QAAQ,EAAE,QAAQ;QAClBC,cAAc,EAAE,EAAE;KACnB,CAAC,AAAC;IAEH,MAAM,IAAIC,OAAO,CAAC,CAACC,GAAG,GAAKlD,GAAG,CAACmD,WAAW,CAAC,OAAO,EAAED,GAAG,CAAC;IAAA,CAAC,CAAC;QAGjDlD,IAA8B;IADvC3B,sBAAsB,CAACkB,WAAW,EAAE;QAClCC,KAAK,EAAEQ,CAAAA,IAA8B,GAA9BA,CAAAA,GAAgB,GAAhBA,GAAG,CAACoD,YAAY,SAAO,GAAvBpD,KAAAA,CAAuB,GAAvBA,QAAAA,GAAgB,CAAER,KAAK,SAAA,GAAvBQ,KAAAA,CAAuB,QAAER,KAAK,AAAP,YAAvBQ,IAA8B,GAAI,EAAE;QAC3CP,SAAS,EAAEO,GAAG,CAACP,SAAS;QACxBC,KAAK;KACN,CAAC,CAAC;CACJ;AAED,6EAA6E,CAC7E,SAAS2D,YAAY,CAAC,EAAE3D,KAAK,CAAA,EAAEH,WAAW,CAAA,EAAyC,EAIjF;IACA,MAAM,EAAEmD,SAAS,CAAA,EAAEC,eAAe,CAAA,EAAE,GAAG9C,OAAO,CAC5CC,CAAAA,GAAAA,YAAW,AAAgD,CAAA,QAAhD,CAACP,WAAW,EAAE,iCAAiC,CAAC,CAC5D,AAAC;IAEF,MAAMC,KAAK,GAAGmD,eAAe,CAACjD,KAAK,CAACF,KAAK,CAAC,AAAC;IAE3C,OAAO,IAAIkD,SAAS,CAAC;QACnBE,KAAK,EAAE,QAAQ;QACf1C,OAAO,EAAE;YACPQ,OAAO,EAAEhB,KAAK,CAACQ,OAAO;YACtB2C,aAAa,EAAE,EAAE;SAClB;QACDC,gBAAgB,EAAE,KAAK;QACvBtD,KAAK;QACLuD,QAAQ,EAAE,QAAQ;QAClBC,cAAc,EAAE,EAAE;KACnB,CAAC,CAAC;CACJ;AAGM,eAAezE,kBAAkB,CAAC,EACvCmB,KAAK,CAAA,EACLH,WAAW,CAAA,EAIZ,EAAE;QAMQS,GAAgB;IALzB,MAAMA,GAAG,GAAGqD,YAAY,CAAC;QAAE9D,WAAW;QAAEG,KAAK;KAAE,CAAC,AAAC;IAEjD,MAAM,IAAIuD,OAAO,CAAO,CAACC,GAAG,GAAKlD,GAAG,CAACmD,WAAW,CAAC,OAAO,EAAED,GAAG,CAAC;IAAA,CAAC,CAAC;QAGvDlD,IAA8B;IADvC3B,sBAAsB,CAACkB,WAAW,EAAE;QAClCC,KAAK,EAAEQ,CAAAA,IAA8B,GAA9BA,CAAAA,GAAgB,GAAhBA,GAAG,CAACoD,YAAY,SAAO,GAAvBpD,KAAAA,CAAuB,GAAvBA,QAAAA,GAAgB,CAAER,KAAK,SAAA,GAAvBQ,KAAAA,CAAuB,QAAER,KAAK,AAAP,YAAvBQ,IAA8B,GAAI,EAAE;QAC3CP,SAAS,EAAEO,GAAG,CAACP,SAAS;QACxBC,KAAK;KACN,CAAC,CAAC;CACJ;AAGM,eAAelB,wBAAwB,CAAC,EAC7CkB,KAAK,CAAA,EACLH,WAAW,CAAA,EACX+D,UAAU,CAAA,EAKX,EAAE;QAMQtD,GAAgB;IALzB,MAAMA,GAAG,GAAGqD,YAAY,CAAC;QAAE9D,WAAW;QAAEG,KAAK;KAAE,CAAC,AAAC;IAEjD,MAAM,IAAIuD,OAAO,CAAO,CAACC,GAAG,GAAKlD,GAAG,CAACmD,WAAW,CAAC,OAAO,EAAED,GAAG,CAAC;IAAA,CAAC,CAAC;QAGvDlD,KAA8B;IADvC3B,sBAAsB,CAACkB,WAAW,EAAE;QAClCC,KAAK,EAAEQ,CAAAA,KAA8B,GAA9BA,CAAAA,GAAgB,GAAhBA,GAAG,CAACoD,YAAY,SAAO,GAAvBpD,KAAAA,CAAuB,GAAvBA,QAAAA,GAAgB,CAAER,KAAK,SAAA,GAAvBQ,KAAAA,CAAuB,QAAER,KAAK,AAAP,YAAvBQ,KAA8B,GAAI,EAAE;QAC3CP,SAAS,EAAEO,GAAG,CAACP,SAAS;QACxBC,KAAK;KACN,CAAC,CAAC;IAEH,MAAM6D,aAAa,GAAG;QACpBC,gBAAgB,EAAE,CAAC;QACnBC,UAAU,EAAE,KAAK;QACjBC,IAAI,EAAE;YAAC1D,GAAG;SAAC;KACZ,AAAC;IACF,MAAM2D,IAAI,GAAG,CAAC,yLAAyL,EAAEC,IAAI,CAACC,SAAS,CACrNN,aAAa,CACd,CAAC,uBAAuB,CAAC,AAAC;IAE3B,MAAMO,iBAAiB,GAAG,MAAMC,CAAAA,GAAAA,yBAAwB,AAYvD,CAAA,yBAZuD,CACtDxE,WAAW,EACX,wBAAwB;IACxB,EAAE,EACFO,CAAAA,GAAAA,YAAW,AAAmC,CAAA,QAAnC,CAACP,WAAW,EAAE,oBAAoB,CAAC,EAC9C;QACEyE,GAAG,EAAE,IAAI;QACTC,QAAQ,EAAE,KAAK;QACfC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,EAAE;QACXb,UAAU;KACX,CACF,AAAC;IAEF,MAAMc,UAAU,GAAGT,IAAI,CAACU,OAAO,CAAC,SAAS,EAAE,CAAC,YAAY,EAAEP,iBAAiB,CAAC,iBAAiB,CAAC,CAAC,AAAC;IAChG,OAAOM,UAAU,CAAC;CACnB"}