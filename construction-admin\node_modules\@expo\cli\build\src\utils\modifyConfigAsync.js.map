{"version": 3, "sources": ["../../../src/utils/modifyConfigAsync.ts"], "sourcesContent": ["import { ExpoConfig, modifyConfigAsync } from '@expo/config';\nimport chalk from 'chalk';\n\nimport { SilentError } from './errors';\nimport * as Log from '../log';\n\n/** Wraps `[@expo/config] modifyConfigAsync()` and adds additional logging. */\nexport async function attemptModification(\n  projectRoot: string,\n  edits: Partial<ExpoConfig>,\n  exactEdits: Partial<ExpoConfig>\n): Promise<void> {\n  const modification = await modifyConfigAsync(projectRoot, edits, {\n    skipSDKVersionRequirement: true,\n  });\n  if (modification.type === 'success') {\n    Log.log();\n  } else {\n    warnAboutConfigAndThrow(modification.type, modification.message!, exactEdits);\n  }\n}\n\nfunction logNoConfig() {\n  Log.log(\n    chalk.yellow(\n      `No Expo config was found. Please create an Expo config (${chalk.bold`app.json`} or ${chalk.bold`app.config.js`}) in your project root.`\n    )\n  );\n}\n\nexport function warnAboutConfigAndThrow(type: string, message: string, edits: Partial<ExpoConfig>) {\n  Log.log();\n  if (type === 'warn') {\n    // The project is using a dynamic config, give the user a helpful log and bail out.\n    Log.log(chalk.yellow(message));\n  } else {\n    logNoConfig();\n  }\n\n  notifyAboutManualConfigEdits(edits);\n  throw new SilentError();\n}\n\nfunction notifyAboutManualConfigEdits(edits: Partial<ExpoConfig>) {\n  Log.log(chalk.cyan(`Please add the following to your Expo config`));\n  Log.log();\n  Log.log(JSON.stringify(edits, null, 2));\n  Log.log();\n}\n"], "names": ["attemptModification", "warnAboutConfigAndThrow", "Log", "projectRoot", "edits", "exactEdits", "modification", "modifyConfigAsync", "skipSDKVersionRequirement", "type", "log", "message", "logNoConfig", "chalk", "yellow", "bold", "notifyAboutManualConfigEdits", "SilentError", "cyan", "JSON", "stringify"], "mappings": "AAAA;;;;QAOsBA,mBAAmB,GAAnBA,mBAAmB;QAuBzBC,uBAAuB,GAAvBA,uBAAuB;AA9BO,IAAA,OAAc,WAAd,cAAc,CAAA;AAC1C,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEG,IAAA,OAAU,WAAV,UAAU,CAAA;AAC1BC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGR,eAAeF,mBAAmB,CACvCG,WAAmB,EACnBC,KAA0B,EAC1BC,UAA+B,EAChB;IACf,MAAMC,YAAY,GAAG,MAAMC,CAAAA,GAAAA,OAAiB,AAE1C,CAAA,kBAF0C,CAACJ,WAAW,EAAEC,KAAK,EAAE;QAC/DI,yBAAyB,EAAE,IAAI;KAChC,CAAC,AAAC;IACH,IAAIF,YAAY,CAACG,IAAI,KAAK,SAAS,EAAE;QACnCP,GAAG,CAACQ,GAAG,EAAE,CAAC;KACX,MAAM;QACLT,uBAAuB,CAACK,YAAY,CAACG,IAAI,EAAEH,YAAY,CAACK,OAAO,EAAGN,UAAU,CAAC,CAAC;KAC/E;CACF;AAED,SAASO,WAAW,GAAG;IACrBV,GAAG,CAACQ,GAAG,CACLG,MAAK,QAAA,CAACC,MAAM,CACV,CAAC,wDAAwD,EAAED,MAAK,QAAA,CAACE,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAEF,MAAK,QAAA,CAACE,IAAI,CAAC,aAAa,CAAC,CAAC,uBAAuB,CAAC,CACzI,CACF,CAAC;CACH;AAEM,SAASd,uBAAuB,CAACQ,IAAY,EAAEE,OAAe,EAAEP,KAA0B,EAAE;IACjGF,GAAG,CAACQ,GAAG,EAAE,CAAC;IACV,IAAID,IAAI,KAAK,MAAM,EAAE;QACnB,mFAAmF;QACnFP,GAAG,CAACQ,GAAG,CAACG,MAAK,QAAA,CAACC,MAAM,CAACH,OAAO,CAAC,CAAC,CAAC;KAChC,MAAM;QACLC,WAAW,EAAE,CAAC;KACf;IAEDI,4BAA4B,CAACZ,KAAK,CAAC,CAAC;IACpC,MAAM,IAAIa,OAAW,YAAA,EAAE,CAAC;CACzB;AAED,SAASD,4BAA4B,CAACZ,KAA0B,EAAE;IAChEF,GAAG,CAACQ,GAAG,CAACG,MAAK,QAAA,CAACK,IAAI,CAAC,CAAC,4CAA4C,CAAC,CAAC,CAAC,CAAC;IACpEhB,GAAG,CAACQ,GAAG,EAAE,CAAC;IACVR,GAAG,CAACQ,GAAG,CAACS,IAAI,CAACC,SAAS,CAAChB,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACxCF,GAAG,CAACQ,GAAG,EAAE,CAAC;CACX"}