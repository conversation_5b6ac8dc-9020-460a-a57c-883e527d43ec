{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/createDebugMiddleware.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { createInspectorDeviceClass } from './InspectorDevice';\nimport { createInspectorProxyClass } from './InspectorProxy';\nimport { Log } from '../../../../log';\nimport { type MetroBundlerDevServer } from '../MetroBundlerDevServer';\n\nexport function createDebugMiddleware(metroBundler: MetroBundlerDevServer) {\n  // Load the React Native debugging tools from project\n  // TODO: check if this works with isolated modules\n  const { createDevMiddleware, unstable_Device, unstable_InspectorProxy } =\n    require('@react-native/dev-middleware') as typeof import('@react-native/dev-middleware');\n\n  // Create the extended inspector proxy, using our own device class\n  const ExpoInspectorProxy = createInspectorProxyClass(\n    unstable_InspectorProxy,\n    createInspectorDeviceClass(metroBundler, unstable_Device)\n  );\n\n  const { middleware, websocketEndpoints } = createDevMiddleware({\n    projectRoot: metroBundler.projectRoot,\n    serverBaseUrl: metroBundler.getUrlCreator().constructUrl({ scheme: 'http', hostType: 'lan' }),\n    logger: createLogger(chalk.bold('Debug:')),\n    unstable_InspectorProxy: ExpoInspectorProxy,\n    unstable_experiments: {\n      enableNewDebugger: true,\n    },\n  });\n\n  return {\n    debugMiddleware: middleware,\n    debugWebsocketEndpoints: websocketEndpoints,\n  };\n}\n\nfunction createLogger(\n  logPrefix: string\n): Parameters<typeof import('@react-native/dev-middleware').createDevMiddleware>[0]['logger'] {\n  return {\n    info: (...args) => Log.log(logPrefix, ...args),\n    warn: (...args) => Log.warn(logPrefix, ...args),\n    error: (...args) => Log.error(logPrefix, ...args),\n  };\n}\n"], "names": ["createDebugMiddleware", "metroBundler", "createDevMiddleware", "unstable_Device", "unstable_InspectorProxy", "require", "ExpoInspectorProxy", "createInspectorProxyClass", "createInspectorDeviceClass", "middleware", "websocketEndpoints", "projectRoot", "serverBaseUrl", "getUrlCreator", "constructUrl", "scheme", "hostType", "logger", "createLogger", "chalk", "bold", "unstable_experiments", "enableNewDebugger", "debugMiddleware", "debugWebsocketEndpoints", "logPrefix", "info", "args", "Log", "log", "warn", "error"], "mappings": "AAAA;;;;QAOgBA,qBAAqB,GAArBA,qBAAqB;AAPnB,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEkB,IAAA,gBAAmB,WAAnB,mBAAmB,CAAA;AACpB,IAAA,eAAkB,WAAlB,kBAAkB,CAAA;AACxC,IAAA,IAAiB,WAAjB,iBAAiB,CAAA;;;;;;AAG9B,SAASA,qBAAqB,CAACC,YAAmC,EAAE;IACzE,qDAAqD;IACrD,kDAAkD;IAClD,MAAM,EAAEC,mBAAmB,CAAA,EAAEC,eAAe,CAAA,EAAEC,uBAAuB,CAAA,EAAE,GACrEC,OAAO,CAAC,8BAA8B,CAAC,AAAiD,AAAC;IAE3F,kEAAkE;IAClE,MAAMC,kBAAkB,GAAGC,CAAAA,GAAAA,eAAyB,AAGnD,CAAA,0BAHmD,CAClDH,uBAAuB,EACvBI,CAAAA,GAAAA,gBAA0B,AAA+B,CAAA,2BAA/B,CAACP,YAAY,EAAEE,eAAe,CAAC,CAC1D,AAAC;IAEF,MAAM,EAAEM,UAAU,CAAA,EAAEC,kBAAkB,CAAA,EAAE,GAAGR,mBAAmB,CAAC;QAC7DS,WAAW,EAAEV,YAAY,CAACU,WAAW;QACrCC,aAAa,EAAEX,YAAY,CAACY,aAAa,EAAE,CAACC,YAAY,CAAC;YAAEC,MAAM,EAAE,MAAM;YAAEC,QAAQ,EAAE,KAAK;SAAE,CAAC;QAC7FC,MAAM,EAAEC,YAAY,CAACC,MAAK,QAAA,CAACC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1ChB,uBAAuB,EAAEE,kBAAkB;QAC3Ce,oBAAoB,EAAE;YACpBC,iBAAiB,EAAE,IAAI;SACxB;KACF,CAAC,AAAC;IAEH,OAAO;QACLC,eAAe,EAAEd,UAAU;QAC3Be,uBAAuB,EAAEd,kBAAkB;KAC5C,CAAC;CACH;AAED,SAASQ,YAAY,CACnBO,SAAiB,EAC2E;IAC5F,OAAO;QACLC,IAAI,EAAE,CAAIC,GAAAA,IAAI,GAAKC,IAAG,IAAA,CAACC,GAAG,CAACJ,SAAS,KAAKE,IAAI,CAAC;QAAA;QAC9CG,IAAI,EAAE,CAAIH,GAAAA,IAAI,GAAKC,IAAG,IAAA,CAACE,IAAI,CAACL,SAAS,KAAKE,IAAI,CAAC;QAAA;QAC/CI,KAAK,EAAE,CAAIJ,GAAAA,IAAI,GAAKC,IAAG,IAAA,CAACG,KAAK,CAACN,SAAS,KAAKE,IAAI,CAAC;KAClD,CAAC;CACH"}