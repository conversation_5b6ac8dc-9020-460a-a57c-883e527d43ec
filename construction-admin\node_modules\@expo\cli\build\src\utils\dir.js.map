{"version": 3, "sources": ["../../../src/utils/dir.ts"], "sourcesContent": ["import fs from 'fs-extra';\n\nexport function fileExistsSync(file: string): boolean {\n  try {\n    return fs.statSync(file)?.isFile() ?? false;\n  } catch {\n    return false;\n  }\n}\n\nexport function directoryExistsSync(file: string): boolean {\n  try {\n    return fs.statSync(file)?.isDirectory() ?? false;\n  } catch {\n    return false;\n  }\n}\n\nexport async function directoryExistsAsync(file: string): Promise<boolean> {\n  return (await fs.promises.stat(file).catch(() => null))?.isDirectory() ?? false;\n}\n\nexport async function fileExistsAsync(file: string): Promise<boolean> {\n  return (await fs.promises.stat(file).catch(() => null))?.isFile() ?? false;\n}\n\nexport const ensureDirectoryAsync = (path: string) => fs.promises.mkdir(path, { recursive: true });\n\nexport const ensureDirectory = (path: string) => fs.mkdirSync(path, { recursive: true });\n\nexport const copySync = fs.copySync;\n\nexport const copyAsync = fs.copy;\n\nexport const removeAsync = fs.remove;\n"], "names": ["fileExistsSync", "directoryExistsSync", "directoryExistsAsync", "fileExistsAsync", "file", "fs", "statSync", "isFile", "isDirectory", "promises", "stat", "catch", "ensureDirectoryAsync", "path", "mkdir", "recursive", "ensureDirectory", "mkdirSync", "copySync", "copyAsync", "copy", "removeAsync", "remove"], "mappings": "AAAA;;;;QAEgBA,cAAc,GAAdA,cAAc;QAQdC,mBAAmB,GAAnBA,mBAAmB;QAQbC,oBAAoB,GAApBA,oBAAoB;QAIpBC,eAAe,GAAfA,eAAe;;AAtBtB,IAAA,QAAU,kCAAV,UAAU,EAAA;;;;;;AAElB,SAASH,cAAc,CAACI,IAAY,EAAW;IACpD,IAAI;YACKC,GAAiB;YAAjBA,IAA2B;QAAlC,OAAOA,CAAAA,IAA2B,GAA3BA,CAAAA,GAAiB,GAAjBA,QAAE,QAAA,CAACC,QAAQ,CAACF,IAAI,CAAC,SAAQ,GAAzBC,KAAAA,CAAyB,GAAzBA,GAAiB,CAAEE,MAAM,EAAE,YAA3BF,IAA2B,GAAI,KAAK,CAAC;KAC7C,CAAC,OAAM;QACN,OAAO,KAAK,CAAC;KACd;CACF;AAEM,SAASJ,mBAAmB,CAACG,IAAY,EAAW;IACzD,IAAI;YACKC,GAAiB;YAAjBA,IAAgC;QAAvC,OAAOA,CAAAA,IAAgC,GAAhCA,CAAAA,GAAiB,GAAjBA,QAAE,QAAA,CAACC,QAAQ,CAACF,IAAI,CAAC,SAAa,GAA9BC,KAAAA,CAA8B,GAA9BA,GAAiB,CAAEG,WAAW,EAAE,YAAhCH,IAAgC,GAAI,KAAK,CAAC;KAClD,CAAC,OAAM;QACN,OAAO,KAAK,CAAC;KACd;CACF;AAEM,eAAeH,oBAAoB,CAACE,IAAY,EAAoB;QAClE,GAAgD;QAAhD,IAA+D;IAAtE,OAAO,CAAA,IAA+D,GAA/D,CAAA,GAAgD,GAA/C,MAAMC,QAAE,QAAA,CAACI,QAAQ,CAACC,IAAI,CAACN,IAAI,CAAC,CAACO,KAAK,CAAC,IAAM,IAAI;IAAA,CAAC,SAAc,GAA7D,KAAA,CAA6D,GAA7D,GAAgD,CAAEH,WAAW,EAAE,YAA/D,IAA+D,GAAI,KAAK,CAAC;CACjF;AAEM,eAAeL,eAAe,CAACC,IAAY,EAAoB;QAC7D,GAAgD;QAAhD,IAA0D;IAAjE,OAAO,CAAA,IAA0D,GAA1D,CAAA,GAAgD,GAA/C,MAAMC,QAAE,QAAA,CAACI,QAAQ,CAACC,IAAI,CAACN,IAAI,CAAC,CAACO,KAAK,CAAC,IAAM,IAAI;IAAA,CAAC,SAAS,GAAxD,KAAA,CAAwD,GAAxD,GAAgD,CAAEJ,MAAM,EAAE,YAA1D,IAA0D,GAAI,KAAK,CAAC;CAC5E;AAEM,MAAMK,oBAAoB,GAAG,CAACC,IAAY,GAAKR,QAAE,QAAA,CAACI,QAAQ,CAACK,KAAK,CAACD,IAAI,EAAE;QAAEE,SAAS,EAAE,IAAI;KAAE,CAAC;AAAC;QAAtFH,oBAAoB,GAApBA,oBAAoB;AAE1B,MAAMI,eAAe,GAAG,CAACH,IAAY,GAAKR,QAAE,QAAA,CAACY,SAAS,CAACJ,IAAI,EAAE;QAAEE,SAAS,EAAE,IAAI;KAAE,CAAC;AAAC;QAA5EC,eAAe,GAAfA,eAAe;AAErB,MAAME,QAAQ,GAAGb,QAAE,QAAA,CAACa,QAAQ,AAAC;QAAvBA,QAAQ,GAARA,QAAQ;AAEd,MAAMC,SAAS,GAAGd,QAAE,QAAA,CAACe,IAAI,AAAC;QAApBD,SAAS,GAATA,SAAS;AAEf,MAAME,WAAW,GAAGhB,QAAE,QAAA,CAACiB,MAAM,AAAC;QAAxBD,WAAW,GAAXA,WAAW"}