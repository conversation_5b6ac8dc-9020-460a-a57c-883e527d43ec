{"version": 3, "sources": ["../../../src/utils/args.ts"], "sourcesContent": ["// Common utilities for interacting with `args` library.\n// These functions should be used by every command.\nimport arg from 'arg';\nimport chalk from 'chalk';\nimport { existsSync } from 'fs';\nimport { resolve } from 'path';\n\nimport * as Log from '../log';\n\n/**\n * Parse the first argument as a project directory.\n *\n * @returns valid project directory.\n */\nexport function getProjectRoot(args: arg.Result<arg.Spec>) {\n  const projectRoot = resolve(args._[0] || '.');\n\n  if (!existsSync(projectRoot)) {\n    Log.exit(`Invalid project root: ${projectRoot}`);\n  }\n\n  return projectRoot;\n}\n\n/**\n * Parse args and assert unknown options.\n *\n * @param schema the `args` schema for parsing the command line arguments.\n * @param argv extra strings\n * @returns processed args object.\n */\nexport function assertArgs(schema: arg.Spec, argv?: string[]): arg.Result<arg.Spec> {\n  return assertWithOptionsArgs(schema, { argv });\n}\n\nexport function assertWithOptionsArgs(\n  schema: arg.Spec,\n  options: arg.Options\n): arg.Result<arg.Spec> {\n  try {\n    return arg(schema, options);\n  } catch (error: any) {\n    // Handle errors caused by user input.\n    // Only errors from `arg`, which does not start with `ARG_CONFIG_` are user input errors.\n    // See: https://github.com/vercel/arg/releases/tag/5.0.0\n    if ('code' in error && error.code.startsWith('ARG_') && !error.code.startsWith('ARG_CONFIG_')) {\n      Log.exit(error.message, 1);\n    }\n    // Otherwise rethrow the error.\n    throw error;\n  }\n}\n\nexport function printHelp(info: string, usage: string, options: string, extra: string = ''): never {\n  Log.exit(\n    chalk`\n  {bold Info}\n    ${info}\n\n  {bold Usage}\n    {dim $} ${usage}\n\n  {bold Options}\n    ${options.split('\\n').join('\\n    ')}\n` + extra,\n    0\n  );\n}\n"], "names": ["getProjectRoot", "assertArgs", "assertWithOptionsArgs", "printHelp", "Log", "args", "projectRoot", "resolve", "_", "existsSync", "exit", "schema", "argv", "options", "arg", "error", "code", "startsWith", "message", "info", "usage", "extra", "chalk", "split", "join"], "mappings": "AAEA;;;;QAYgBA,cAAc,GAAdA,cAAc;QAiBdC,UAAU,GAAVA,UAAU;QAIVC,qBAAqB,GAArBA,qBAAqB;QAkBrBC,SAAS,GAATA,SAAS;AAnDT,IAAA,IAAK,kCAAL,KAAK,EAAA;AACH,IAAA,MAAO,kCAAP,OAAO,EAAA;AACE,IAAA,GAAI,WAAJ,IAAI,CAAA;AACP,IAAA,KAAM,WAAN,MAAM,CAAA;AAElBC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOR,SAASJ,cAAc,CAACK,IAA0B,EAAE;IACzD,MAAMC,WAAW,GAAGC,CAAAA,GAAAA,KAAO,AAAkB,CAAA,QAAlB,CAACF,IAAI,CAACG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,AAAC;IAE9C,IAAI,CAACC,CAAAA,GAAAA,GAAU,AAAa,CAAA,WAAb,CAACH,WAAW,CAAC,EAAE;QAC5BF,GAAG,CAACM,IAAI,CAAC,CAAC,sBAAsB,EAAEJ,WAAW,CAAC,CAAC,CAAC,CAAC;KAClD;IAED,OAAOA,WAAW,CAAC;CACpB;AASM,SAASL,UAAU,CAACU,MAAgB,EAAEC,IAAe,EAAwB;IAClF,OAAOV,qBAAqB,CAACS,MAAM,EAAE;QAAEC,IAAI;KAAE,CAAC,CAAC;CAChD;AAEM,SAASV,qBAAqB,CACnCS,MAAgB,EAChBE,OAAoB,EACE;IACtB,IAAI;QACF,OAAOC,CAAAA,GAAAA,IAAG,AAAiB,CAAA,QAAjB,CAACH,MAAM,EAAEE,OAAO,CAAC,CAAC;KAC7B,CAAC,OAAOE,KAAK,EAAO;QACnB,sCAAsC;QACtC,yFAAyF;QACzF,wDAAwD;QACxD,IAAI,MAAM,IAAIA,KAAK,IAAIA,KAAK,CAACC,IAAI,CAACC,UAAU,CAAC,MAAM,CAAC,IAAI,CAACF,KAAK,CAACC,IAAI,CAACC,UAAU,CAAC,aAAa,CAAC,EAAE;YAC7Fb,GAAG,CAACM,IAAI,CAACK,KAAK,CAACG,OAAO,EAAE,CAAC,CAAC,CAAC;SAC5B;QACD,+BAA+B;QAC/B,MAAMH,KAAK,CAAC;KACb;CACF;AAEM,SAASZ,SAAS,CAACgB,IAAY,EAAEC,KAAa,EAAEP,OAAe,EAAEQ,KAAa,GAAG,EAAE,EAAS;IACjGjB,GAAG,CAACM,IAAI,CACNY,MAAK,QAAA,CAAC;;IAEN,EAAEH,IAAI,CAAC;;;YAGC,EAAEC,KAAK,CAAC;;;IAGhB,EAAEP,OAAO,CAACU,KAAK,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACzC,CAAC,GAAGH,KAAK,EACL,CAAC,CACF,CAAC;CACH"}