{"version": 3, "sources": ["../../../src/utils/findUp.ts"], "sourcesContent": ["import path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { CommandError } from '../utils/errors';\n\n/** Look up directories until one with a `package.json` can be found, assert if none can be found. */\nexport function findUpProjectRootOrAssert(cwd: string): string {\n  const projectRoot = findUpProjectRoot(cwd);\n  if (!projectRoot) {\n    throw new CommandError(`Project root directory not found (working directory: ${cwd})`);\n  }\n  return projectRoot;\n}\n\nfunction findUpProjectRoot(cwd: string): string | null {\n  if (['.', path.sep].includes(cwd)) return null;\n\n  const found = resolveFrom.silent(cwd, './package.json');\n  if (found) {\n    return path.dirname(found);\n  }\n  return findUpProjectRoot(path.dirname(cwd));\n}\n"], "names": ["findUpProjectRootOrAssert", "cwd", "projectRoot", "findUpProjectRoot", "CommandError", "path", "sep", "includes", "found", "resolveFrom", "silent", "dirname"], "mappings": "AAAA;;;;QAMgBA,yBAAyB,GAAzBA,yBAAyB;AANxB,IAAA,KAAM,kCAAN,MAAM,EAAA;AACC,IAAA,YAAc,kCAAd,cAAc,EAAA;AAET,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;;;;;;AAGvC,SAASA,yBAAyB,CAACC,GAAW,EAAU;IAC7D,MAAMC,WAAW,GAAGC,iBAAiB,CAACF,GAAG,CAAC,AAAC;IAC3C,IAAI,CAACC,WAAW,EAAE;QAChB,MAAM,IAAIE,OAAY,aAAA,CAAC,CAAC,qDAAqD,EAAEH,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACxF;IACD,OAAOC,WAAW,CAAC;CACpB;AAED,SAASC,iBAAiB,CAACF,GAAW,EAAiB;IACrD,IAAI;QAAC,GAAG;QAAEI,KAAI,QAAA,CAACC,GAAG;KAAC,CAACC,QAAQ,CAACN,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC;IAE/C,MAAMO,KAAK,GAAGC,YAAW,QAAA,CAACC,MAAM,CAACT,GAAG,EAAE,gBAAgB,CAAC,AAAC;IACxD,IAAIO,KAAK,EAAE;QACT,OAAOH,KAAI,QAAA,CAACM,OAAO,CAACH,KAAK,CAAC,CAAC;KAC5B;IACD,OAAOL,iBAAiB,CAACE,KAAI,QAAA,CAACM,OAAO,CAACV,GAAG,CAAC,CAAC,CAAC;CAC7C"}