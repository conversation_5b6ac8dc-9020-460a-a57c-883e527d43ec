{"version": 3, "sources": ["../../../../src/start/project/devices.ts"], "sourcesContent": ["import { createTemporaryProjectFile } from './dotExpo';\n\nconst debug = require('debug')('expo:start:project:devices') as typeof console.log;\n\nexport type DeviceInfo = {\n  installationId: string;\n  lastUsed: number;\n};\n\nexport type DevicesInfo = {\n  devices: DeviceInfo[];\n};\n\nconst DEVICES_FILE_NAME = 'devices.json';\n\nconst MILLISECONDS_IN_30_DAYS = 30 * 24 * 60 * 60 * 1000;\n\nexport const DevicesFile = createTemporaryProjectFile<DevicesInfo>(DEVICES_FILE_NAME, {\n  devices: [],\n});\n\nlet devicesInfo: DevicesInfo | null = null;\n\nexport async function getDevicesInfoAsync(projectRoot: string): Promise<DevicesInfo> {\n  if (devicesInfo) {\n    return devicesInfo;\n  }\n  return readDevicesInfoAsync(projectRoot);\n}\n\nexport async function readDevicesInfoAsync(projectRoot: string): Promise<DevicesInfo> {\n  try {\n    devicesInfo = await DevicesFile.readAsync(projectRoot);\n\n    // if the file on disk has old devices, filter them out here before we use them\n    const filteredDevices = filterOldDevices(devicesInfo.devices);\n    if (filteredDevices.length < devicesInfo.devices.length) {\n      devicesInfo = {\n        ...devicesInfo,\n        devices: filteredDevices,\n      };\n      // save the newly filtered list for consistency\n      try {\n        await setDevicesInfoAsync(projectRoot, devicesInfo);\n      } catch {\n        // do nothing here, we'll just keep using the filtered list in memory for now\n      }\n    }\n\n    return devicesInfo;\n  } catch {\n    return await DevicesFile.setAsync(projectRoot, { devices: [] });\n  }\n}\n\nexport async function setDevicesInfoAsync(\n  projectRoot: string,\n  json: DevicesInfo\n): Promise<DevicesInfo> {\n  devicesInfo = json;\n  return await DevicesFile.setAsync(projectRoot, json);\n}\n\nexport async function saveDevicesAsync(\n  projectRoot: string,\n  deviceIds: string | string[]\n): Promise<void> {\n  const currentTime = Date.now();\n  const newDeviceIds = typeof deviceIds === 'string' ? [deviceIds] : deviceIds;\n\n  debug(`Saving devices: ${newDeviceIds}`);\n  const { devices } = await getDevicesInfoAsync(projectRoot);\n  const newDevicesJson = devices\n    .filter((device) => !newDeviceIds.includes(device.installationId))\n    .concat(newDeviceIds.map((deviceId) => ({ installationId: deviceId, lastUsed: currentTime })));\n  await setDevicesInfoAsync(projectRoot, { devices: filterOldDevices(newDevicesJson) });\n}\n\nfunction filterOldDevices(devices: DeviceInfo[]) {\n  const currentTime = Date.now();\n  return (\n    devices\n      // filter out any devices that haven't been used to open this project in 30 days\n      .filter((device) => currentTime - device.lastUsed <= MILLISECONDS_IN_30_DAYS)\n      // keep only the 10 most recently used devices\n      .sort((a, b) => b.lastUsed - a.lastUsed)\n      .slice(0, 10)\n  );\n}\n"], "names": ["getDevicesInfoAsync", "readDevicesInfoAsync", "setDevicesInfoAsync", "saveDevicesAsync", "debug", "require", "DEVICES_FILE_NAME", "MILLISECONDS_IN_30_DAYS", "DevicesFile", "createTemporaryProjectFile", "devices", "devicesInfo", "projectRoot", "readAsync", "filteredDevices", "filterOldDevices", "length", "setAsync", "json", "deviceIds", "currentTime", "Date", "now", "newDeviceIds", "newDevicesJson", "filter", "device", "includes", "installationId", "concat", "map", "deviceId", "lastUsed", "sort", "a", "b", "slice"], "mappings": "AAAA;;;;QAuBsBA,mBAAmB,GAAnBA,mBAAmB;QAOnBC,oBAAoB,GAApBA,oBAAoB;QAyBpBC,mBAAmB,GAAnBA,mBAAmB;QAQnBC,gBAAgB,GAAhBA,gBAAgB;;AA/DK,IAAA,QAAW,WAAX,WAAW,CAAA;AAEtD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,4BAA4B,CAAC,AAAsB,AAAC;AAWnF,MAAMC,iBAAiB,GAAG,cAAc,AAAC;AAEzC,MAAMC,uBAAuB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,AAAC;AAElD,MAAMC,WAAW,GAAGC,CAAAA,GAAAA,QAA0B,AAEnD,CAAA,2BAFmD,CAAcH,iBAAiB,EAAE;IACpFI,OAAO,EAAE,EAAE;CACZ,CAAC,AAAC;QAFUF,WAAW,GAAXA,WAAW;AAIxB,IAAIG,WAAW,GAAuB,IAAI,AAAC;AAEpC,eAAeX,mBAAmB,CAACY,WAAmB,EAAwB;IACnF,IAAID,WAAW,EAAE;QACf,OAAOA,WAAW,CAAC;KACpB;IACD,OAAOV,oBAAoB,CAACW,WAAW,CAAC,CAAC;CAC1C;AAEM,eAAeX,oBAAoB,CAACW,WAAmB,EAAwB;IACpF,IAAI;QACFD,WAAW,GAAG,MAAMH,WAAW,CAACK,SAAS,CAACD,WAAW,CAAC,CAAC;QAEvD,+EAA+E;QAC/E,MAAME,eAAe,GAAGC,gBAAgB,CAACJ,WAAW,CAACD,OAAO,CAAC,AAAC;QAC9D,IAAII,eAAe,CAACE,MAAM,GAAGL,WAAW,CAACD,OAAO,CAACM,MAAM,EAAE;YACvDL,WAAW,GAAG;gBACZ,GAAGA,WAAW;gBACdD,OAAO,EAAEI,eAAe;aACzB,CAAC;YACF,+CAA+C;YAC/C,IAAI;gBACF,MAAMZ,mBAAmB,CAACU,WAAW,EAAED,WAAW,CAAC,CAAC;aACrD,CAAC,OAAM;YACN,6EAA6E;aAC9E;SACF;QAED,OAAOA,WAAW,CAAC;KACpB,CAAC,OAAM;QACN,OAAO,MAAMH,WAAW,CAACS,QAAQ,CAACL,WAAW,EAAE;YAAEF,OAAO,EAAE,EAAE;SAAE,CAAC,CAAC;KACjE;CACF;AAEM,eAAeR,mBAAmB,CACvCU,WAAmB,EACnBM,IAAiB,EACK;IACtBP,WAAW,GAAGO,IAAI,CAAC;IACnB,OAAO,MAAMV,WAAW,CAACS,QAAQ,CAACL,WAAW,EAAEM,IAAI,CAAC,CAAC;CACtD;AAEM,eAAef,gBAAgB,CACpCS,WAAmB,EACnBO,SAA4B,EACb;IACf,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,EAAE,AAAC;IAC/B,MAAMC,YAAY,GAAG,OAAOJ,SAAS,KAAK,QAAQ,GAAG;QAACA,SAAS;KAAC,GAAGA,SAAS,AAAC;IAE7Ef,KAAK,CAAC,CAAC,gBAAgB,EAAEmB,YAAY,CAAC,CAAC,CAAC,CAAC;IACzC,MAAM,EAAEb,OAAO,CAAA,EAAE,GAAG,MAAMV,mBAAmB,CAACY,WAAW,CAAC,AAAC;IAC3D,MAAMY,cAAc,GAAGd,OAAO,CAC3Be,MAAM,CAAC,CAACC,MAAM,GAAK,CAACH,YAAY,CAACI,QAAQ,CAACD,MAAM,CAACE,cAAc,CAAC;IAAA,CAAC,CACjEC,MAAM,CAACN,YAAY,CAACO,GAAG,CAAC,CAACC,QAAQ,GAAK,CAAC;YAAEH,cAAc,EAAEG,QAAQ;YAAEC,QAAQ,EAAEZ,WAAW;SAAE,CAAC;IAAA,CAAC,CAAC,AAAC;IACjG,MAAMlB,mBAAmB,CAACU,WAAW,EAAE;QAAEF,OAAO,EAAEK,gBAAgB,CAACS,cAAc,CAAC;KAAE,CAAC,CAAC;CACvF;AAED,SAAST,gBAAgB,CAACL,OAAqB,EAAE;IAC/C,MAAMU,WAAW,GAAGC,IAAI,CAACC,GAAG,EAAE,AAAC;IAC/B,OACEZ,OAAO,AACL,gFAAgF;KAC/Ee,MAAM,CAAC,CAACC,MAAM,GAAKN,WAAW,GAAGM,MAAM,CAACM,QAAQ,IAAIzB,uBAAuB;IAAA,CAAC,AAC7E,8CAA8C;KAC7C0B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAKA,CAAC,CAACH,QAAQ,GAAGE,CAAC,CAACF,QAAQ;IAAA,CAAC,CACvCI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACf;CACH"}