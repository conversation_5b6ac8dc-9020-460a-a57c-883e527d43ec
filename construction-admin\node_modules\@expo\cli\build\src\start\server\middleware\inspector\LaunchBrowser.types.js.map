{"version": 3, "sources": ["../../../../../../src/start/server/middleware/inspector/LaunchBrowser.types.ts"], "sourcesContent": ["export interface LaunchBrowserInstance {\n  close: () => Promise<void>;\n}\n\n/**\n * Supported browser types enum\n */\nexport const LaunchBrowserTypesEnum = {\n  CHROME: 'Google Chrome',\n  EDGE: 'Microsoft Edge',\n  BRAVE: 'Brave',\n} as const;\n\n/**\n * Supported browser types\n */\nexport type LaunchBrowserTypes =\n  (typeof LaunchBrowserTypesEnum)[keyof typeof LaunchBrowserTypesEnum];\n\n/**\n * A browser launcher\n */\nexport interface LaunchBrowser {\n  /**\n   * Return whether the given `browserType` is supported\n   */\n  isSupportedBrowser: (browserType: LaunchBrowserTypes) => Promise<boolean>;\n\n  /**\n   * Create temp directory for browser profile\n   *\n   * @param baseDirName The base directory name for the created directory\n   */\n  createTempBrowserDir: (baseDirName: string) => Promise<string>;\n\n  /**\n   * Launch the browser\n   */\n  launchAsync: (browserType: LaunchBrowserTypes, args: string[]) => Promise<LaunchBrowserInstance>;\n\n  /**\n   * Close current browser instance\n   */\n  close: () => Promise<void>;\n}\n"], "names": ["LaunchBrowserTypesEnum", "CHROME", "EDGE", "BRAVE"], "mappings": "AAAA;;;;;AAOO,MAAMA,sBAAsB,GAAG;IACpCC,MAAM,EAAE,eAAe;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,OAAO;CACf,AAAS,AAAC;QAJEH,sBAAsB,GAAtBA,sBAAsB"}