{"version": 3, "sources": ["../../../../../src/start/server/middleware/ContextModuleSourceMapsMiddleware.ts"], "sourcesContent": ["import { ServerRequest, ServerResponse } from './server.types';\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:metro-context-modules'\n) as typeof console.log;\n\n/**\n * Source maps for `require.context` modules aren't supported in the Metro dev server\n * we should intercept the request and return a noop response to prevent Chrome/Metro\n * from erroring out.\n */\nexport class ContextModuleSourceMapsMiddleware {\n  getHandler() {\n    return (req: ServerRequest, res: ServerResponse, next: any) => {\n      if (!req?.url || (req.method !== 'GET' && req.method !== 'HEAD')) {\n        return next();\n      }\n\n      if (req.url.match(/%3Fctx=[\\d\\w\\W]+\\.map\\?/)) {\n        debug('Skipping sourcemap request for context module %s', req.url);\n        // Return a noop response for the sourcemap\n        res.writeHead(200, {\n          'Content-Type': 'application/json',\n        });\n        res.end('{}');\n        return;\n      }\n\n      next();\n    };\n  }\n}\n"], "names": ["debug", "require", "ContextModuleSourceMapsMiddleware", "<PERSON><PERSON><PERSON><PERSON>", "req", "res", "next", "url", "method", "match", "writeHead", "end"], "mappings": "AAAA;;;;AAEA,MAAMA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,oDAAoD,CACrD,AAAsB,AAAC;AAOjB,MAAMC,iCAAiC;IAC5CC,UAAU,GAAG;QACX,OAAO,CAACC,GAAkB,EAAEC,GAAmB,EAAEC,IAAS,GAAK;YAC7D,IAAI,CAACF,CAAAA,GAAG,QAAK,GAARA,KAAAA,CAAQ,GAARA,GAAG,CAAEG,GAAG,CAAA,IAAKH,GAAG,CAACI,MAAM,KAAK,KAAK,IAAIJ,GAAG,CAACI,MAAM,KAAK,MAAM,AAAC,EAAE;gBAChE,OAAOF,IAAI,EAAE,CAAC;aACf;YAED,IAAIF,GAAG,CAACG,GAAG,CAACE,KAAK,2BAA2B,EAAE;gBAC5CT,KAAK,CAAC,kDAAkD,EAAEI,GAAG,CAACG,GAAG,CAAC,CAAC;gBACnE,2CAA2C;gBAC3CF,GAAG,CAACK,SAAS,CAAC,GAAG,EAAE;oBACjB,cAAc,EAAE,kBAAkB;iBACnC,CAAC,CAAC;gBACHL,GAAG,CAACM,GAAG,CAAC,IAAI,CAAC,CAAC;gBACd,OAAO;aACR;YAEDL,IAAI,EAAE,CAAC;SACR,CAAC;KACH;CACF;QApBYJ,iCAAiC,GAAjCA,iCAAiC"}