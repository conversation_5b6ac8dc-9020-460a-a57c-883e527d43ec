{"version": 3, "sources": ["../../../src/utils/profile.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { env } from './env';\nimport * as Log from '../log';\n\n/**\n * Wrap a method and profile the time it takes to execute the method using `EXPO_PROFILE`.\n * Works best with named functions (i.e. not arrow functions).\n *\n * @param fn function to profile.\n * @param functionName optional name of the function to display in the profile output.\n */\nexport function profile<IArgs extends any[], T extends (...args: IArgs) => any>(\n  fn: T,\n  functionName: string = fn.name\n): T {\n  if (!env.EXPO_PROFILE) {\n    return fn;\n  }\n\n  const name = chalk.dim(`⏱  [profile] ${functionName ?? 'unknown'}`);\n\n  return ((...args: IArgs) => {\n    // Start the timer.\n    Log.time(name);\n\n    // Invoke the method.\n    const results = fn(...args);\n\n    // If non-promise then return as-is.\n    if (!(results instanceof Promise)) {\n      Log.timeEnd(name);\n      return results;\n    }\n\n    // Otherwise await to profile after the promise resolves.\n    return new Promise<Awaited<ReturnType<T>>>((resolve, reject) => {\n      results.then(\n        (results) => {\n          resolve(results);\n          Log.timeEnd(name);\n        },\n        (reason) => {\n          reject(reason);\n          Log.timeEnd(name);\n        }\n      );\n    });\n  }) as T;\n}\n"], "names": ["profile", "Log", "fn", "functionName", "name", "env", "EXPO_PROFILE", "chalk", "dim", "args", "time", "results", "Promise", "timeEnd", "resolve", "reject", "then", "reason"], "mappings": "AAAA;;;;QAYgBA,OAAO,GAAPA,OAAO;AAZL,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEL,IAAA,IAAO,WAAP,OAAO,CAAA;AACfC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;;;;;;;;;;;;;;;;;;;;;;;;;;;AASR,SAASD,OAAO,CACrBE,EAAK,EACLC,YAAoB,GAAGD,EAAE,CAACE,IAAI,EAC3B;IACH,IAAI,CAACC,IAAG,IAAA,CAACC,YAAY,EAAE;QACrB,OAAOJ,EAAE,CAAC;KACX;IAED,MAAME,IAAI,GAAGG,MAAK,QAAA,CAACC,GAAG,CAAC,CAAC,eAAa,EAAEL,YAAY,WAAZA,YAAY,GAAI,SAAS,CAAC,CAAC,CAAC,AAAC;IAEpE,OAAQ,CAAC,GAAGM,IAAI,AAAO,GAAK;QAC1B,mBAAmB;QACnBR,GAAG,CAACS,IAAI,CAACN,IAAI,CAAC,CAAC;QAEf,qBAAqB;QACrB,MAAMO,QAAO,GAAGT,EAAE,IAAIO,IAAI,CAAC,AAAC;QAE5B,oCAAoC;QACpC,IAAI,CAAC,CAACE,QAAO,YAAYC,OAAO,CAAC,EAAE;YACjCX,GAAG,CAACY,OAAO,CAACT,IAAI,CAAC,CAAC;YAClB,OAAOO,QAAO,CAAC;SAChB;QAED,yDAAyD;QACzD,OAAO,IAAIC,OAAO,CAAyB,CAACE,OAAO,EAAEC,MAAM,GAAK;YAC9DJ,QAAO,CAACK,IAAI,CACV,CAACL,OAAO,GAAK;gBACXG,OAAO,CAACH,OAAO,CAAC,CAAC;gBACjBV,GAAG,CAACY,OAAO,CAACT,IAAI,CAAC,CAAC;aACnB,EACD,CAACa,MAAM,GAAK;gBACVF,MAAM,CAACE,MAAM,CAAC,CAAC;gBACfhB,GAAG,CAACY,OAAO,CAACT,IAAI,CAAC,CAAC;aACnB,CACF,CAAC;SACH,CAAC,CAAC;KACJ,CAAO;CACT"}