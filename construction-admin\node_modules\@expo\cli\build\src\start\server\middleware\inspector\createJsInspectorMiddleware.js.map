{"version": 3, "sources": ["../../../../../../src/start/server/middleware/inspector/createJsInspectorMiddleware.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport type { NextHandleFunction } from 'connect';\nimport type { IncomingMessage, ServerResponse } from 'http';\nimport net from 'net';\nimport { TLSSocket } from 'tls';\nimport { URL } from 'url';\n\nimport { openJsInspector, queryInspectorAppAsync } from './JsInspector';\n\n/**\n * Create a middleware that handles new requests to open the debugger from the dev menu.\n * @todo(cedric): delete this middleware once we fully swap over to the new React Native JS Inspector.\n */\nexport function createJsInspectorMiddleware(): NextHandleFunction {\n  return async function (req: IncomingMessage, res: ServerResponse, next: (err?: Error) => void) {\n    const { origin, searchParams } = new URL(req.url ?? '/', getServerBase(req));\n    const appId = searchParams.get('appId') || searchParams.get('applicationId');\n    if (!appId) {\n      res.writeHead(400).end('Missing application identifier (\"?appId=...\")');\n      return;\n    }\n\n    const app = await queryInspectorAppAsync(origin, appId);\n    if (!app) {\n      res.writeHead(404).end('Unable to find inspector target from @react-native/dev-middleware');\n      console.warn(\n        chalk.yellow(\n          'No compatible apps connected. JavaScript Debugging can only be used with the Hermes engine.'\n        )\n      );\n      return;\n    }\n\n    if (req.method === 'GET') {\n      const data = JSON.stringify(app);\n      res.writeHead(200, {\n        'Content-Type': 'application/json; charset=UTF-8',\n        'Cache-Control': 'no-cache',\n        'Content-Length': data.length.toString(),\n      });\n      res.end(data);\n    } else if (req.method === 'POST' || req.method === 'PUT') {\n      try {\n        await openJsInspector(origin, app);\n      } catch (error: any) {\n        // abort(Error: Command failed: osascript -e POSIX path of (path to application \"google chrome\")\n        // 15:50: execution error: Google Chrome got an error: Application isn’t running. (-600)\n\n        console.error(\n          chalk.red('Error launching JS inspector: ' + (error?.message ?? 'Unknown error occurred'))\n        );\n        res.writeHead(500);\n        res.end();\n        return;\n      }\n      res.end();\n    } else {\n      res.writeHead(405);\n    }\n  };\n}\n\nfunction getServerBase(req: IncomingMessage): string {\n  const scheme =\n    req.socket instanceof TLSSocket && req.socket.encrypted === true ? 'https' : 'http';\n  const { localAddress, localPort } = req.socket;\n  const address = localAddress && net.isIPv6(localAddress) ? `[${localAddress}]` : localAddress;\n  return `${scheme}:${address}:${localPort}`;\n}\n"], "names": ["createJsInspectorMiddleware", "req", "res", "next", "origin", "searchParams", "URL", "url", "getServerBase", "appId", "get", "writeHead", "end", "app", "queryInspectorAppAsync", "console", "warn", "chalk", "yellow", "method", "data", "JSON", "stringify", "length", "toString", "openJsInspector", "error", "red", "message", "scheme", "socket", "TLSSocket", "encrypted", "localAddress", "localPort", "address", "net", "isIPv6"], "mappings": "AAAA;;;;QAagBA,2BAA2B,GAA3BA,2BAA2B;AAbzB,IAAA,MAAO,kCAAP,OAAO,EAAA;AAGT,IAAA,IAAK,kCAAL,KAAK,EAAA;AACK,IAAA,IAAK,WAAL,KAAK,CAAA;AACX,IAAA,IAAK,WAAL,KAAK,CAAA;AAE+B,IAAA,YAAe,WAAf,eAAe,CAAA;;;;;;AAMhE,SAASA,2BAA2B,GAAuB;IAChE,OAAO,eAAgBC,GAAoB,EAAEC,GAAmB,EAAEC,IAA2B,EAAE;YACpDF,KAAO;QAAhD,MAAM,EAAEG,MAAM,CAAA,EAAEC,YAAY,CAAA,EAAE,GAAG,IAAIC,IAAG,IAAA,CAACL,CAAAA,KAAO,GAAPA,GAAG,CAACM,GAAG,YAAPN,KAAO,GAAI,GAAG,EAAEO,aAAa,CAACP,GAAG,CAAC,CAAC,AAAC;QAC7E,MAAMQ,KAAK,GAAGJ,YAAY,CAACK,GAAG,CAAC,OAAO,CAAC,IAAIL,YAAY,CAACK,GAAG,CAAC,eAAe,CAAC,AAAC;QAC7E,IAAI,CAACD,KAAK,EAAE;YACVP,GAAG,CAACS,SAAS,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,+CAA+C,CAAC,CAAC;YACxE,OAAO;SACR;QAED,MAAMC,GAAG,GAAG,MAAMC,CAAAA,GAAAA,YAAsB,AAAe,CAAA,uBAAf,CAACV,MAAM,EAAEK,KAAK,CAAC,AAAC;QACxD,IAAI,CAACI,GAAG,EAAE;YACRX,GAAG,CAACS,SAAS,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,mEAAmE,CAAC,CAAC;YAC5FG,OAAO,CAACC,IAAI,CACVC,MAAK,QAAA,CAACC,MAAM,CACV,6FAA6F,CAC9F,CACF,CAAC;YACF,OAAO;SACR;QAED,IAAIjB,GAAG,CAACkB,MAAM,KAAK,KAAK,EAAE;YACxB,MAAMC,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACT,GAAG,CAAC,AAAC;YACjCX,GAAG,CAACS,SAAS,CAAC,GAAG,EAAE;gBACjB,cAAc,EAAE,iCAAiC;gBACjD,eAAe,EAAE,UAAU;gBAC3B,gBAAgB,EAAES,IAAI,CAACG,MAAM,CAACC,QAAQ,EAAE;aACzC,CAAC,CAAC;YACHtB,GAAG,CAACU,GAAG,CAACQ,IAAI,CAAC,CAAC;SACf,MAAM,IAAInB,GAAG,CAACkB,MAAM,KAAK,MAAM,IAAIlB,GAAG,CAACkB,MAAM,KAAK,KAAK,EAAE;YACxD,IAAI;gBACF,MAAMM,CAAAA,GAAAA,YAAe,AAAa,CAAA,gBAAb,CAACrB,MAAM,EAAES,GAAG,CAAC,CAAC;aACpC,CAAC,OAAOa,KAAK,EAAO;oBAK6BA,GAAc;gBAJ9D,gGAAgG;gBAChG,0FAAwF;gBAExFX,OAAO,CAACW,KAAK,CACXT,MAAK,QAAA,CAACU,GAAG,CAAC,gCAAgC,GAAG,CAACD,CAAAA,GAAc,GAAdA,KAAK,QAAS,GAAdA,KAAAA,CAAc,GAAdA,KAAK,CAAEE,OAAO,YAAdF,GAAc,GAAI,wBAAwB,CAAC,CAAC,CAC3F,CAAC;gBACFxB,GAAG,CAACS,SAAS,CAAC,GAAG,CAAC,CAAC;gBACnBT,GAAG,CAACU,GAAG,EAAE,CAAC;gBACV,OAAO;aACR;YACDV,GAAG,CAACU,GAAG,EAAE,CAAC;SACX,MAAM;YACLV,GAAG,CAACS,SAAS,CAAC,GAAG,CAAC,CAAC;SACpB;KACF,CAAC;CACH;AAED,SAASH,aAAa,CAACP,GAAoB,EAAU;IACnD,MAAM4B,MAAM,GACV5B,GAAG,CAAC6B,MAAM,YAAYC,IAAS,UAAA,IAAI9B,GAAG,CAAC6B,MAAM,CAACE,SAAS,KAAK,IAAI,GAAG,OAAO,GAAG,MAAM,AAAC;IACtF,MAAM,EAAEC,YAAY,CAAA,EAAEC,SAAS,CAAA,EAAE,GAAGjC,GAAG,CAAC6B,MAAM,AAAC;IAC/C,MAAMK,OAAO,GAAGF,YAAY,IAAIG,IAAG,QAAA,CAACC,MAAM,CAACJ,YAAY,CAAC,GAAG,CAAC,CAAC,EAAEA,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,AAAC;IAC9F,OAAO,CAAC,EAAEJ,MAAM,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,EAAED,SAAS,CAAC,CAAC,CAAC;CAC5C"}