{"version": 3, "sources": ["../../../../../src/start/server/webpack/compile.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport { promisify } from 'util';\nimport type webpack from 'webpack';\n\nimport { formatWebpackMessages } from './formatWebpackMessages';\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\n\n/** Run the `webpack` compiler and format errors/warnings. */\nexport async function compileAsync(compiler: webpack.Compiler) {\n  const stats = await promisify(compiler.run.bind(compiler))();\n  const { errors, warnings } = formatWebpackMessages(\n    stats.toJson({ all: false, warnings: true, errors: true })\n  );\n  if (errors?.length) {\n    // Only keep the first error. Others are often indicative\n    // of the same problem, but confuse the reader with noise.\n    if (errors.length > 1) {\n      errors.length = 1;\n    }\n    throw new CommandError('WEBPACK_BUNDLE', errors.join('\\n\\n'));\n  }\n  if (warnings?.length) {\n    Log.warn(chalk.yellow('Compiled with warnings\\n'));\n    Log.warn(warnings.join('\\n\\n'));\n  } else {\n    Log.log(chalk.green('Compiled successfully'));\n  }\n\n  return { errors, warnings };\n}\n"], "names": ["compileAsync", "Log", "compiler", "stats", "promisify", "run", "bind", "errors", "warnings", "formatWebpackMessages", "to<PERSON><PERSON>", "all", "length", "CommandError", "join", "warn", "chalk", "yellow", "log", "green"], "mappings": "AAAA;;;;QASsBA,YAAY,GAAZA,YAAY;AAThB,IAAA,MAAO,kCAAP,OAAO,EAAA;AACC,IAAA,KAAM,WAAN,MAAM,CAAA;AAGM,IAAA,sBAAyB,WAAzB,yBAAyB,CAAA;AACnDC,IAAAA,GAAG,mCAAM,cAAc,EAApB;AACc,IAAA,OAAuB,WAAvB,uBAAuB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG7C,eAAeD,YAAY,CAACE,QAA0B,EAAE;IAC7D,MAAMC,KAAK,GAAG,MAAMC,CAAAA,GAAAA,KAAS,AAA6B,CAAA,UAA7B,CAACF,QAAQ,CAACG,GAAG,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAAC,EAAE,AAAC;IAC7D,MAAM,EAAEK,MAAM,CAAA,EAAEC,QAAQ,CAAA,EAAE,GAAGC,CAAAA,GAAAA,sBAAqB,AAEjD,CAAA,sBAFiD,CAChDN,KAAK,CAACO,MAAM,CAAC;QAAEC,GAAG,EAAE,KAAK;QAAEH,QAAQ,EAAE,IAAI;QAAED,MAAM,EAAE,IAAI;KAAE,CAAC,CAC3D,AAAC;IACF,IAAIA,MAAM,QAAQ,GAAdA,KAAAA,CAAc,GAAdA,MAAM,CAAEK,MAAM,EAAE;QAClB,yDAAyD;QACzD,0DAA0D;QAC1D,IAAIL,MAAM,CAACK,MAAM,GAAG,CAAC,EAAE;YACrBL,MAAM,CAACK,MAAM,GAAG,CAAC,CAAC;SACnB;QACD,MAAM,IAAIC,OAAY,aAAA,CAAC,gBAAgB,EAAEN,MAAM,CAACO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;KAC/D;IACD,IAAIN,QAAQ,QAAQ,GAAhBA,KAAAA,CAAgB,GAAhBA,QAAQ,CAAEI,MAAM,EAAE;QACpBX,GAAG,CAACc,IAAI,CAACC,MAAK,QAAA,CAACC,MAAM,CAAC,0BAA0B,CAAC,CAAC,CAAC;QACnDhB,GAAG,CAACc,IAAI,CAACP,QAAQ,CAACM,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;KACjC,MAAM;QACLb,GAAG,CAACiB,GAAG,CAACF,MAAK,QAAA,CAACG,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC;KAC/C;IAED,OAAO;QAAEZ,MAAM;QAAEC,QAAQ;KAAE,CAAC;CAC7B"}