{"version": 3, "sources": ["../../../src/utils/getOrPromptApplicationId.ts"], "sourcesContent": ["import { ExpoConfig, getAccountUsername, getConfig } from '@expo/config';\nimport chalk from 'chalk';\n\nimport { learnMore } from './link';\nimport { attemptModification } from './modifyConfigAsync';\nimport prompt, { confirmAsync } from './prompts';\nimport {\n  assertValidBundleId,\n  assertValidPackage,\n  getBundleIdWarningAsync,\n  getPackageNameWarningAsync,\n  validateBundleId,\n  validatePackage,\n  validatePackageWithWarning,\n} from './validateApplicationId';\nimport * as Log from '../log';\n\nfunction getUsernameAsync(exp: ExpoConfig) {\n  // TODO: Use XDL's UserManager\n  // import { UserManager } from 'xdl';\n  return getAccountUsername(exp);\n}\n\nconst NO_BUNDLE_ID_MESSAGE = `Project must have a \\`ios.bundleIdentifier\\` set in the Expo config (app.json or app.config.js).`;\n\nconst NO_PACKAGE_MESSAGE = `Project must have a \\`android.package\\` set in the Expo config (app.json or app.config.js).`;\n\n/**\n * Get the bundle identifier from the Expo config or prompt the user to choose a new bundle identifier.\n * Prompted value will be validated against the App Store and a local regex.\n * If the project Expo config is a static JSON file, the bundle identifier will be updated in the config automatically.\n */\nexport async function getOrPromptForBundleIdentifier(\n  projectRoot: string,\n  exp: ExpoConfig = getConfig(projectRoot).exp\n): Promise<string> {\n  const current = exp.ios?.bundleIdentifier;\n  if (current) {\n    assertValidBundleId(current);\n    return current;\n  }\n\n  Log.log(\n    chalk`\\n{bold 📝  iOS Bundle Identifier} {dim ${learnMore(\n      'https://expo.fyi/bundle-identifier'\n    )}}\\n`\n  );\n\n  return await promptForBundleIdAsync(projectRoot, exp);\n}\n\nasync function promptForBundleIdAsync(projectRoot: string, exp: ExpoConfig): Promise<string> {\n  // Prompt the user for the bundle ID.\n  // Even if the project is using a dynamic config we can still\n  // prompt a better error message, recommend a default value, and help the user\n  // validate their custom bundle ID upfront.\n  const { bundleIdentifier } = await prompt(\n    {\n      type: 'text',\n      name: 'bundleIdentifier',\n      initial: (await getRecommendedBundleIdAsync(exp)) ?? undefined,\n      // The Apple helps people know this isn't an EAS feature.\n      message: `What would you like your iOS bundle identifier to be?`,\n      validate: validateBundleId,\n    },\n    {\n      nonInteractiveHelp: NO_BUNDLE_ID_MESSAGE,\n    }\n  );\n\n  // Warn the user if the bundle ID is already in use.\n  const warning = await getBundleIdWarningAsync(bundleIdentifier);\n  if (warning && !(await warnAndConfirmAsync(warning))) {\n    // Cycle the Bundle ID prompt to try again.\n    return await promptForBundleIdAsync(projectRoot, exp);\n  }\n\n  // Apply the changes to the config.\n  await attemptModification(\n    projectRoot,\n    {\n      ios: { ...(exp.ios || {}), bundleIdentifier },\n    },\n    { ios: { bundleIdentifier } }\n  );\n\n  return bundleIdentifier;\n}\n\nasync function warnAndConfirmAsync(warning: string): Promise<boolean> {\n  Log.log();\n  Log.warn(warning);\n  Log.log();\n  if (\n    !(await confirmAsync({\n      message: `Continue?`,\n      initial: true,\n    }))\n  ) {\n    return false;\n  }\n  return true;\n}\n\n// Recommend a bundle identifier based on the username and project slug.\nasync function getRecommendedBundleIdAsync(exp: ExpoConfig): Promise<string | null> {\n  // Attempt to use the android package name first since it's convenient to have them aligned.\n  if (exp.android?.package && validateBundleId(exp.android?.package)) {\n    return exp.android?.package;\n  } else {\n    const username = await getUsernameAsync(exp);\n    const possibleId = `com.${username}.${exp.slug}`;\n    if (username && validateBundleId(possibleId)) {\n      return possibleId;\n    }\n  }\n\n  return null;\n}\n\n// Recommend a package name based on the username and project slug.\nasync function getRecommendedPackageNameAsync(exp: ExpoConfig): Promise<string | null> {\n  // Attempt to use the ios bundle id first since it's convenient to have them aligned.\n  if (exp.ios?.bundleIdentifier && validatePackage(exp.ios.bundleIdentifier)) {\n    return exp.ios.bundleIdentifier;\n  } else {\n    const username = await getUsernameAsync(exp);\n    // It's common to use dashes in your node project name, strip them from the suggested package name.\n    const possibleId = `com.${username}.${exp.slug}`.split('-').join('');\n    if (username && validatePackage(possibleId)) {\n      return possibleId;\n    }\n  }\n  return null;\n}\n\n/**\n * Get the package name from the Expo config or prompt the user to choose a new package name.\n * Prompted value will be validated against the Play Store and a local regex.\n * If the project Expo config is a static JSON file, the package name will be updated in the config automatically.\n */\nexport async function getOrPromptForPackage(\n  projectRoot: string,\n  exp: ExpoConfig = getConfig(projectRoot).exp\n): Promise<string> {\n  const current = exp.android?.package;\n  if (current) {\n    assertValidPackage(current);\n    return current;\n  }\n\n  Log.log(\n    chalk`\\n{bold 📝  Android package} {dim ${learnMore('https://expo.fyi/android-package')}}\\n`\n  );\n\n  return await promptForPackageAsync(projectRoot, exp);\n}\n\nasync function promptForPackageAsync(projectRoot: string, exp: ExpoConfig): Promise<string> {\n  // Prompt the user for the android package.\n  // Even if the project is using a dynamic config we can still\n  // prompt a better error message, recommend a default value, and help the user\n  // validate their custom android package upfront.\n  const { packageName } = await prompt(\n    {\n      type: 'text',\n      name: 'packageName',\n      initial: (await getRecommendedPackageNameAsync(exp)) ?? undefined,\n      message: `What would you like your Android package name to be?`,\n      validate: validatePackageWithWarning,\n    },\n    {\n      nonInteractiveHelp: NO_PACKAGE_MESSAGE,\n    }\n  );\n\n  // Warn the user if the package name is already in use.\n  const warning = await getPackageNameWarningAsync(packageName);\n  if (warning && !(await warnAndConfirmAsync(warning))) {\n    // Cycle the Package name prompt to try again.\n    return await promptForPackageAsync(projectRoot, exp);\n  }\n\n  // Apply the changes to the config.\n  await attemptModification(\n    projectRoot,\n    {\n      android: { ...(exp.android || {}), package: packageName },\n    },\n    {\n      android: { package: packageName },\n    }\n  );\n\n  return packageName;\n}\n"], "names": ["getOrPromptForBundleIdentifier", "getOrPromptForPackage", "Log", "getUsernameAsync", "exp", "getAccountUsername", "NO_BUNDLE_ID_MESSAGE", "NO_PACKAGE_MESSAGE", "projectRoot", "getConfig", "current", "ios", "bundleIdentifier", "assertValidBundleId", "log", "chalk", "learnMore", "promptForBundleIdAsync", "prompt", "type", "name", "initial", "getRecommendedBundleIdAsync", "undefined", "message", "validate", "validateBundleId", "nonInteractiveHelp", "warning", "getBundleIdWarningAsync", "warnAndConfirmAsync", "attemptModification", "warn", "<PERSON><PERSON><PERSON>", "android", "package", "username", "possibleId", "slug", "getRecommendedPackageNameAsync", "validatePackage", "split", "join", "assertValidPackage", "promptForPackageAsync", "packageName", "validatePackageWithWarning", "getPackageNameWarningAsync"], "mappings": "AAAA;;;;QAgCsBA,8BAA8B,GAA9BA,8BAA8B;QA6G9BC,qBAAqB,GAArBA,qBAAqB;AA7Ie,IAAA,OAAc,WAAd,cAAc,CAAA;AACtD,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEC,IAAA,KAAQ,WAAR,QAAQ,CAAA;AACE,IAAA,kBAAqB,WAArB,qBAAqB,CAAA;AACpB,IAAA,QAAW,mCAAX,WAAW,EAAA;AASzC,IAAA,sBAAyB,WAAzB,yBAAyB,CAAA;AACpBC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEf,SAASC,gBAAgB,CAACC,GAAe,EAAE;IACzC,8BAA8B;IAC9B,qCAAqC;IACrC,OAAOC,CAAAA,GAAAA,OAAkB,AAAK,CAAA,mBAAL,CAACD,GAAG,CAAC,CAAC;CAChC;AAED,MAAME,oBAAoB,GAAG,CAAC,gGAAgG,CAAC,AAAC;AAEhI,MAAMC,kBAAkB,GAAG,CAAC,2FAA2F,CAAC,AAAC;AAOlH,eAAeP,8BAA8B,CAClDQ,WAAmB,EACnBJ,GAAe,GAAGK,CAAAA,GAAAA,OAAS,AAAa,CAAA,UAAb,CAACD,WAAW,CAAC,CAACJ,GAAG,EAC3B;QACDA,GAAO;IAAvB,MAAMM,OAAO,GAAGN,CAAAA,GAAO,GAAPA,GAAG,CAACO,GAAG,SAAkB,GAAzBP,KAAAA,CAAyB,GAAzBA,GAAO,CAAEQ,gBAAgB,AAAC;IAC1C,IAAIF,OAAO,EAAE;QACXG,CAAAA,GAAAA,sBAAmB,AAAS,CAAA,oBAAT,CAACH,OAAO,CAAC,CAAC;QAC7B,OAAOA,OAAO,CAAC;KAChB;IAEDR,GAAG,CAACY,GAAG,CACLC,MAAK,QAAA,CAAC,0CAA0C,EAAEC,CAAAA,GAAAA,KAAS,AAE1D,CAAA,UAF0D,CACzD,oCAAoC,CACrC,CAAC,GAAG,CAAC,CACP,CAAC;IAEF,OAAO,MAAMC,sBAAsB,CAACT,WAAW,EAAEJ,GAAG,CAAC,CAAC;CACvD;AAED,eAAea,sBAAsB,CAACT,WAAmB,EAAEJ,GAAe,EAAmB;QAS9E,GAAwC;IARrD,qCAAqC;IACrC,6DAA6D;IAC7D,8EAA8E;IAC9E,2CAA2C;IAC3C,MAAM,EAAEQ,gBAAgB,CAAA,EAAE,GAAG,MAAMM,CAAAA,GAAAA,QAAM,AAYxC,CAAA,QAZwC,CACvC;QACEC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,kBAAkB;QACxBC,OAAO,EAAE,CAAA,GAAwC,GAAvC,MAAMC,2BAA2B,CAAClB,GAAG,CAAC,YAAvC,GAAwC,GAAImB,SAAS;QAC9D,yDAAyD;QACzDC,OAAO,EAAE,CAAC,qDAAqD,CAAC;QAChEC,QAAQ,EAAEC,sBAAgB,iBAAA;KAC3B,EACD;QACEC,kBAAkB,EAAErB,oBAAoB;KACzC,CACF,AAAC;IAEF,oDAAoD;IACpD,MAAMsB,OAAO,GAAG,MAAMC,CAAAA,GAAAA,sBAAuB,AAAkB,CAAA,wBAAlB,CAACjB,gBAAgB,CAAC,AAAC;IAChE,IAAIgB,OAAO,IAAI,CAAE,MAAME,mBAAmB,CAACF,OAAO,CAAC,AAAC,EAAE;QACpD,2CAA2C;QAC3C,OAAO,MAAMX,sBAAsB,CAACT,WAAW,EAAEJ,GAAG,CAAC,CAAC;KACvD;IAED,mCAAmC;IACnC,MAAM2B,CAAAA,GAAAA,kBAAmB,AAMxB,CAAA,oBANwB,CACvBvB,WAAW,EACX;QACEG,GAAG,EAAE;YAAE,GAAIP,GAAG,CAACO,GAAG,IAAI,EAAE;YAAGC,gBAAgB;SAAE;KAC9C,EACD;QAAED,GAAG,EAAE;YAAEC,gBAAgB;SAAE;KAAE,CAC9B,CAAC;IAEF,OAAOA,gBAAgB,CAAC;CACzB;AAED,eAAekB,mBAAmB,CAACF,OAAe,EAAoB;IACpE1B,GAAG,CAACY,GAAG,EAAE,CAAC;IACVZ,GAAG,CAAC8B,IAAI,CAACJ,OAAO,CAAC,CAAC;IAClB1B,GAAG,CAACY,GAAG,EAAE,CAAC;IACV,IACE,CAAE,MAAMmB,CAAAA,GAAAA,QAAY,AAGlB,CAAA,aAHkB,CAAC;QACnBT,OAAO,EAAE,CAAC,SAAS,CAAC;QACpBH,OAAO,EAAE,IAAI;KACd,CAAC,AAAC,EACH;QACA,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;CACb;AAED,wEAAwE;AACxE,eAAeC,2BAA2B,CAAClB,GAAe,EAA0B;QAE9EA,GAAW,EAA8BA,IAAW;IADxD,4FAA4F;IAC5F,IAAIA,CAAAA,CAAAA,GAAW,GAAXA,GAAG,CAAC8B,OAAO,SAAS,GAApB9B,KAAAA,CAAoB,GAApBA,GAAW,CAAE+B,OAAO,CAAA,IAAIT,CAAAA,GAAAA,sBAAgB,AAAsB,CAAA,iBAAtB,CAACtB,CAAAA,IAAW,GAAXA,GAAG,CAAC8B,OAAO,SAAS,GAApB9B,KAAAA,CAAoB,GAApBA,IAAW,CAAE+B,OAAO,CAAC,EAAE;YAC3D/B,IAAW;QAAlB,OAAOA,CAAAA,IAAW,GAAXA,GAAG,CAAC8B,OAAO,SAAS,GAApB9B,KAAAA,CAAoB,GAApBA,IAAW,CAAE+B,OAAO,CAAC;KAC7B,MAAM;QACL,MAAMC,QAAQ,GAAG,MAAMjC,gBAAgB,CAACC,GAAG,CAAC,AAAC;QAC7C,MAAMiC,UAAU,GAAG,CAAC,IAAI,EAAED,QAAQ,CAAC,CAAC,EAAEhC,GAAG,CAACkC,IAAI,CAAC,CAAC,AAAC;QACjD,IAAIF,QAAQ,IAAIV,CAAAA,GAAAA,sBAAgB,AAAY,CAAA,iBAAZ,CAACW,UAAU,CAAC,EAAE;YAC5C,OAAOA,UAAU,CAAC;SACnB;KACF;IAED,OAAO,IAAI,CAAC;CACb;AAED,mEAAmE;AACnE,eAAeE,8BAA8B,CAACnC,GAAe,EAA0B;QAEjFA,GAAO;IADX,qFAAqF;IACrF,IAAIA,CAAAA,CAAAA,GAAO,GAAPA,GAAG,CAACO,GAAG,SAAkB,GAAzBP,KAAAA,CAAyB,GAAzBA,GAAO,CAAEQ,gBAAgB,CAAA,IAAI4B,CAAAA,GAAAA,sBAAe,AAA0B,CAAA,gBAA1B,CAACpC,GAAG,CAACO,GAAG,CAACC,gBAAgB,CAAC,EAAE;QAC1E,OAAOR,GAAG,CAACO,GAAG,CAACC,gBAAgB,CAAC;KACjC,MAAM;QACL,MAAMwB,QAAQ,GAAG,MAAMjC,gBAAgB,CAACC,GAAG,CAAC,AAAC;QAC7C,mGAAmG;QACnG,MAAMiC,UAAU,GAAG,CAAC,IAAI,EAAED,QAAQ,CAAC,CAAC,EAAEhC,GAAG,CAACkC,IAAI,CAAC,CAAC,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,AAAC;QACrE,IAAIN,QAAQ,IAAII,CAAAA,GAAAA,sBAAe,AAAY,CAAA,gBAAZ,CAACH,UAAU,CAAC,EAAE;YAC3C,OAAOA,UAAU,CAAC;SACnB;KACF;IACD,OAAO,IAAI,CAAC;CACb;AAOM,eAAepC,qBAAqB,CACzCO,WAAmB,EACnBJ,GAAe,GAAGK,CAAAA,GAAAA,OAAS,AAAa,CAAA,UAAb,CAACD,WAAW,CAAC,CAACJ,GAAG,EAC3B;QACDA,GAAW;IAA3B,MAAMM,OAAO,GAAGN,CAAAA,GAAW,GAAXA,GAAG,CAAC8B,OAAO,SAAS,GAApB9B,KAAAA,CAAoB,GAApBA,GAAW,CAAE+B,OAAO,AAAC;IACrC,IAAIzB,OAAO,EAAE;QACXiC,CAAAA,GAAAA,sBAAkB,AAAS,CAAA,mBAAT,CAACjC,OAAO,CAAC,CAAC;QAC5B,OAAOA,OAAO,CAAC;KAChB;IAEDR,GAAG,CAACY,GAAG,CACLC,MAAK,QAAA,CAAC,oCAAiC,EAAEC,CAAAA,GAAAA,KAAS,AAAoC,CAAA,UAApC,CAAC,kCAAkC,CAAC,CAAC,GAAG,CAAC,CAC5F,CAAC;IAEF,OAAO,MAAM4B,qBAAqB,CAACpC,WAAW,EAAEJ,GAAG,CAAC,CAAC;CACtD;AAED,eAAewC,qBAAqB,CAACpC,WAAmB,EAAEJ,GAAe,EAAmB;QAS7E,GAA2C;IARxD,2CAA2C;IAC3C,6DAA6D;IAC7D,8EAA8E;IAC9E,iDAAiD;IACjD,MAAM,EAAEyC,WAAW,CAAA,EAAE,GAAG,MAAM3B,CAAAA,GAAAA,QAAM,AAWnC,CAAA,QAXmC,CAClC;QACEC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,aAAa;QACnBC,OAAO,EAAE,CAAA,GAA2C,GAA1C,MAAMkB,8BAA8B,CAACnC,GAAG,CAAC,YAA1C,GAA2C,GAAImB,SAAS;QACjEC,OAAO,EAAE,CAAC,oDAAoD,CAAC;QAC/DC,QAAQ,EAAEqB,sBAA0B,2BAAA;KACrC,EACD;QACEnB,kBAAkB,EAAEpB,kBAAkB;KACvC,CACF,AAAC;IAEF,uDAAuD;IACvD,MAAMqB,OAAO,GAAG,MAAMmB,CAAAA,GAAAA,sBAA0B,AAAa,CAAA,2BAAb,CAACF,WAAW,CAAC,AAAC;IAC9D,IAAIjB,OAAO,IAAI,CAAE,MAAME,mBAAmB,CAACF,OAAO,CAAC,AAAC,EAAE;QACpD,8CAA8C;QAC9C,OAAO,MAAMgB,qBAAqB,CAACpC,WAAW,EAAEJ,GAAG,CAAC,CAAC;KACtD;IAED,mCAAmC;IACnC,MAAM2B,CAAAA,GAAAA,kBAAmB,AAQxB,CAAA,oBARwB,CACvBvB,WAAW,EACX;QACE0B,OAAO,EAAE;YAAE,GAAI9B,GAAG,CAAC8B,OAAO,IAAI,EAAE;YAAGC,OAAO,EAAEU,WAAW;SAAE;KAC1D,EACD;QACEX,OAAO,EAAE;YAAEC,OAAO,EAAEU,WAAW;SAAE;KAClC,CACF,CAAC;IAEF,OAAOA,WAAW,CAAC;CACpB"}