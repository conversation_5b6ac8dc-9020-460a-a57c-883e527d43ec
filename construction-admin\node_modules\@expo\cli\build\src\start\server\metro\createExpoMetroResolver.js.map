{"version": 3, "sources": ["../../../../../src/start/server/metro/createExpoMetroResolver.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport fs from 'fs';\nimport { Resolution, ResolutionContext } from 'metro-resolver';\nimport path from 'path';\n\nimport jestResolver from './createJResolver';\nimport { isNodeExternal } from './externals';\nimport { formatFileCandidates } from './formatFileCandidates';\n\nclass FailedToResolvePathError extends Error {}\n\nclass ShimModuleError extends Error {}\n\nconst realpathFS =\n  process.platform !== 'win32' && fs.realpathSync && typeof fs.realpathSync.native === 'function'\n    ? fs.realpathSync.native\n    : fs.realpathSync;\n\nfunction realpathSync(x: string) {\n  try {\n    return realpathFS(x);\n  } catch (realpathErr: any) {\n    if (realpathErr.code !== 'ENOENT') {\n      throw realpathErr;\n    }\n  }\n  return x;\n}\n\nexport function createFastResolver({\n  preserveSymlinks,\n  blockList,\n}: {\n  preserveSymlinks: boolean;\n  blockList: RegExp[];\n}) {\n  const cachedExtensions: Map<string, readonly string[]> = new Map();\n\n  function getAdjustedExtensions({\n    metroSourceExtensions,\n    platform,\n    isNative,\n  }: {\n    metroSourceExtensions: readonly string[];\n    platform: string | null;\n    isNative: boolean;\n  }): readonly string[] {\n    const key = JSON.stringify({ metroSourceExtensions, platform, isNative });\n    if (cachedExtensions.has(key)) {\n      return cachedExtensions.get(key)!;\n    }\n\n    let output = metroSourceExtensions;\n    if (platform) {\n      const nextOutput: string[] = [];\n\n      output.forEach((ext) => {\n        nextOutput.push(`${platform}.${ext}`);\n        if (isNative) {\n          nextOutput.push(`native.${ext}`);\n        }\n        nextOutput.push(ext);\n      });\n\n      output = nextOutput;\n    }\n\n    output = Array.from(new Set<string>(output));\n\n    // resolve expects these to start with a dot.\n    output = output.map((ext) => `.${ext}`);\n\n    cachedExtensions.set(key, output);\n\n    return output;\n  }\n\n  function fastResolve(\n    context: Pick<\n      ResolutionContext,\n      | 'unstable_enablePackageExports'\n      | 'customResolverOptions'\n      | 'sourceExts'\n      | 'preferNativePlatform'\n      | 'originModulePath'\n      | 'getPackage'\n      | 'nodeModulesPaths'\n      | 'mainFields'\n      | 'resolveAsset'\n      | 'unstable_conditionNames'\n      | 'unstable_conditionsByPlatform'\n    >,\n    moduleName: string,\n    platform: string | null\n  ): Resolution {\n    const environment = context.customResolverOptions?.environment;\n    const isServer = environment === 'node';\n\n    const extensions = getAdjustedExtensions({\n      metroSourceExtensions: context.sourceExts,\n      platform,\n      isNative: context.preferNativePlatform,\n    }) as string[];\n\n    let fp: string;\n\n    try {\n      const conditions = context.unstable_enablePackageExports\n        ? [\n            ...new Set([\n              'default',\n              ...context.unstable_conditionNames,\n              ...(platform != null ? context.unstable_conditionsByPlatform[platform] ?? [] : []),\n            ]),\n          ]\n        : [];\n\n      fp = jestResolver(moduleName, {\n        blockList,\n        enablePackageExports: context.unstable_enablePackageExports,\n        basedir: path.dirname(context.originModulePath),\n        paths: context.nodeModulesPaths.length ? (context.nodeModulesPaths as string[]) : undefined,\n        extensions,\n        conditions,\n        realpathSync(file: string): string {\n          // @ts-expect-error: Missing on type.\n          const metroRealPath = context.unstable_getRealPath?.(file);\n          if (metroRealPath == null && preserveSymlinks) {\n            return realpathSync(file);\n          }\n          return metroRealPath ?? file;\n        },\n        packageFilter(pkg) {\n          // set the pkg.main to the first available field in context.mainFields\n          for (const field of context.mainFields) {\n            if (\n              pkg[field] &&\n              // object-inspect uses browser: {} in package.json\n              typeof pkg[field] === 'string'\n            ) {\n              return {\n                ...pkg,\n                main: pkg[field],\n              };\n            }\n          }\n          return pkg;\n        },\n        // Used to ensure files trace to packages instead of node_modules in expo/expo. This is how Metro works and\n        // the app doesn't finish without it.\n        preserveSymlinks,\n        readPackageSync(readFileSync, pkgFile) {\n          return (\n            context.getPackage(pkgFile) ??\n            JSON.parse(\n              // @ts-expect-error\n              readFileSync(pkgfile)\n            )\n          );\n        },\n        includeCoreModules: isServer,\n\n        pathFilter:\n          // Disable `browser` field for server environments.\n          isServer\n            ? undefined\n            : // Enable `browser` field support\n              (pkg: any, _resolvedPath: string, relativePathIn: string): string => {\n                let relativePath = relativePathIn;\n                if (relativePath[0] !== '.') {\n                  relativePath = `./${relativePath}`;\n                }\n\n                const replacements = pkg.browser;\n                if (replacements === undefined) {\n                  return '';\n                }\n\n                // TODO: Probably use a better extension matching system here.\n                // This was added for `uuid/v4` -> `./lib/rng` -> `./lib/rng-browser.js`\n                const mappedPath = replacements[relativePath] ?? replacements[relativePath + '.js'];\n                if (mappedPath === false) {\n                  throw new ShimModuleError();\n                }\n                return mappedPath;\n              },\n      });\n    } catch (error: any) {\n      if (error instanceof ShimModuleError) {\n        return {\n          type: 'empty',\n        };\n      }\n\n      if ('code' in error && error.code === 'MODULE_NOT_FOUND') {\n        if (isNodeExternal(moduleName)) {\n          // In this case, mock the file to use an empty module.\n          return {\n            type: 'empty',\n          };\n        }\n\n        throw new FailedToResolvePathError(\n          'The module could not be resolved because no file or module matched the pattern:\\n' +\n            `  ${formatFileCandidates(\n              {\n                type: 'sourceFile',\n                filePathPrefix: moduleName,\n                candidateExts: extensions,\n              },\n              true\n            )}\\n\\nFrom:\\n  ${context.originModulePath}\\n`\n        );\n      }\n      throw error;\n    }\n\n    if (context.sourceExts.some((ext) => fp.endsWith(ext))) {\n      return {\n        type: 'sourceFile',\n        filePath: fp,\n      };\n    }\n\n    if (isNodeExternal(fp)) {\n      if (isServer) {\n        return {\n          type: 'sourceFile',\n          filePath: fp,\n        };\n      }\n      // NOTE: This shouldn't happen, the module should throw.\n      // Mock non-server built-in modules to empty.\n      return {\n        type: 'empty',\n      };\n    }\n\n    // NOTE: platform extensions may not be supported on assets.\n\n    if (platform === 'web') {\n      // Skip multi-resolution on web/server bundles. Only consideration here is that\n      // we may still need it in case the only image is a multi-resolution image.\n      return {\n        type: 'assetFiles',\n        filePaths: [fp],\n      };\n    }\n\n    const dirPath = path.dirname(fp);\n    const extension = path.extname(fp);\n    const basename = path.basename(fp, extension);\n    return {\n      type: 'assetFiles',\n      // Support multi-resolution asset extensions...\n      filePaths: context.resolveAsset(dirPath, basename, extension) ?? [fp],\n    };\n  }\n\n  return fastResolve;\n}\n"], "names": ["createFastResolver", "FailedToResolvePathError", "Error", "ShimModuleError", "realpathFS", "process", "platform", "fs", "realpathSync", "native", "x", "realpathErr", "code", "preserveSymlinks", "blockList", "cachedExtensions", "Map", "getAdjustedExtensions", "metroSourceExtensions", "isNative", "key", "JSON", "stringify", "has", "get", "output", "nextOutput", "for<PERSON>ach", "ext", "push", "Array", "from", "Set", "map", "set", "fastResolve", "context", "moduleName", "environment", "customResolverOptions", "isServer", "extensions", "sourceExts", "preferNativePlatform", "fp", "conditions", "unstable_enablePackageExports", "unstable_conditionNames", "unstable_conditionsByPlatform", "jestResolver", "enablePackageExports", "basedir", "path", "dirname", "originModulePath", "paths", "nodeModulesPaths", "length", "undefined", "file", "metroRealPath", "unstable_getRealPath", "packageFilter", "pkg", "field", "mainFields", "main", "readPackageSync", "readFileSync", "pkgFile", "getPackage", "parse", "pkgfile", "includeCoreModules", "pathFilter", "_resolvedPath", "relativePathIn", "relativePath", "replacements", "browser", "mappedPath", "error", "type", "isNodeExternal", "formatFileCandidates", "filePathPrefix", "candidate<PERSON><PERSON><PERSON>", "some", "endsWith", "filePath", "filePaths", "<PERSON><PERSON><PERSON>", "extension", "extname", "basename", "resolveAsset"], "mappings": "AAMA;;;;QA4BgBA,kBAAkB,GAAlBA,kBAAkB;AA5BnB,IAAA,GAAI,kCAAJ,IAAI,EAAA;AAEF,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEE,IAAA,gBAAmB,kCAAnB,mBAAmB,EAAA;AACb,IAAA,UAAa,WAAb,aAAa,CAAA;AACP,IAAA,qBAAwB,WAAxB,wBAAwB,CAAA;;;;;;AAE7D,MAAMC,wBAAwB,SAASC,KAAK;CAAG;AAE/C,MAAMC,eAAe,SAASD,KAAK;CAAG;AAEtC,MAAME,UAAU,GACdC,OAAO,CAACC,QAAQ,KAAK,OAAO,IAAIC,GAAE,QAAA,CAACC,YAAY,IAAI,OAAOD,GAAE,QAAA,CAACC,YAAY,CAACC,MAAM,KAAK,UAAU,GAC3FF,GAAE,QAAA,CAACC,YAAY,CAACC,MAAM,GACtBF,GAAE,QAAA,CAACC,YAAY,AAAC;AAEtB,SAASA,YAAY,CAACE,CAAS,EAAE;IAC/B,IAAI;QACF,OAAON,UAAU,CAACM,CAAC,CAAC,CAAC;KACtB,CAAC,OAAOC,WAAW,EAAO;QACzB,IAAIA,WAAW,CAACC,IAAI,KAAK,QAAQ,EAAE;YACjC,MAAMD,WAAW,CAAC;SACnB;KACF;IACD,OAAOD,CAAC,CAAC;CACV;AAEM,SAASV,kBAAkB,CAAC,EACjCa,gBAAgB,CAAA,EAChBC,SAAS,CAAA,EAIV,EAAE;IACD,MAAMC,gBAAgB,GAAmC,IAAIC,GAAG,EAAE,AAAC;IAEnE,SAASC,qBAAqB,CAAC,EAC7BC,qBAAqB,CAAA,EACrBZ,QAAQ,CAAA,EACRa,QAAQ,CAAA,EAKT,EAAqB;QACpB,MAAMC,GAAG,GAAGC,IAAI,CAACC,SAAS,CAAC;YAAEJ,qBAAqB;YAAEZ,QAAQ;YAAEa,QAAQ;SAAE,CAAC,AAAC;QAC1E,IAAIJ,gBAAgB,CAACQ,GAAG,CAACH,GAAG,CAAC,EAAE;YAC7B,OAAOL,gBAAgB,CAACS,GAAG,CAACJ,GAAG,CAAC,CAAE;SACnC;QAED,IAAIK,MAAM,GAAGP,qBAAqB,AAAC;QACnC,IAAIZ,QAAQ,EAAE;YACZ,MAAMoB,UAAU,GAAa,EAAE,AAAC;YAEhCD,MAAM,CAACE,OAAO,CAAC,CAACC,GAAG,GAAK;gBACtBF,UAAU,CAACG,IAAI,CAAC,CAAC,EAAEvB,QAAQ,CAAC,CAAC,EAAEsB,GAAG,CAAC,CAAC,CAAC,CAAC;gBACtC,IAAIT,QAAQ,EAAE;oBACZO,UAAU,CAACG,IAAI,CAAC,CAAC,OAAO,EAAED,GAAG,CAAC,CAAC,CAAC,CAAC;iBAClC;gBACDF,UAAU,CAACG,IAAI,CAACD,GAAG,CAAC,CAAC;aACtB,CAAC,CAAC;YAEHH,MAAM,GAAGC,UAAU,CAAC;SACrB;QAEDD,MAAM,GAAGK,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAASP,MAAM,CAAC,CAAC,CAAC;QAE7C,6CAA6C;QAC7CA,MAAM,GAAGA,MAAM,CAACQ,GAAG,CAAC,CAACL,GAAG,GAAK,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC;QAAA,CAAC,CAAC;QAExCb,gBAAgB,CAACmB,GAAG,CAACd,GAAG,EAAEK,MAAM,CAAC,CAAC;QAElC,OAAOA,MAAM,CAAC;KACf;IAED,SAASU,WAAW,CAClBC,OAaC,EACDC,UAAkB,EAClB/B,QAAuB,EACX;YACQ8B,IAA6B;QAAjD,MAAME,WAAW,GAAGF,CAAAA,IAA6B,GAA7BA,OAAO,CAACG,qBAAqB,SAAa,GAA1CH,KAAAA,CAA0C,GAA1CA,IAA6B,CAAEE,WAAW,AAAC;QAC/D,MAAME,QAAQ,GAAGF,WAAW,KAAK,MAAM,AAAC;QAExC,MAAMG,UAAU,GAAGxB,qBAAqB,CAAC;YACvCC,qBAAqB,EAAEkB,OAAO,CAACM,UAAU;YACzCpC,QAAQ;YACRa,QAAQ,EAAEiB,OAAO,CAACO,oBAAoB;SACvC,CAAC,AAAY,AAAC;QAEf,IAAIC,EAAE,AAAQ,AAAC;QAEf,IAAI;gBAM6BR,SAA+C;YAL9E,MAAMS,UAAU,GAAGT,OAAO,CAACU,6BAA6B,GACpD;mBACK,IAAId,GAAG,CAAC;oBACT,SAAS;uBACNI,OAAO,CAACW,uBAAuB;uBAC9BzC,QAAQ,IAAI,IAAI,GAAG8B,CAAAA,SAA+C,GAA/CA,OAAO,CAACY,6BAA6B,CAAC1C,QAAQ,CAAC,YAA/C8B,SAA+C,GAAI,EAAE,GAAG,EAAE;iBAClF,CAAC;aACH,GACD,EAAE,AAAC;YAEPQ,EAAE,GAAGK,CAAAA,GAAAA,gBAAY,AAqEf,CAAA,QArEe,CAACZ,UAAU,EAAE;gBAC5BvB,SAAS;gBACToC,oBAAoB,EAAEd,OAAO,CAACU,6BAA6B;gBAC3DK,OAAO,EAAEC,KAAI,QAAA,CAACC,OAAO,CAACjB,OAAO,CAACkB,gBAAgB,CAAC;gBAC/CC,KAAK,EAAEnB,OAAO,CAACoB,gBAAgB,CAACC,MAAM,GAAIrB,OAAO,CAACoB,gBAAgB,GAAgBE,SAAS;gBAC3FjB,UAAU;gBACVI,UAAU;gBACVrC,YAAY,EAACmD,IAAY,EAAU;oBACjC,qCAAqC;oBACrC,MAAMC,aAAa,GAAGxB,OAAO,CAACyB,oBAAoB,QAAQ,GAApCzB,KAAAA,CAAoC,GAApCA,OAAO,CAACyB,oBAAoB,CAAGF,IAAI,CAAC,AAAC;oBAC3D,IAAIC,aAAa,IAAI,IAAI,IAAI/C,gBAAgB,EAAE;wBAC7C,OAAOL,YAAY,CAACmD,IAAI,CAAC,CAAC;qBAC3B;oBACD,OAAOC,aAAa,WAAbA,aAAa,GAAID,IAAI,CAAC;iBAC9B;gBACDG,aAAa,EAACC,GAAG,EAAE;oBACjB,sEAAsE;oBACtE,KAAK,MAAMC,KAAK,IAAI5B,OAAO,CAAC6B,UAAU,CAAE;wBACtC,IACEF,GAAG,CAACC,KAAK,CAAC,IACV,kDAAkD;wBAClD,OAAOD,GAAG,CAACC,KAAK,CAAC,KAAK,QAAQ,EAC9B;4BACA,OAAO;gCACL,GAAGD,GAAG;gCACNG,IAAI,EAAEH,GAAG,CAACC,KAAK,CAAC;6BACjB,CAAC;yBACH;qBACF;oBACD,OAAOD,GAAG,CAAC;iBACZ;gBACD,2GAA2G;gBAC3G,qCAAqC;gBACrClD,gBAAgB;gBAChBsD,eAAe,EAACC,YAAY,EAAEC,OAAO,EAAE;wBAEnCjC,GAA2B;oBAD7B,OACEA,CAAAA,GAA2B,GAA3BA,OAAO,CAACkC,UAAU,CAACD,OAAO,CAAC,YAA3BjC,GAA2B,GAC3Bf,IAAI,CAACkD,KAAK,CACR,mBAAmB;oBACnBH,YAAY,CAACI,OAAO,CAAC,CACtB,CACD;iBACH;gBACDC,kBAAkB,EAAEjC,QAAQ;gBAE5BkC,UAAU,EACR,mDAAmD;gBACnDlC,QAAQ,GACJkB,SAAS,GAET,CAACK,GAAQ,EAAEY,aAAqB,EAAEC,cAAsB,GAAa;oBACnE,IAAIC,YAAY,GAAGD,cAAc,AAAC;oBAClC,IAAIC,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;wBAC3BA,YAAY,GAAG,CAAC,EAAE,EAAEA,YAAY,CAAC,CAAC,CAAC;qBACpC;oBAED,MAAMC,YAAY,GAAGf,GAAG,CAACgB,OAAO,AAAC;oBACjC,IAAID,YAAY,KAAKpB,SAAS,EAAE;wBAC9B,OAAO,EAAE,CAAC;qBACX;wBAIkBoB,aAA0B;oBAF7C,8DAA8D;oBAC9D,wEAAwE;oBACxE,MAAME,UAAU,GAAGF,CAAAA,aAA0B,GAA1BA,YAAY,CAACD,YAAY,CAAC,YAA1BC,aAA0B,GAAIA,YAAY,CAACD,YAAY,GAAG,KAAK,CAAC,AAAC;oBACpF,IAAIG,UAAU,KAAK,KAAK,EAAE;wBACxB,MAAM,IAAI7E,eAAe,EAAE,CAAC;qBAC7B;oBACD,OAAO6E,UAAU,CAAC;iBACnB;aACR,CAAC,CAAC;SACJ,CAAC,OAAOC,KAAK,EAAO;YACnB,IAAIA,KAAK,YAAY9E,eAAe,EAAE;gBACpC,OAAO;oBACL+E,IAAI,EAAE,OAAO;iBACd,CAAC;aACH;YAED,IAAI,MAAM,IAAID,KAAK,IAAIA,KAAK,CAACrE,IAAI,KAAK,kBAAkB,EAAE;gBACxD,IAAIuE,CAAAA,GAAAA,UAAc,AAAY,CAAA,eAAZ,CAAC9C,UAAU,CAAC,EAAE;oBAC9B,sDAAsD;oBACtD,OAAO;wBACL6C,IAAI,EAAE,OAAO;qBACd,CAAC;iBACH;gBAED,MAAM,IAAIjF,wBAAwB,CAChC,mFAAmF,GACjF,CAAC,EAAE,EAAEmF,CAAAA,GAAAA,qBAAoB,AAOxB,CAAA,qBAPwB,CACvB;oBACEF,IAAI,EAAE,YAAY;oBAClBG,cAAc,EAAEhD,UAAU;oBAC1BiD,aAAa,EAAE7C,UAAU;iBAC1B,EACD,IAAI,CACL,CAAC,aAAa,EAAEL,OAAO,CAACkB,gBAAgB,CAAC,EAAE,CAAC,CAChD,CAAC;aACH;YACD,MAAM2B,KAAK,CAAC;SACb;QAED,IAAI7C,OAAO,CAACM,UAAU,CAAC6C,IAAI,CAAC,CAAC3D,GAAG,GAAKgB,EAAE,CAAC4C,QAAQ,CAAC5D,GAAG,CAAC;QAAA,CAAC,EAAE;YACtD,OAAO;gBACLsD,IAAI,EAAE,YAAY;gBAClBO,QAAQ,EAAE7C,EAAE;aACb,CAAC;SACH;QAED,IAAIuC,CAAAA,GAAAA,UAAc,AAAI,CAAA,eAAJ,CAACvC,EAAE,CAAC,EAAE;YACtB,IAAIJ,QAAQ,EAAE;gBACZ,OAAO;oBACL0C,IAAI,EAAE,YAAY;oBAClBO,QAAQ,EAAE7C,EAAE;iBACb,CAAC;aACH;YACD,wDAAwD;YACxD,6CAA6C;YAC7C,OAAO;gBACLsC,IAAI,EAAE,OAAO;aACd,CAAC;SACH;QAED,4DAA4D;QAE5D,IAAI5E,QAAQ,KAAK,KAAK,EAAE;YACtB,+EAA+E;YAC/E,2EAA2E;YAC3E,OAAO;gBACL4E,IAAI,EAAE,YAAY;gBAClBQ,SAAS,EAAE;oBAAC9C,EAAE;iBAAC;aAChB,CAAC;SACH;QAED,MAAM+C,OAAO,GAAGvC,KAAI,QAAA,CAACC,OAAO,CAACT,EAAE,CAAC,AAAC;QACjC,MAAMgD,SAAS,GAAGxC,KAAI,QAAA,CAACyC,OAAO,CAACjD,EAAE,CAAC,AAAC;QACnC,MAAMkD,QAAQ,GAAG1C,KAAI,QAAA,CAAC0C,QAAQ,CAAClD,EAAE,EAAEgD,SAAS,CAAC,AAAC;YAIjCxD,IAAkD;QAH/D,OAAO;YACL8C,IAAI,EAAE,YAAY;YAClB,+CAA+C;YAC/CQ,SAAS,EAAEtD,CAAAA,IAAkD,GAAlDA,OAAO,CAAC2D,YAAY,CAACJ,OAAO,EAAEG,QAAQ,EAAEF,SAAS,CAAC,YAAlDxD,IAAkD,GAAI;gBAACQ,EAAE;aAAC;SACtE,CAAC;KACH;IAED,OAAOT,WAAW,CAAC;CACpB"}