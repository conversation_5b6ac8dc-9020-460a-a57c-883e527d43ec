{"version": 3, "sources": ["../../../src/utils/nodeEnv.ts"], "sourcesContent": ["// Set the environment to production or development\n// lots of tools use this to determine if they should run in a dev mode.\nexport function setNodeEnv(mode: 'development' | 'production') {\n  process.env.NODE_ENV = process.env.NODE_ENV || mode;\n  process.env.BABEL_ENV = process.env.BABEL_ENV || process.env.NODE_ENV;\n}\n"], "names": ["setNodeEnv", "mode", "process", "env", "NODE_ENV", "BABEL_ENV"], "mappings": "AAEA;;;;QAAgBA,UAAU,GAAVA,UAAU;AAAnB,SAASA,UAAU,CAACC,IAAkC,EAAE;IAC7DC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAGF,OAAO,CAACC,GAAG,CAACC,QAAQ,IAAIH,IAAI,CAAC;IACpDC,OAAO,CAACC,GAAG,CAACE,SAAS,GAAGH,OAAO,CAACC,GAAG,CAACE,SAAS,IAAIH,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;CACvE"}