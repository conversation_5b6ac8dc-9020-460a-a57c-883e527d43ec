{"version": 3, "sources": ["../../../../../../src/start/server/middleware/inspector/LaunchBrowserImplLinux.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport { type ChildProcess } from 'child_process';\nimport open from 'open';\nimport path from 'path';\n\nimport {\n  LaunchBrowserTypes,\n  type LaunchBrowser,\n  type LaunchBrowserInstance,\n  LaunchBrowserTypesEnum,\n} from './LaunchBrowser.types';\n\n/**\n * Browser implementation for Linux\n */\nexport default class LaunchBrowserImplLinux implements LaunchBrowser, LaunchBrowserInstance {\n  private _appId: string | undefined;\n  private _process: ChildProcess | undefined;\n\n  MAP = {\n    [LaunchBrowserTypesEnum.CHROME]: ['google-chrome', 'google-chrome-stable', 'chromium'],\n    [LaunchBrowserTypesEnum.EDGE]: ['microsoft-edge', 'microsoft-edge-dev'],\n    [LaunchBrowserTypesEnum.BRAVE]: ['brave'],\n  };\n\n  /**\n   * On Linux, the supported appId is an array, this function finds the available appId and caches it\n   */\n  private async getAppId(browserType: LaunchBrowserTypes): Promise<string> {\n    if (this._appId == null || !this.MAP[browserType].includes(this._appId)) {\n      for (const appId of this.MAP[browserType]) {\n        try {\n          const { status } = await spawnAsync('which', [appId], { stdio: 'ignore' });\n          if (status === 0) {\n            this._appId = appId;\n            break;\n          }\n        } catch {}\n      }\n    }\n\n    if (this._appId == null) {\n      throw new Error(\n        `Unable to find supported browser - tried[${this.MAP[browserType].join(', ')}]`\n      );\n    }\n\n    return this._appId;\n  }\n\n  async isSupportedBrowser(browserType: LaunchBrowserTypes): Promise<boolean> {\n    let result = false;\n    try {\n      await this.getAppId(browserType);\n      result = true;\n    } catch {\n      result = false;\n    }\n    return result;\n  }\n\n  async createTempBrowserDir(baseDirName: string) {\n    return path.join(require('temp-dir'), baseDirName);\n  }\n\n  async launchAsync(\n    browserType: LaunchBrowserTypes,\n    args: string[]\n  ): Promise<LaunchBrowserInstance> {\n    const appId = await this.getAppId(browserType);\n    this._process = await open.openApp(appId, { arguments: args });\n    return this;\n  }\n\n  async close(): Promise<void> {\n    this._process?.kill();\n    this._process = undefined;\n    this._appId = undefined;\n  }\n}\n"], "names": ["LaunchBrowserImplLinux", "MAP", "LaunchBrowserTypesEnum", "CHROME", "EDGE", "BRAVE", "getAppId", "browserType", "_appId", "includes", "appId", "status", "spawnAsync", "stdio", "Error", "join", "isSupportedBrowser", "result", "createTempBrowserDir", "baseDirName", "path", "require", "launchAsync", "args", "_process", "open", "openApp", "arguments", "close", "kill", "undefined"], "mappings": "AAAA;;;;;AAAuB,IAAA,WAAmB,kCAAnB,mBAAmB,EAAA;AAEzB,IAAA,KAAM,kCAAN,MAAM,EAAA;AACN,IAAA,KAAM,kCAAN,MAAM,EAAA;AAOhB,IAAA,mBAAuB,WAAvB,uBAAuB,CAAA;AAKf,MAAMA,sBAAsB;IAIzCC,GAAG,GAAG;QACJ,CAACC,mBAAsB,uBAAA,CAACC,MAAM,CAAC,EAAE;YAAC,eAAe;YAAE,sBAAsB;YAAE,UAAU;SAAC;QACtF,CAACD,mBAAsB,uBAAA,CAACE,IAAI,CAAC,EAAE;YAAC,gBAAgB;YAAE,oBAAoB;SAAC;QACvE,CAACF,mBAAsB,uBAAA,CAACG,KAAK,CAAC,EAAE;YAAC,OAAO;SAAC;KAC1C,CAAC;IAEF;;KA<PERSON>,CACH,MAAcC,QAAQ,CAACC,WAA+B,EAAmB;QACvE,IAAI,IAAI,CAACC,MAAM,IAAI,IAAI,IAAI,CAAC,IAAI,CAACP,GAAG,CAACM,WAAW,CAAC,CAACE,QAAQ,CAAC,IAAI,CAACD,MAAM,CAAC,EAAE;YACvE,KAAK,MAAME,KAAK,IAAI,IAAI,CAACT,GAAG,CAACM,WAAW,CAAC,CAAE;gBACzC,IAAI;oBACF,MAAM,EAAEI,MAAM,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,WAAU,AAAuC,CAAA,QAAvC,CAAC,OAAO,EAAE;wBAACF,KAAK;qBAAC,EAAE;wBAAEG,KAAK,EAAE,QAAQ;qBAAE,CAAC,AAAC;oBAC3E,IAAIF,MAAM,KAAK,CAAC,EAAE;wBAChB,IAAI,CAACH,MAAM,GAAGE,KAAK,CAAC;wBACpB,MAAM;qBACP;iBACF,CAAC,OAAM,EAAE;aACX;SACF;QAED,IAAI,IAAI,CAACF,MAAM,IAAI,IAAI,EAAE;YACvB,MAAM,IAAIM,KAAK,CACb,CAAC,yCAAyC,EAAE,IAAI,CAACb,GAAG,CAACM,WAAW,CAAC,CAACQ,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAChF,CAAC;SACH;QAED,OAAO,IAAI,CAACP,MAAM,CAAC;KACpB;IAED,MAAMQ,kBAAkB,CAACT,WAA+B,EAAoB;QAC1E,IAAIU,MAAM,GAAG,KAAK,AAAC;QACnB,IAAI;YACF,MAAM,IAAI,CAACX,QAAQ,CAACC,WAAW,CAAC,CAAC;YACjCU,MAAM,GAAG,IAAI,CAAC;SACf,CAAC,OAAM;YACNA,MAAM,GAAG,KAAK,CAAC;SAChB;QACD,OAAOA,MAAM,CAAC;KACf;IAED,MAAMC,oBAAoB,CAACC,WAAmB,EAAE;QAC9C,OAAOC,KAAI,QAAA,CAACL,IAAI,CAACM,OAAO,CAAC,UAAU,CAAC,EAAEF,WAAW,CAAC,CAAC;KACpD;IAED,MAAMG,WAAW,CACff,WAA+B,EAC/BgB,IAAc,EACkB;QAChC,MAAMb,KAAK,GAAG,MAAM,IAAI,CAACJ,QAAQ,CAACC,WAAW,CAAC,AAAC;QAC/C,IAAI,CAACiB,QAAQ,GAAG,MAAMC,KAAI,QAAA,CAACC,OAAO,CAAChB,KAAK,EAAE;YAAEiB,SAAS,EAAEJ,IAAI;SAAE,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;KACb;IAED,MAAMK,KAAK,GAAkB;YAC3B,GAAa;QAAb,CAAA,GAAa,GAAb,IAAI,CAACJ,QAAQ,SAAM,GAAnB,KAAA,CAAmB,GAAnB,GAAa,CAAEK,IAAI,EAAE,AA3EzB,CA2E0B;QACtB,IAAI,CAACL,QAAQ,GAAGM,SAAS,CAAC;QAC1B,IAAI,CAACtB,MAAM,GAAGsB,SAAS,CAAC;KACzB;CACF;kBAhEoB9B,sBAAsB"}