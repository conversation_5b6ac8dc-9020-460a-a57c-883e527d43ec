{"version": 3, "sources": ["../../../../../src/start/server/webpack/formatWebpackMessages.ts"], "sourcesContent": ["/**\n * Copyright (c) 2022 Expo, Inc.\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on https://github.com/facebook/create-react-app/blob/b172b5e/packages/react-dev-utils/ModuleNotFoundPlugin.js\n * But with Node LTS support.\n */\n\nimport type { Stats } from 'webpack';\n\nconst friendlySyntaxErrorLabel = 'Syntax error:';\n\nfunction isLikelyASyntaxError(message: string): boolean {\n  return message.indexOf(friendlySyntaxErrorLabel) !== -1;\n}\n\n// Cleans up webpack error messages.\nfunction formatMessage(message: string | { message: string } | { message: string }[]) {\n  let lines: string[] = [];\n\n  if (typeof message === 'string') {\n    lines = message.split('\\n');\n  } else if ('message' in message) {\n    lines = message['message'].split('\\n');\n  } else if (Array.isArray(message)) {\n    message.forEach((message) => {\n      if ('message' in message) {\n        lines = message['message'].split('\\n');\n      }\n    });\n  }\n\n  // Strip webpack-added headers off errors/warnings\n  // https://github.com/webpack/webpack/blob/master/lib/ModuleError.js\n  lines = lines.filter((line) => !/Module [A-z ]+\\(from/.test(line));\n\n  // Transform parsing error into syntax error\n  // TODO: move this to our ESLint formatter?\n  lines = lines.map((line) => {\n    const parsingError = /Line (\\d+):(?:(\\d+):)?\\s*Parsing error: (.+)$/.exec(line);\n    if (!parsingError) {\n      return line;\n    }\n    const [, errorLine, errorColumn, errorMessage] = parsingError;\n    return `${friendlySyntaxErrorLabel} ${errorMessage} (${errorLine}:${errorColumn})`;\n  });\n\n  message = lines.join('\\n');\n  // Smoosh syntax errors (commonly found in CSS)\n  message = message.replace(\n    /SyntaxError\\s+\\((\\d+):(\\d+)\\)\\s*(.+?)\\n/g,\n    `${friendlySyntaxErrorLabel} $3 ($1:$2)\\n`\n  );\n  // Clean up export errors\n  message = message.replace(\n    /^.*export '(.+?)' was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$1' is not exported from '$2'.`\n  );\n  message = message.replace(\n    /^.*export 'default' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$2' does not contain a default export (imported as '$1').`\n  );\n  message = message.replace(\n    /^.*export '(.+?)' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$1' is not exported from '$3' (imported as '$2').`\n  );\n  lines = message.split('\\n');\n\n  // Remove leading newline\n  if (lines.length > 2 && lines[1].trim() === '') {\n    lines.splice(1, 1);\n  }\n  // Clean up file name\n  lines[0] = lines[0].replace(/^(.*) \\d+:\\d+-\\d+$/, '$1');\n\n  // Cleans up verbose \"module not found\" messages for files and packages.\n  if (lines[1] && lines[1].indexOf('Module not found: ') === 0) {\n    lines = [\n      lines[0],\n      lines[1]\n        .replace('Error: ', '')\n        .replace('Module not found: Cannot find file:', 'Cannot find file:'),\n    ];\n  }\n\n  // Add helpful message for users trying to use Sass for the first time\n  if (lines[1] && lines[1].match(/Cannot find module.+sass/)) {\n    lines[1] = 'To import Sass files, you first need to install sass.\\n';\n    lines[1] += 'Run `npm install sass` or `yarn add sass` inside your workspace.';\n  }\n\n  message = lines.join('\\n');\n  // Internal stacks are generally useless so we strip them... with the\n  // exception of stacks containing `webpack:` because they're normally\n  // from user code generated by webpack. For more information see\n  // https://github.com/facebook/create-react-app/pull/1050\n  message = message.replace(/^\\s*at\\s((?!webpack:).)*:\\d+:\\d+[\\s)]*(\\n|$)/gm, ''); // at ... ...:x:y\n  message = message.replace(/^\\s*at\\s<anonymous>(\\n|$)/gm, ''); // at <anonymous>\n  lines = message.split('\\n');\n\n  // Remove duplicated newlines\n  lines = lines.filter(\n    (line, index, arr) => index === 0 || line.trim() !== '' || line.trim() !== arr[index - 1].trim()\n  );\n\n  // Reassemble the message\n  message = lines.join('\\n');\n  return message.trim();\n}\n\nexport function formatWebpackMessages(json?: Stats.ToJsonOutput) {\n  const formattedErrors = json?.errors?.map(formatMessage);\n  const formattedWarnings = json?.warnings?.map(formatMessage);\n  const result = { errors: formattedErrors, warnings: formattedWarnings };\n  if (result.errors?.some(isLikelyASyntaxError)) {\n    // If there are any syntax errors, show just them.\n    result.errors = result.errors.filter(isLikelyASyntaxError);\n  }\n  return result;\n}\n"], "names": ["formatWebpackMessages", "friendlySyntaxErrorLabel", "isLikelyASyntaxError", "message", "indexOf", "formatMessage", "lines", "split", "Array", "isArray", "for<PERSON>ach", "filter", "line", "test", "map", "parsingError", "exec", "errorLine", "errorColumn", "errorMessage", "join", "replace", "length", "trim", "splice", "match", "index", "arr", "json", "result", "formattedErrors", "errors", "formattedWarnings", "warnings", "some"], "mappings": "AAWA;;;;QAsGgBA,qBAAqB,GAArBA,qBAAqB;AApGrC,MAAMC,wBAAwB,GAAG,eAAe,AAAC;AAEjD,SAASC,oBAAoB,CAACC,OAAe,EAAW;IACtD,OAAOA,OAAO,CAACC,OAAO,CAACH,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC;CACzD;AAED,oCAAoC;AACpC,SAASI,aAAa,CAACF,QAA6D,EAAE;IACpF,IAAIG,KAAK,GAAa,EAAE,AAAC;IAEzB,IAAI,OAAOH,QAAO,KAAK,QAAQ,EAAE;QAC/BG,KAAK,GAAGH,QAAO,CAACI,KAAK,CAAC,IAAI,CAAC,CAAC;KAC7B,MAAM,IAAI,SAAS,IAAIJ,QAAO,EAAE;QAC/BG,KAAK,GAAGH,QAAO,CAAC,SAAS,CAAC,CAACI,KAAK,CAAC,IAAI,CAAC,CAAC;KACxC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACN,QAAO,CAAC,EAAE;QACjCA,QAAO,CAACO,OAAO,CAAC,CAACP,OAAO,GAAK;YAC3B,IAAI,SAAS,IAAIA,OAAO,EAAE;gBACxBG,KAAK,GAAGH,OAAO,CAAC,SAAS,CAAC,CAACI,KAAK,CAAC,IAAI,CAAC,CAAC;aACxC;SACF,CAAC,CAAC;KACJ;IAED,kDAAkD;IAClD,oEAAoE;IACpED,KAAK,GAAGA,KAAK,CAACK,MAAM,CAAC,CAACC,IAAI,GAAK,CAAC,uBAAuBC,IAAI,CAACD,IAAI,CAAC;IAAA,CAAC,CAAC;IAEnE,4CAA4C;IAC5C,2CAA2C;IAC3CN,KAAK,GAAGA,KAAK,CAACQ,GAAG,CAAC,CAACF,IAAI,GAAK;QAC1B,MAAMG,YAAY,GAAG,gDAAgDC,IAAI,CAACJ,IAAI,CAAC,AAAC;QAChF,IAAI,CAACG,YAAY,EAAE;YACjB,OAAOH,IAAI,CAAC;SACb;QACD,MAAM,GAAGK,SAAS,EAAEC,WAAW,EAAEC,YAAY,CAAC,GAAGJ,YAAY,AAAC;QAC9D,OAAO,CAAC,EAAEd,wBAAwB,CAAC,CAAC,EAAEkB,YAAY,CAAC,EAAE,EAAEF,SAAS,CAAC,CAAC,EAAEC,WAAW,CAAC,CAAC,CAAC,CAAC;KACpF,CAAC,CAAC;IAEHf,QAAO,GAAGG,KAAK,CAACc,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3B,+CAA+C;IAC/CjB,QAAO,GAAGA,QAAO,CAACkB,OAAO,6CAEvB,CAAC,EAAEpB,wBAAwB,CAAC,aAAa,CAAC,CAC3C,CAAC;IACF,yBAAyB;IACzBE,QAAO,GAAGA,QAAO,CAACkB,OAAO,oDAEvB,CAAC,uDAAuD,CAAC,CAC1D,CAAC;IACFlB,QAAO,GAAGA,QAAO,CAACkB,OAAO,8EAEvB,CAAC,kFAAkF,CAAC,CACrF,CAAC;IACFlB,QAAO,GAAGA,QAAO,CAACkB,OAAO,4EAEvB,CAAC,0EAA0E,CAAC,CAC7E,CAAC;IACFf,KAAK,GAAGH,QAAO,CAACI,KAAK,CAAC,IAAI,CAAC,CAAC;IAE5B,yBAAyB;IACzB,IAAID,KAAK,CAACgB,MAAM,GAAG,CAAC,IAAIhB,KAAK,CAAC,CAAC,CAAC,CAACiB,IAAI,EAAE,KAAK,EAAE,EAAE;QAC9CjB,KAAK,CAACkB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACpB;IACD,qBAAqB;IACrBlB,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACe,OAAO,uBAAuB,IAAI,CAAC,CAAC;IAExD,wEAAwE;IACxE,IAAIf,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACF,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;QAC5DE,KAAK,GAAG;YACNA,KAAK,CAAC,CAAC,CAAC;YACRA,KAAK,CAAC,CAAC,CAAC,CACLe,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CACtBA,OAAO,CAAC,qCAAqC,EAAE,mBAAmB,CAAC;SACvE,CAAC;KACH;IAED,sEAAsE;IACtE,IAAIf,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACmB,KAAK,4BAA4B,EAAE;QAC1DnB,KAAK,CAAC,CAAC,CAAC,GAAG,yDAAyD,CAAC;QACrEA,KAAK,CAAC,CAAC,CAAC,IAAI,kEAAkE,CAAC;KAChF;IAEDH,QAAO,GAAGG,KAAK,CAACc,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3B,qEAAqE;IACrE,qEAAqE;IACrE,gEAAgE;IAChE,yDAAyD;IACzDjB,QAAO,GAAGA,QAAO,CAACkB,OAAO,mDAAmD,EAAE,CAAC,CAAC,CAAC,iBAAiB;IAClGlB,QAAO,GAAGA,QAAO,CAACkB,OAAO,gCAAgC,EAAE,CAAC,CAAC,CAAC,iBAAiB;IAC/Ef,KAAK,GAAGH,QAAO,CAACI,KAAK,CAAC,IAAI,CAAC,CAAC;IAE5B,6BAA6B;IAC7BD,KAAK,GAAGA,KAAK,CAACK,MAAM,CAClB,CAACC,IAAI,EAAEc,KAAK,EAAEC,GAAG,GAAKD,KAAK,KAAK,CAAC,IAAId,IAAI,CAACW,IAAI,EAAE,KAAK,EAAE,IAAIX,IAAI,CAACW,IAAI,EAAE,KAAKI,GAAG,CAACD,KAAK,GAAG,CAAC,CAAC,CAACH,IAAI,EAAE;IAAA,CACjG,CAAC;IAEF,yBAAyB;IACzBpB,QAAO,GAAGG,KAAK,CAACc,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3B,OAAOjB,QAAO,CAACoB,IAAI,EAAE,CAAC;CACvB;AAEM,SAASvB,qBAAqB,CAAC4B,IAAyB,EAAE;QACvCA,GAAY,EACVA,IAAc,EAEpCC,IAAa;IAHjB,MAAMC,eAAe,GAAGF,IAAI,QAAQ,GAAZA,KAAAA,CAAY,GAAZA,CAAAA,GAAY,GAAZA,IAAI,CAAEG,MAAM,SAAA,GAAZH,KAAAA,CAAY,GAAZA,GAAY,CAAEd,GAAG,CAACT,aAAa,CAAC,AAAC;IACzD,MAAM2B,iBAAiB,GAAGJ,IAAI,QAAU,GAAdA,KAAAA,CAAc,GAAdA,CAAAA,IAAc,GAAdA,IAAI,CAAEK,QAAQ,SAAA,GAAdL,KAAAA,CAAc,GAAdA,IAAc,CAAEd,GAAG,CAACT,aAAa,CAAC,AAAC;IAC7D,MAAMwB,MAAM,GAAG;QAAEE,MAAM,EAAED,eAAe;QAAEG,QAAQ,EAAED,iBAAiB;KAAE,AAAC;IACxE,IAAIH,CAAAA,IAAa,GAAbA,MAAM,CAACE,MAAM,SAAM,GAAnBF,KAAAA,CAAmB,GAAnBA,IAAa,CAAEK,IAAI,CAAChC,oBAAoB,CAAC,EAAE;QAC7C,kDAAkD;QAClD2B,MAAM,CAACE,MAAM,GAAGF,MAAM,CAACE,MAAM,CAACpB,MAAM,CAACT,oBAAoB,CAAC,CAAC;KAC5D;IACD,OAAO2B,MAAM,CAAC;CACf"}