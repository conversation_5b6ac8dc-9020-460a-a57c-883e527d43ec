{"version": 3, "sources": ["../../../../../src/start/server/metro/serializeHtml.ts"], "sourcesContent": ["import { SerialAsset } from '@expo/metro-config/build/serializer/serializerAssets';\nimport { RouteNode } from 'expo-router/build/Route';\n\nconst debug = require('debug')('expo:metro:html') as typeof console.log;\n\nexport function serializeHtmlWithAssets({\n  mode,\n  resources,\n  template,\n  devBundleUrl,\n  baseUrl,\n  route,\n}: {\n  mode: 'development' | 'production';\n  resources: SerialAsset[];\n  template: string;\n  /** asset prefix used for deploying to non-standard origins like GitHub pages. */\n  baseUrl: string;\n  devBundleUrl?: string;\n  route?: RouteNode;\n}): string {\n  if (!resources) {\n    return '';\n  }\n  const isDev = mode === 'development';\n  return htmlFromSerialAssets(resources, {\n    dev: isDev,\n    template,\n    baseUrl,\n    bundleUrl: isDev ? devBundleUrl : undefined,\n    route,\n  });\n}\n\nfunction htmlFromSerialAssets(\n  assets: SerialAsset[],\n  {\n    dev,\n    template,\n    baseUrl,\n    bundleUrl,\n    route,\n  }: {\n    dev: boolean;\n    template: string;\n    baseUrl: string;\n    /** This is dev-only. */\n    bundleUrl?: string;\n    route?: RouteNode;\n  }\n) {\n  // Combine the CSS modules into tags that have hot refresh data attributes.\n  const styleString = assets\n    .filter((asset) => asset.type === 'css')\n    .map(({ metadata, filename, source }) => {\n      if (dev) {\n        return `<style data-expo-css-hmr=\"${metadata.hmrId}\">` + source + '\\n</style>';\n      } else {\n        return [\n          `<link rel=\"preload\" href=\"${baseUrl}/${filename}\" as=\"style\">`,\n          `<link rel=\"stylesheet\" href=\"${baseUrl}/${filename}\">`,\n        ].join('');\n      }\n    })\n    .join('');\n\n  const jsAssets = assets.filter((asset) => asset.type === 'js');\n\n  const scripts = bundleUrl\n    ? `<script src=\"${bundleUrl}\" defer></script>`\n    : jsAssets\n        .map(({ filename, metadata }) => {\n          // TODO: Mark dependencies of the HTML and include them to prevent waterfalls.\n          if (metadata.isAsync) {\n            // We have the data required to match async chunks to the route's HTML file.\n            if (\n              route?.entryPoints &&\n              metadata.modulePaths &&\n              Array.isArray(route.entryPoints) &&\n              Array.isArray(metadata.modulePaths)\n            ) {\n              // TODO: Handle module IDs like `expo-router/build/views/Unmatched.js`\n              const doesAsyncChunkContainRouteEntryPoint = route.entryPoints.some((entryPoint) =>\n                (metadata.modulePaths as string[]).includes(entryPoint)\n              );\n              if (!doesAsyncChunkContainRouteEntryPoint) {\n                return '';\n              }\n              debug('Linking async chunk %s to HTML for route %s', filename, route.contextKey);\n              // Pass through to the next condition.\n            } else {\n              return '';\n            }\n            // Mark async chunks as defer so they don't block the page load.\n            // return `<script src=\"${baseUrl}/${filename}\" defer></script>`;\n          }\n\n          return `<script src=\"${baseUrl}/${filename}\" defer></script>`;\n        })\n        .join('');\n\n  return template\n    .replace('</head>', `${styleString}</head>`)\n    .replace('</body>', `${scripts}\\n</body>`);\n}\n"], "names": ["serializeHtmlWithAssets", "debug", "require", "mode", "resources", "template", "devBundleUrl", "baseUrl", "route", "isDev", "htmlFromSerialAssets", "dev", "bundleUrl", "undefined", "assets", "styleString", "filter", "asset", "type", "map", "metadata", "filename", "source", "hmrId", "join", "jsAssets", "scripts", "isAsync", "entryPoints", "modulePaths", "Array", "isArray", "doesAsyncChunkContainRouteEntryPoint", "some", "entryPoint", "includes", "<PERSON><PERSON>ey", "replace"], "mappings": "AAAA;;;;QAKgBA,uBAAuB,GAAvBA,uBAAuB;AAFvC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,AAAsB,AAAC;AAEjE,SAASF,uBAAuB,CAAC,EACtCG,IAAI,CAAA,EACJC,SAAS,CAAA,EACTC,QAAQ,CAAA,EACRC,YAAY,CAAA,EACZC,OAAO,CAAA,EACPC,KAAK,CAAA,EASN,EAAU;IACT,IAAI,CAACJ,SAAS,EAAE;QACd,OAAO,EAAE,CAAC;KACX;IACD,MAAMK,KAAK,GAAGN,IAAI,KAAK,aAAa,AAAC;IACrC,OAAOO,oBAAoB,CAACN,SAAS,EAAE;QACrCO,GAAG,EAAEF,KAAK;QACVJ,QAAQ;QACRE,OAAO;QACPK,SAAS,EAAEH,KAAK,GAAGH,YAAY,GAAGO,SAAS;QAC3CL,KAAK;KACN,CAAC,CAAC;CACJ;AAED,SAASE,oBAAoB,CAC3BI,MAAqB,EACrB,EACEH,GAAG,CAAA,EACHN,QAAQ,CAAA,EACRE,OAAO,CAAA,EACPK,SAAS,CAAA,EACTJ,KAAK,CAAA,EAQN,EACD;IACA,2EAA2E;IAC3E,MAAMO,WAAW,GAAGD,MAAM,CACvBE,MAAM,CAAC,CAACC,KAAK,GAAKA,KAAK,CAACC,IAAI,KAAK,KAAK;IAAA,CAAC,CACvCC,GAAG,CAAC,CAAC,EAAEC,QAAQ,CAAA,EAAEC,QAAQ,CAAA,EAAEC,MAAM,CAAA,EAAE,GAAK;QACvC,IAAIX,GAAG,EAAE;YACP,OAAO,CAAC,0BAA0B,EAAES,QAAQ,CAACG,KAAK,CAAC,EAAE,CAAC,GAAGD,MAAM,GAAG,YAAY,CAAC;SAChF,MAAM;YACL,OAAO;gBACL,CAAC,0BAA0B,EAAEf,OAAO,CAAC,CAAC,EAAEc,QAAQ,CAAC,aAAa,CAAC;gBAC/D,CAAC,6BAA6B,EAAEd,OAAO,CAAC,CAAC,EAAEc,QAAQ,CAAC,EAAE,CAAC;aACxD,CAACG,IAAI,CAAC,EAAE,CAAC,CAAC;SACZ;KACF,CAAC,CACDA,IAAI,CAAC,EAAE,CAAC,AAAC;IAEZ,MAAMC,QAAQ,GAAGX,MAAM,CAACE,MAAM,CAAC,CAACC,KAAK,GAAKA,KAAK,CAACC,IAAI,KAAK,IAAI;IAAA,CAAC,AAAC;IAE/D,MAAMQ,OAAO,GAAGd,SAAS,GACrB,CAAC,aAAa,EAAEA,SAAS,CAAC,iBAAiB,CAAC,GAC5Ca,QAAQ,CACLN,GAAG,CAAC,CAAC,EAAEE,QAAQ,CAAA,EAAED,QAAQ,CAAA,EAAE,GAAK;QAC/B,8EAA8E;QAC9E,IAAIA,QAAQ,CAACO,OAAO,EAAE;YACpB,4EAA4E;YAC5E,IACEnB,CAAAA,KAAK,QAAa,GAAlBA,KAAAA,CAAkB,GAAlBA,KAAK,CAAEoB,WAAW,CAAA,IAClBR,QAAQ,CAACS,WAAW,IACpBC,KAAK,CAACC,OAAO,CAACvB,KAAK,CAACoB,WAAW,CAAC,IAChCE,KAAK,CAACC,OAAO,CAACX,QAAQ,CAACS,WAAW,CAAC,EACnC;gBACA,sEAAsE;gBACtE,MAAMG,oCAAoC,GAAGxB,KAAK,CAACoB,WAAW,CAACK,IAAI,CAAC,CAACC,UAAU,GAC7E,AAACd,QAAQ,CAACS,WAAW,CAAcM,QAAQ,CAACD,UAAU,CAAC;gBAAA,CACxD,AAAC;gBACF,IAAI,CAACF,oCAAoC,EAAE;oBACzC,OAAO,EAAE,CAAC;iBACX;gBACD/B,KAAK,CAAC,6CAA6C,EAAEoB,QAAQ,EAAEb,KAAK,CAAC4B,UAAU,CAAC,CAAC;YACjF,sCAAsC;aACvC,MAAM;gBACL,OAAO,EAAE,CAAC;aACX;QACD,gEAAgE;QAChE,iEAAiE;SAClE;QAED,OAAO,CAAC,aAAa,EAAE7B,OAAO,CAAC,CAAC,EAAEc,QAAQ,CAAC,iBAAiB,CAAC,CAAC;KAC/D,CAAC,CACDG,IAAI,CAAC,EAAE,CAAC,AAAC;IAEhB,OAAOnB,QAAQ,CACZgC,OAAO,CAAC,SAAS,EAAE,CAAC,EAAEtB,WAAW,CAAC,OAAO,CAAC,CAAC,CAC3CsB,OAAO,CAAC,SAAS,EAAE,CAAC,EAAEX,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;CAC9C"}