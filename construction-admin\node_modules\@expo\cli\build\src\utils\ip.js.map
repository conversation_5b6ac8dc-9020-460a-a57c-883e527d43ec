{"version": 3, "sources": ["../../../src/utils/ip.ts"], "sourcesContent": ["import internalIp from 'internal-ip';\n\nexport function getIpAddress(): string {\n  return internalIp.v4.sync() || '127.0.0.1';\n}\n"], "names": ["getIpAddress", "internalIp", "v4", "sync"], "mappings": "AAAA;;;;QAEgBA,YAAY,GAAZA,YAAY;AAFL,IAAA,WAAa,kCAAb,aAAa,EAAA;;;;;;AAE7B,SAASA,YAAY,GAAW;IACrC,OAAOC,WAAU,QAAA,CAACC,EAAE,CAACC,IAAI,EAAE,IAAI,WAAW,CAAC;CAC5C"}