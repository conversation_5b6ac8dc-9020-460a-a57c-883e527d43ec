"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
var _chalk = _interopRequireDefault(require("chalk"));
var _fs = require("fs");
var _path = _interopRequireDefault(require("path"));
var _resolveFrom = _interopRequireDefault(require("resolve-from"));
var _fn = require("./fn");
var Log = _interopRequireWildcard(require("../log"));
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _interopRequireWildcard(obj) {
    if (obj && obj.__esModule) {
        return obj;
    } else {
        var newObj = {};
        if (obj != null) {
            for(var key in obj){
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {};
                    if (desc.get || desc.set) {
                        Object.defineProperty(newObj, key, desc);
                    } else {
                        newObj[key] = obj[key];
                    }
                }
            }
        }
        newObj.default = obj;
        return newObj;
    }
}
const debug = require("debug")("expo:utils:fileNotifier");
class FileNotifier {
    static instances = [];
    static stopAll() {
        for (const instance of FileNotifier.instances){
            instance.stopObserving();
        }
    }
    constructor(projectRoot, moduleIds, settings = {}){
        this.projectRoot = projectRoot;
        this.moduleIds = moduleIds;
        this.settings = settings;
        this.unsubscribe = null;
        this.watchFile = (0, _fn).memoize(this.startWatchingFile.bind(this));
        FileNotifier.instances.push(this);
    }
    /** Get the file in the project. */ resolveFilePath() {
        for (const moduleId of this.moduleIds){
            const filePath = _resolveFrom.default.silent(this.projectRoot, moduleId);
            if (filePath) {
                return filePath;
            }
        }
        return null;
    }
    startObserving(callback) {
        const configPath = this.resolveFilePath();
        if (configPath) {
            debug(`Observing ${configPath}`);
            return this.watchFile(configPath, callback);
        }
        return configPath;
    }
    stopObserving() {
        var _obj, ref;
        (ref = (_obj = this).unsubscribe) == null ? void 0 : ref.call(_obj);
    }
    startWatchingFile(filePath, callback) {
        const configName = _path.default.relative(this.projectRoot, filePath);
        const listener = (cur, prev)=>{
            if (prev.size || cur.size) {
                Log.log(`\u203A Detected a change in ${_chalk.default.bold(configName)}. Restart the server to see the new results.` + (this.settings.additionalWarning || ""));
            }
        };
        const watcher = (0, _fs).watchFile(filePath, callback != null ? callback : listener);
        this.unsubscribe = ()=>{
            watcher.unref();
        };
        return filePath;
    }
}
exports.FileNotifier = FileNotifier;

//# sourceMappingURL=FileNotifier.js.map