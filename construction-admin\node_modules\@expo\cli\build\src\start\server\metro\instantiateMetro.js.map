{"version": 3, "sources": ["../../../../../src/start/server/metro/instantiateMetro.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\nimport { getDefaultConfig, LoadOptions } from '@expo/metro-config';\nimport chalk from 'chalk';\nimport { Server as ConnectServer } from 'connect';\nimport http from 'http';\nimport type Metro from 'metro';\nimport { loadConfig, resolveConfig, ConfigT } from 'metro-config';\nimport { Terminal } from 'metro-core';\nimport semver from 'semver';\nimport { URL } from 'url';\n\nimport { MetroBundlerDevServer } from './MetroBundlerDevServer';\nimport { MetroTerminalReporter } from './MetroTerminalReporter';\nimport { createDebugMiddleware } from './debugging/createDebugMiddleware';\nimport { runServer } from './runServer-fork';\nimport { withMetroMultiPlatformAsync } from './withMetroMultiPlatform';\nimport { MetroDevServerOptions } from '../../../export/fork-bundleAsync';\nimport { Log } from '../../../log';\nimport { getMetroProperties } from '../../../utils/analytics/getMetroProperties';\nimport { createDebuggerTelemetryMiddleware } from '../../../utils/analytics/metroDebuggerMiddleware';\nimport { logEventAsync } from '../../../utils/analytics/rudderstackClient';\nimport { env } from '../../../utils/env';\nimport { createCorsMiddleware } from '../middleware/CorsMiddleware';\nimport { getMetroServerRoot } from '../middleware/ManifestMiddleware';\nimport { createJsInspectorMiddleware } from '../middleware/inspector/createJsInspectorMiddleware';\nimport { prependMiddleware, replaceMiddlewareWith } from '../middleware/mutations';\nimport { ServerNext, ServerRequest, ServerResponse } from '../middleware/server.types';\nimport { suppressRemoteDebuggingErrorMiddleware } from '../middleware/suppressErrorMiddleware';\nimport { getPlatformBundlers } from '../platformBundlers';\n\n// From expo/dev-server but with ability to use custom logger.\ntype MessageSocket = {\n  broadcast: (method: string, params?: Record<string, any> | undefined) => void;\n};\n\nfunction gteSdkVersion(exp: Pick<ExpoConfig, 'sdkVersion'>, sdkVersion: string): boolean {\n  if (!exp.sdkVersion) {\n    return false;\n  }\n\n  if (exp.sdkVersion === 'UNVERSIONED') {\n    return true;\n  }\n\n  try {\n    return semver.gte(exp.sdkVersion, sdkVersion);\n  } catch {\n    throw new Error(`${exp.sdkVersion} is not a valid version. Must be in the form of x.y.z`);\n  }\n}\n\nexport async function loadMetroConfigAsync(\n  projectRoot: string,\n  options: LoadOptions,\n  {\n    exp = getConfig(projectRoot, { skipSDKVersionRequirement: true }).exp,\n    isExporting,\n  }: { exp?: ExpoConfig; isExporting: boolean }\n) {\n  let reportEvent: ((event: any) => void) | undefined;\n  const serverRoot = getMetroServerRoot(projectRoot);\n\n  const terminal = new Terminal(process.stdout);\n  const terminalReporter = new MetroTerminalReporter(serverRoot, terminal);\n\n  const hasConfig = await resolveConfig(options.config, projectRoot);\n  let config: ConfigT = {\n    ...(await loadConfig(\n      { cwd: projectRoot, projectRoot, ...options },\n      // If the project does not have a metro.config.js, then we use the default config.\n      hasConfig.isEmpty ? getDefaultConfig(projectRoot) : undefined\n    )),\n    reporter: {\n      update(event: any) {\n        terminalReporter.update(event);\n        if (reportEvent) {\n          reportEvent(event);\n        }\n      },\n    },\n  };\n\n  if (\n    // Requires SDK 50 for expo-assets hashAssetPlugin change.\n    !exp.sdkVersion ||\n    gteSdkVersion(exp, '50.0.0')\n  ) {\n    if (isExporting) {\n      // This token will be used in the asset plugin to ensure the path is correct for writing locally.\n      // @ts-expect-error: typed as readonly.\n      config.transformer.publicPath = `/assets?export_path=${\n        (exp.experiments?.baseUrl ?? '') + '/assets'\n      }`;\n    } else {\n      // @ts-expect-error: typed as readonly\n      config.transformer.publicPath = '/assets/?unstable_path=.';\n    }\n  } else {\n    if (isExporting && exp.experiments?.baseUrl) {\n      // This token will be used in the asset plugin to ensure the path is correct for writing locally.\n      // @ts-expect-error: typed as readonly.\n      config.transformer.publicPath = exp.experiments?.baseUrl;\n    }\n  }\n\n  const platformBundlers = getPlatformBundlers(projectRoot, exp);\n\n  config = await withMetroMultiPlatformAsync(projectRoot, {\n    config,\n    exp,\n    platformBundlers,\n    isTsconfigPathsEnabled: exp.experiments?.tsconfigPaths ?? true,\n    webOutput: exp.web?.output ?? 'single',\n    isFastResolverEnabled: env.EXPO_USE_FAST_RESOLVER,\n    isExporting,\n  });\n\n  if (process.env.NODE_ENV !== 'test') {\n    logEventAsync('metro config', getMetroProperties(projectRoot, exp, config));\n  }\n\n  return {\n    config,\n    setEventReporter: (logger: (event: any) => void) => (reportEvent = logger),\n    reporter: terminalReporter,\n  };\n}\n\n/** The most generic possible setup for Metro bundler. */\nexport async function instantiateMetroAsync(\n  metroBundler: MetroBundlerDevServer,\n  options: Omit<MetroDevServerOptions, 'logger'>,\n  { isExporting }: { isExporting: boolean }\n): Promise<{\n  metro: Metro.Server;\n  server: http.Server;\n  middleware: any;\n  messageSocket: MessageSocket;\n}> {\n  const projectRoot = metroBundler.projectRoot;\n\n  // TODO: When we bring expo/metro-config into the expo/expo repo, then we can upstream this.\n  const { exp } = getConfig(projectRoot, {\n    skipSDKVersionRequirement: true,\n  });\n\n  const { config: metroConfig, setEventReporter } = await loadMetroConfigAsync(\n    projectRoot,\n    options,\n    { exp, isExporting }\n  );\n\n  const { createDevServerMiddleware, securityHeadersMiddleware } =\n    require('@react-native-community/cli-server-api') as typeof import('@react-native-community/cli-server-api');\n\n  const { middleware, messageSocketEndpoint, eventsSocketEndpoint, websocketEndpoints } =\n    createDevServerMiddleware({\n      port: metroConfig.server.port,\n      watchFolders: metroConfig.watchFolders,\n    });\n\n  // The `securityHeadersMiddleware` does not support cross-origin requests, we replace with the enhanced version.\n  replaceMiddlewareWith(\n    middleware as ConnectServer,\n    securityHeadersMiddleware,\n    createCorsMiddleware(exp)\n  );\n\n  prependMiddleware(middleware, suppressRemoteDebuggingErrorMiddleware);\n\n  // TODO: We can probably drop this now.\n  const customEnhanceMiddleware = metroConfig.server.enhanceMiddleware;\n  // @ts-expect-error: can't mutate readonly config\n  metroConfig.server.enhanceMiddleware = (metroMiddleware: any, server: Metro.Server) => {\n    if (customEnhanceMiddleware) {\n      metroMiddleware = customEnhanceMiddleware(metroMiddleware, server);\n    }\n    return middleware.use(metroMiddleware);\n  };\n\n  middleware.use(createDebuggerTelemetryMiddleware(projectRoot, exp));\n\n  // Initialize all React Native debug features\n  const { debugMiddleware, debugWebsocketEndpoints } = createDebugMiddleware(metroBundler);\n  prependMiddleware(middleware, debugMiddleware);\n  middleware.use('/_expo/debugger', createJsInspectorMiddleware());\n\n  const { server, metro } = await runServer(metroBundler, metroConfig, {\n    // @ts-expect-error: Inconsistent `websocketEndpoints` type between metro and @react-native-community/cli-server-api\n    websocketEndpoints: {\n      ...websocketEndpoints,\n      ...debugWebsocketEndpoints,\n    },\n    watch: !isExporting && isWatchEnabled(),\n  });\n\n  prependMiddleware(middleware, (req: ServerRequest, res: ServerResponse, next: ServerNext) => {\n    // If the URL is a Metro asset request, then we need to skip all other middleware to prevent\n    // the community CLI's serve-static from hosting `/assets/index.html` in place of all assets if it exists.\n    // /assets/?unstable_path=.\n    if (req.url) {\n      const url = new URL(req.url!, 'http://localhost:8000');\n      if (url.pathname.match(/^\\/assets\\/?/) && url.searchParams.get('unstable_path') != null) {\n        return metro.processRequest(req, res, next);\n      }\n    }\n    return next();\n  });\n\n  setEventReporter(eventsSocketEndpoint.reportEvent);\n\n  return {\n    metro,\n    server,\n    middleware,\n    messageSocket: messageSocketEndpoint,\n  };\n}\n\n/**\n * Simplify and communicate if Metro is running without watching file updates,.\n * Exposed for testing.\n */\nexport function isWatchEnabled() {\n  if (env.CI) {\n    Log.log(\n      chalk`Metro is running in CI mode, reloads are disabled. Remove {bold CI=true} to enable watch mode.`\n    );\n  }\n\n  return !env.CI;\n}\n"], "names": ["loadMetroConfigAsync", "instantiateMetroAsync", "isWatchEnabled", "gteSdkVersion", "exp", "sdkVersion", "semver", "gte", "Error", "projectRoot", "options", "getConfig", "skipSDKVersionRequirement", "isExporting", "reportEvent", "serverRoot", "getMetroServerRoot", "terminal", "Terminal", "process", "stdout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MetroTerminalReporter", "hasConfig", "resolveConfig", "config", "loadConfig", "cwd", "isEmpty", "getDefaultConfig", "undefined", "reporter", "update", "event", "transformer", "publicPath", "experiments", "baseUrl", "platformBundlers", "getPlatformBundlers", "withMetroMultiPlatformAsync", "isTsconfigPathsEnabled", "tsconfigPaths", "webOutput", "web", "output", "isFastResolverEnabled", "env", "EXPO_USE_FAST_RESOLVER", "NODE_ENV", "logEventAsync", "getMetroProperties", "setEventReporter", "logger", "metroBundler", "metroConfig", "createDevServerMiddleware", "securityHeadersMiddleware", "require", "middleware", "messageSocketEndpoint", "eventsSocketEndpoint", "websocketEndpoints", "port", "server", "watchFolders", "replaceMiddlewareWith", "createCorsMiddleware", "prependMiddleware", "suppressRemoteDebuggingErrorMiddleware", "customEnhanceMiddleware", "enhanceMiddleware", "metroMiddleware", "use", "createDebuggerTelemetryMiddleware", "debugMiddleware", "debugWebsocketEndpoints", "createDebugMiddleware", "createJsInspectorMiddleware", "metro", "runServer", "watch", "req", "res", "next", "url", "URL", "pathname", "match", "searchParams", "get", "processRequest", "messageSocket", "CI", "Log", "log", "chalk"], "mappings": "AAAA;;;;QAmDsBA,oBAAoB,GAApBA,oBAAoB;QA8EpBC,qBAAqB,GAArBA,qBAAqB;QA8F3BC,cAAc,GAAdA,cAAc;AA/NQ,IAAA,OAAc,WAAd,cAAc,CAAA;AACN,IAAA,YAAoB,WAApB,oBAAoB,CAAA;AAChD,IAAA,MAAO,kCAAP,OAAO,EAAA;AAI0B,IAAA,aAAc,WAAd,cAAc,CAAA;AACxC,IAAA,UAAY,WAAZ,YAAY,CAAA;AAClB,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACP,IAAA,IAAK,WAAL,KAAK,CAAA;AAGa,IAAA,sBAAyB,WAAzB,yBAAyB,CAAA;AACzB,IAAA,sBAAmC,WAAnC,mCAAmC,CAAA;AAC/C,IAAA,cAAkB,WAAlB,kBAAkB,CAAA;AACA,IAAA,uBAA0B,WAA1B,0BAA0B,CAAA;AAElD,IAAA,IAAc,WAAd,cAAc,CAAA;AACC,IAAA,mBAA6C,WAA7C,6CAA6C,CAAA;AAC9B,IAAA,wBAAkD,WAAlD,kDAAkD,CAAA;AACtE,IAAA,kBAA4C,WAA5C,4CAA4C,CAAA;AACtD,IAAA,IAAoB,WAApB,oBAAoB,CAAA;AACH,IAAA,eAA8B,WAA9B,8BAA8B,CAAA;AAChC,IAAA,mBAAkC,WAAlC,kCAAkC,CAAA;AACzB,IAAA,4BAAqD,WAArD,qDAAqD,CAAA;AACxC,IAAA,UAAyB,WAAzB,yBAAyB,CAAA;AAE3B,IAAA,wBAAuC,WAAvC,uCAAuC,CAAA;AAC1D,IAAA,iBAAqB,WAArB,qBAAqB,CAAA;;;;;;AAOzD,SAASC,aAAa,CAACC,GAAmC,EAAEC,UAAkB,EAAW;IACvF,IAAI,CAACD,GAAG,CAACC,UAAU,EAAE;QACnB,OAAO,KAAK,CAAC;KACd;IAED,IAAID,GAAG,CAACC,UAAU,KAAK,aAAa,EAAE;QACpC,OAAO,IAAI,CAAC;KACb;IAED,IAAI;QACF,OAAOC,OAAM,QAAA,CAACC,GAAG,CAACH,GAAG,CAACC,UAAU,EAAEA,UAAU,CAAC,CAAC;KAC/C,CAAC,OAAM;QACN,MAAM,IAAIG,KAAK,CAAC,CAAC,EAAEJ,GAAG,CAACC,UAAU,CAAC,qDAAqD,CAAC,CAAC,CAAC;KAC3F;CACF;AAEM,eAAeL,oBAAoB,CACxCS,WAAmB,EACnBC,OAAoB,EACpB,EACEN,GAAG,EAAGO,CAAAA,GAAAA,OAAS,AAAkD,CAAA,UAAlD,CAACF,WAAW,EAAE;IAAEG,yBAAyB,EAAE,IAAI;CAAE,CAAC,CAACR,GAAG,CAAA,EACrES,WAAW,CAAA,EACgC,EAC7C;QAqD0BT,GAAe,EAC5BA,IAAO;IArDpB,IAAIU,WAAW,AAAoC,AAAC;IACpD,MAAMC,UAAU,GAAGC,CAAAA,GAAAA,mBAAkB,AAAa,CAAA,mBAAb,CAACP,WAAW,CAAC,AAAC;IAEnD,MAAMQ,QAAQ,GAAG,IAAIC,UAAQ,SAAA,CAACC,OAAO,CAACC,MAAM,CAAC,AAAC;IAC9C,MAAMC,gBAAgB,GAAG,IAAIC,sBAAqB,sBAAA,CAACP,UAAU,EAAEE,QAAQ,CAAC,AAAC;IAEzE,MAAMM,SAAS,GAAG,MAAMC,CAAAA,GAAAA,aAAa,AAA6B,CAAA,cAA7B,CAACd,OAAO,CAACe,MAAM,EAAEhB,WAAW,CAAC,AAAC;IACnE,IAAIgB,MAAM,GAAY;QACpB,GAAI,MAAMC,CAAAA,GAAAA,aAAU,AAInB,CAAA,WAJmB,CAClB;YAAEC,GAAG,EAAElB,WAAW;YAAEA,WAAW;YAAE,GAAGC,OAAO;SAAE,EAC7C,kFAAkF;QAClFa,SAAS,CAACK,OAAO,GAAGC,CAAAA,GAAAA,YAAgB,AAAa,CAAA,iBAAb,CAACpB,WAAW,CAAC,GAAGqB,SAAS,CAC9D;QACDC,QAAQ,EAAE;YACRC,MAAM,EAACC,KAAU,EAAE;gBACjBZ,gBAAgB,CAACW,MAAM,CAACC,KAAK,CAAC,CAAC;gBAC/B,IAAInB,WAAW,EAAE;oBACfA,WAAW,CAACmB,KAAK,CAAC,CAAC;iBACpB;aACF;SACF;KACF,AAAC;IAEF,IACE,0DAA0D;IAC1D,CAAC7B,GAAG,CAACC,UAAU,IACfF,aAAa,CAACC,GAAG,EAAE,QAAQ,CAAC,EAC5B;QACA,IAAIS,WAAW,EAAE;gBAIZT,IAAe;gBAAfA,IAAwB;YAH3B,iGAAiG;YACjG,uCAAuC;YACvCqB,MAAM,CAACS,WAAW,CAACC,UAAU,GAAG,CAAC,oBAAoB,EACnD,CAAC/B,CAAAA,IAAwB,GAAxBA,CAAAA,IAAe,GAAfA,GAAG,CAACgC,WAAW,SAAS,GAAxBhC,KAAAA,CAAwB,GAAxBA,IAAe,CAAEiC,OAAO,YAAxBjC,IAAwB,GAAI,EAAE,CAAC,GAAG,SAAS,CAC7C,CAAC,CAAC;SACJ,MAAM;YACL,sCAAsC;YACtCqB,MAAM,CAACS,WAAW,CAACC,UAAU,GAAG,0BAA0B,CAAC;SAC5D;KACF,MAAM;YACc/B,IAAe;QAAlC,IAAIS,WAAW,IAAIT,CAAAA,CAAAA,IAAe,GAAfA,GAAG,CAACgC,WAAW,SAAS,GAAxBhC,KAAAA,CAAwB,GAAxBA,IAAe,CAAEiC,OAAO,CAAA,EAAE;gBAGXjC,IAAe;YAF/C,iGAAiG;YACjG,uCAAuC;YACvCqB,MAAM,CAACS,WAAW,CAACC,UAAU,GAAG/B,CAAAA,IAAe,GAAfA,GAAG,CAACgC,WAAW,SAAS,GAAxBhC,KAAAA,CAAwB,GAAxBA,IAAe,CAAEiC,OAAO,CAAC;SAC1D;KACF;IAED,MAAMC,gBAAgB,GAAGC,CAAAA,GAAAA,iBAAmB,AAAkB,CAAA,oBAAlB,CAAC9B,WAAW,EAAEL,GAAG,CAAC,AAAC;QAMrCA,IAA8B,EAC3CA,IAAe;IAL5BqB,MAAM,GAAG,MAAMe,CAAAA,GAAAA,uBAA2B,AAQxC,CAAA,4BARwC,CAAC/B,WAAW,EAAE;QACtDgB,MAAM;QACNrB,GAAG;QACHkC,gBAAgB;QAChBG,sBAAsB,EAAErC,CAAAA,IAA8B,GAA9BA,CAAAA,GAAe,GAAfA,GAAG,CAACgC,WAAW,SAAe,GAA9BhC,KAAAA,CAA8B,GAA9BA,GAAe,CAAEsC,aAAa,YAA9BtC,IAA8B,GAAI,IAAI;QAC9DuC,SAAS,EAAEvC,CAAAA,IAAe,GAAfA,CAAAA,IAAO,GAAPA,GAAG,CAACwC,GAAG,SAAQ,GAAfxC,KAAAA,CAAe,GAAfA,IAAO,CAAEyC,MAAM,YAAfzC,IAAe,GAAI,QAAQ;QACtC0C,qBAAqB,EAAEC,IAAG,IAAA,CAACC,sBAAsB;QACjDnC,WAAW;KACZ,CAAC,CAAC;IAEH,IAAIM,OAAO,CAAC4B,GAAG,CAACE,QAAQ,KAAK,MAAM,EAAE;QACnCC,CAAAA,GAAAA,kBAAa,AAA8D,CAAA,cAA9D,CAAC,cAAc,EAAEC,CAAAA,GAAAA,mBAAkB,AAA0B,CAAA,mBAA1B,CAAC1C,WAAW,EAAEL,GAAG,EAAEqB,MAAM,CAAC,CAAC,CAAC;KAC7E;IAED,OAAO;QACLA,MAAM;QACN2B,gBAAgB,EAAE,CAACC,MAA4B,GAAMvC,WAAW,GAAGuC,MAAM;QAAC;QAC1EtB,QAAQ,EAAEV,gBAAgB;KAC3B,CAAC;CACH;AAGM,eAAepB,qBAAqB,CACzCqD,YAAmC,EACnC5C,OAA8C,EAC9C,EAAEG,WAAW,CAAA,EAA4B,EAMxC;IACD,MAAMJ,WAAW,GAAG6C,YAAY,CAAC7C,WAAW,AAAC;IAE7C,4FAA4F;IAC5F,MAAM,EAAEL,GAAG,CAAA,EAAE,GAAGO,CAAAA,GAAAA,OAAS,AAEvB,CAAA,UAFuB,CAACF,WAAW,EAAE;QACrCG,yBAAyB,EAAE,IAAI;KAChC,CAAC,AAAC;IAEH,MAAM,EAAEa,MAAM,EAAE8B,WAAW,CAAA,EAAEH,gBAAgB,CAAA,EAAE,GAAG,MAAMpD,oBAAoB,CAC1ES,WAAW,EACXC,OAAO,EACP;QAAEN,GAAG;QAAES,WAAW;KAAE,CACrB,AAAC;IAEF,MAAM,EAAE2C,yBAAyB,CAAA,EAAEC,yBAAyB,CAAA,EAAE,GAC5DC,OAAO,CAAC,wCAAwC,CAAC,AAA2D,AAAC;IAE/G,MAAM,EAAEC,UAAU,CAAA,EAAEC,qBAAqB,CAAA,EAAEC,oBAAoB,CAAA,EAAEC,kBAAkB,CAAA,EAAE,GACnFN,yBAAyB,CAAC;QACxBO,IAAI,EAAER,WAAW,CAACS,MAAM,CAACD,IAAI;QAC7BE,YAAY,EAAEV,WAAW,CAACU,YAAY;KACvC,CAAC,AAAC;IAEL,gHAAgH;IAChHC,CAAAA,GAAAA,UAAqB,AAIpB,CAAA,sBAJoB,CACnBP,UAAU,EACVF,yBAAyB,EACzBU,CAAAA,GAAAA,eAAoB,AAAK,CAAA,qBAAL,CAAC/D,GAAG,CAAC,CAC1B,CAAC;IAEFgE,CAAAA,GAAAA,UAAiB,AAAoD,CAAA,kBAApD,CAACT,UAAU,EAAEU,wBAAsC,uCAAA,CAAC,CAAC;IAEtE,uCAAuC;IACvC,MAAMC,uBAAuB,GAAGf,WAAW,CAACS,MAAM,CAACO,iBAAiB,AAAC;IACrE,iDAAiD;IACjDhB,WAAW,CAACS,MAAM,CAACO,iBAAiB,GAAG,CAACC,eAAoB,EAAER,MAAoB,GAAK;QACrF,IAAIM,uBAAuB,EAAE;YAC3BE,eAAe,GAAGF,uBAAuB,CAACE,eAAe,EAAER,MAAM,CAAC,CAAC;SACpE;QACD,OAAOL,UAAU,CAACc,GAAG,CAACD,eAAe,CAAC,CAAC;KACxC,CAAC;IAEFb,UAAU,CAACc,GAAG,CAACC,CAAAA,GAAAA,wBAAiC,AAAkB,CAAA,kCAAlB,CAACjE,WAAW,EAAEL,GAAG,CAAC,CAAC,CAAC;IAEpE,6CAA6C;IAC7C,MAAM,EAAEuE,eAAe,CAAA,EAAEC,uBAAuB,CAAA,EAAE,GAAGC,CAAAA,GAAAA,sBAAqB,AAAc,CAAA,sBAAd,CAACvB,YAAY,CAAC,AAAC;IACzFc,CAAAA,GAAAA,UAAiB,AAA6B,CAAA,kBAA7B,CAACT,UAAU,EAAEgB,eAAe,CAAC,CAAC;IAC/ChB,UAAU,CAACc,GAAG,CAAC,iBAAiB,EAAEK,CAAAA,GAAAA,4BAA2B,AAAE,CAAA,4BAAF,EAAE,CAAC,CAAC;IAEjE,MAAM,EAAEd,MAAM,EAANA,OAAM,CAAA,EAAEe,KAAK,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,cAAS,AAOvC,CAAA,UAPuC,CAAC1B,YAAY,EAAEC,WAAW,EAAE;QACnE,oHAAoH;QACpHO,kBAAkB,EAAE;YAClB,GAAGA,kBAAkB;YACrB,GAAGc,uBAAuB;SAC3B;QACDK,KAAK,EAAE,CAACpE,WAAW,IAAIX,cAAc,EAAE;KACxC,CAAC,AAAC;IAEHkE,CAAAA,GAAAA,UAAiB,AAWf,CAAA,kBAXe,CAACT,UAAU,EAAE,CAACuB,GAAkB,EAAEC,GAAmB,EAAEC,IAAgB,GAAK;QAC3F,4FAA4F;QAC5F,0GAA0G;QAC1G,2BAA2B;QAC3B,IAAIF,GAAG,CAACG,GAAG,EAAE;YACX,MAAMA,GAAG,GAAG,IAAIC,IAAG,IAAA,CAACJ,GAAG,CAACG,GAAG,EAAG,uBAAuB,CAAC,AAAC;YACvD,IAAIA,GAAG,CAACE,QAAQ,CAACC,KAAK,gBAAgB,IAAIH,GAAG,CAACI,YAAY,CAACC,GAAG,CAAC,eAAe,CAAC,IAAI,IAAI,EAAE;gBACvF,OAAOX,KAAK,CAACY,cAAc,CAACT,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC;aAC7C;SACF;QACD,OAAOA,IAAI,EAAE,CAAC;KACf,CAAC,CAAC;IAEHhC,gBAAgB,CAACS,oBAAoB,CAAC/C,WAAW,CAAC,CAAC;IAEnD,OAAO;QACLiE,KAAK;QACLf,MAAM,EAANA,OAAM;QACNL,UAAU;QACViC,aAAa,EAAEhC,qBAAqB;KACrC,CAAC;CACH;AAMM,SAAS1D,cAAc,GAAG;IAC/B,IAAI6C,IAAG,IAAA,CAAC8C,EAAE,EAAE;QACVC,IAAG,IAAA,CAACC,GAAG,CACLC,MAAK,QAAA,CAAC,8FAA8F,CAAC,CACtG,CAAC;KACH;IAED,OAAO,CAACjD,IAAG,IAAA,CAAC8C,EAAE,CAAC;CAChB"}