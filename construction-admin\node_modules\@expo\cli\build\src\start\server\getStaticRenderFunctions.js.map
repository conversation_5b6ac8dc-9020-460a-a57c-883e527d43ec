{"version": 3, "sources": ["../../../../src/start/server/getStaticRenderFunctions.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport fs from 'fs';\nimport fetch from 'node-fetch';\nimport path from 'path';\nimport requireString from 'require-from-string';\nimport resolveFrom from 'resolve-from';\n\nimport { logMetroError, logMetroErrorAsync } from './metro/metroErrorInterface';\nimport { getMetroServerRoot } from './middleware/ManifestMiddleware';\nimport { createBundleUrlPath } from './middleware/metroOptions';\nimport { augmentLogs } from './serverLogLikeMetro';\nimport { stripAnsi } from '../../utils/ansi';\nimport { delayAsync } from '../../utils/delay';\nimport { SilentError } from '../../utils/errors';\nimport { memoize } from '../../utils/fn';\nimport { profile } from '../../utils/profile';\n\ntype StaticRenderOptions = {\n  // Ensure the style format is `css-xxxx` (prod) instead of `css-view-xxxx` (dev)\n  dev?: boolean;\n  minify?: boolean;\n  platform?: string;\n  environment?: 'node';\n  engine?: 'hermes';\n  baseUrl: string;\n  routerRoot: string;\n};\n\nclass MetroNodeError extends Error {\n  constructor(\n    message: string,\n    public rawObject: any\n  ) {\n    super(message);\n  }\n}\n\nconst debug = require('debug')('expo:start:server:node-renderer') as typeof console.log;\n\nconst cachedSourceMaps: Map<string, { url: string; map: string }> = new Map();\n\n// Support unhandled rejections\nrequire('source-map-support').install({\n  retrieveSourceMap(source: string) {\n    if (cachedSourceMaps.has(source)) {\n      return cachedSourceMaps.get(source);\n    }\n    return null;\n  },\n});\n\nfunction wrapBundle(str: string) {\n  // Skip the metro runtime so debugging is a bit easier.\n  // Replace the __r() call with an export statement.\n  // Use gm to apply to the last require line. This is needed when the bundle has side-effects.\n  return str.replace(/^(__r\\(.*\\);)$/gm, 'module.exports = $1');\n}\n\n// TODO(EvanBacon): Group all the code together and version.\nconst getRenderModuleId = (projectRoot: string): string => {\n  const moduleId = resolveFrom.silent(projectRoot, 'expo-router/node/render.js');\n  if (!moduleId) {\n    throw new Error(\n      `A version of expo-router with Node.js support is not installed in the project.`\n    );\n  }\n\n  return moduleId;\n};\n\nconst moveStaticRenderFunction = memoize(async (projectRoot: string, requiredModuleId: string) => {\n  // Copy the file into the project to ensure it works in monorepos.\n  // This means the file cannot have any relative imports.\n  const tempDir = path.join(projectRoot, '.expo/static');\n  await fs.promises.mkdir(tempDir, { recursive: true });\n  const moduleId = path.join(tempDir, 'render.js');\n  await fs.promises.writeFile(moduleId, await fs.promises.readFile(requiredModuleId, 'utf8'));\n  // Sleep to give watchman time to register the file.\n  await delayAsync(50);\n  return moduleId;\n});\n\n/** @returns the js file contents required to generate the static generation function. */\nasync function getStaticRenderFunctionsContentAsync(\n  projectRoot: string,\n  devServerUrl: string,\n  { dev = false, minify = false, environment, baseUrl, routerRoot }: StaticRenderOptions\n): Promise<{ src: string; filename: string }> {\n  const root = getMetroServerRoot(projectRoot);\n  const requiredModuleId = getRenderModuleId(root);\n  let moduleId = requiredModuleId;\n\n  // Cannot be accessed using Metro's server API, we need to move the file\n  // into the project root and try again.\n  if (path.relative(root, moduleId).startsWith('..')) {\n    moduleId = await moveStaticRenderFunction(projectRoot, requiredModuleId);\n  }\n\n  return requireFileContentsWithMetro(root, devServerUrl, moduleId, {\n    dev,\n    minify,\n    environment,\n    baseUrl,\n    routerRoot,\n  });\n}\n\nasync function ensureFileInRootDirectory(projectRoot: string, otherFile: string) {\n  // Cannot be accessed using Metro's server API, we need to move the file\n  // into the project root and try again.\n  if (!path.relative(projectRoot, otherFile).startsWith('..' + path.sep)) {\n    return otherFile;\n  }\n\n  // Copy the file into the project to ensure it works in monorepos.\n  // This means the file cannot have any relative imports.\n  const tempDir = path.join(projectRoot, '.expo/static-tmp');\n  await fs.promises.mkdir(tempDir, { recursive: true });\n  const moduleId = path.join(tempDir, path.basename(otherFile));\n  await fs.promises.writeFile(moduleId, await fs.promises.readFile(otherFile, 'utf8'));\n  // Sleep to give watchman time to register the file.\n  await delayAsync(50);\n  return moduleId;\n}\n\nexport async function createMetroEndpointAsync(\n  projectRoot: string,\n  devServerUrl: string,\n  absoluteFilePath: string,\n  {\n    dev = false,\n    platform = 'web',\n    minify = false,\n    environment,\n    engine = 'hermes',\n    baseUrl,\n    routerRoot,\n  }: StaticRenderOptions\n): Promise<string> {\n  const root = getMetroServerRoot(projectRoot);\n  const safeOtherFile = await ensureFileInRootDirectory(projectRoot, absoluteFilePath);\n  const serverPath = path.relative(root, safeOtherFile).replace(/\\.[jt]sx?$/, '');\n\n  const urlFragment = createBundleUrlPath({\n    platform,\n    mode: dev ? 'development' : 'production',\n    mainModuleName: serverPath,\n    engine,\n    environment,\n    lazy: false,\n    minify,\n    baseUrl,\n    isExporting: true,\n    asyncRoutes: false,\n    routerRoot,\n    inlineSourceMap: false,\n    bytecode: false,\n  });\n\n  let url: string;\n  if (devServerUrl) {\n    url = new URL(urlFragment.replace(/^\\//, ''), devServerUrl).toString();\n  } else {\n    url = '/' + urlFragment.replace(/^\\/+/, '');\n  }\n  debug('fetching from Metro:', root, serverPath, url);\n  return url;\n}\n\nexport async function requireFileContentsWithMetro(\n  projectRoot: string,\n  devServerUrl: string,\n  absoluteFilePath: string,\n  props: StaticRenderOptions\n): Promise<{ src: string; filename: string }> {\n  const url = await createMetroEndpointAsync(projectRoot, devServerUrl, absoluteFilePath, props);\n\n  const res = await fetch(url);\n\n  // TODO: Improve error handling\n  if (res.status === 500) {\n    const text = await res.text();\n    if (text.startsWith('{\"originModulePath\"') || text.startsWith('{\"type\":\"TransformError\"')) {\n      const errorObject = JSON.parse(text);\n\n      throw new MetroNodeError(stripAnsi(errorObject.message) ?? errorObject.message, errorObject);\n    }\n    throw new Error(`[${res.status}]: ${res.statusText}\\n${text}`);\n  }\n\n  if (!res.ok) {\n    throw new Error(`Error fetching bundle for static rendering: ${res.status} ${res.statusText}`);\n  }\n\n  const content = await res.text();\n\n  const map = await fetch(url.replace('.bundle?', '.map?')).then((r) => r.json());\n  cachedSourceMaps.set(url, { url: projectRoot, map });\n\n  return { src: wrapBundle(content), filename: url };\n}\n\nexport async function getStaticRenderFunctions(\n  projectRoot: string,\n  devServerUrl: string,\n  options: StaticRenderOptions\n): Promise<Record<string, (...args: any[]) => Promise<any>>> {\n  const { src: scriptContents, filename } = await getStaticRenderFunctionsContentAsync(\n    projectRoot,\n    devServerUrl,\n    options\n  );\n\n  return evalMetroAndWrapFunctions(projectRoot, scriptContents, filename);\n}\n\nfunction evalMetroAndWrapFunctions<T = Record<string, (...args: any[]) => Promise<any>>>(\n  projectRoot: string,\n  script: string,\n  filename: string\n): Promise<T> {\n  const contents = evalMetro(projectRoot, script, filename);\n\n  // wrap each function with a try/catch that uses Metro's error formatter\n  return Object.keys(contents).reduce((acc, key) => {\n    const fn = contents[key];\n    if (typeof fn !== 'function') {\n      return { ...acc, [key]: fn };\n    }\n\n    acc[key] = async function (...props: any[]) {\n      try {\n        return await fn.apply(this, props);\n      } catch (error: any) {\n        await logMetroError(projectRoot, { error });\n        throw new SilentError(error);\n      }\n    };\n    return acc;\n  }, {} as any);\n}\n\nfunction evalMetro(projectRoot: string, src: string, filename: string) {\n  augmentLogs(projectRoot);\n  try {\n    return profile(requireString, 'eval-metro-bundle')(src, filename);\n  } catch (error: any) {\n    // Format any errors that were thrown in the global scope of the evaluation.\n    if (error instanceof Error) {\n      logMetroErrorAsync({ projectRoot, error }).catch((internalError) => {\n        debug('Failed to log metro error:', internalError);\n        throw error;\n      });\n    } else {\n      throw error;\n    }\n  } finally {\n  }\n}\n"], "names": ["createMetroEndpointAsync", "requireFileContentsWithMetro", "getStaticRenderFunctions", "MetroNodeError", "Error", "constructor", "message", "rawObject", "debug", "require", "cachedSourceMaps", "Map", "install", "retrieveSourceMap", "source", "has", "get", "wrapBundle", "str", "replace", "getRenderModuleId", "projectRoot", "moduleId", "resolveFrom", "silent", "moveStaticRenderFunction", "memoize", "requiredModuleId", "tempDir", "path", "join", "fs", "promises", "mkdir", "recursive", "writeFile", "readFile", "delayAsync", "getStaticRenderFunctionsContentAsync", "devServerUrl", "dev", "minify", "environment", "baseUrl", "routerRoot", "root", "getMetroServerRoot", "relative", "startsWith", "ensureFileInRootDirectory", "otherFile", "sep", "basename", "absoluteFilePath", "platform", "engine", "safeOtherFile", "serverPath", "urlFragment", "createBundleUrlPath", "mode", "mainModuleName", "lazy", "isExporting", "asyncRoutes", "inlineSourceMap", "bytecode", "url", "URL", "toString", "props", "res", "fetch", "status", "text", "errorObject", "JSON", "parse", "stripAnsi", "statusText", "ok", "content", "map", "then", "r", "json", "set", "src", "filename", "options", "scriptContents", "evalMetroAndWrapFunctions", "script", "contents", "evalMetro", "Object", "keys", "reduce", "acc", "key", "fn", "apply", "error", "logMetroError", "SilentError", "augmentLogs", "profile", "requireString", "logMetroErrorAsync", "catch", "internalError"], "mappings": "AAMA;;;;QA4HsBA,wBAAwB,GAAxBA,wBAAwB;QA4CxBC,4BAA4B,GAA5BA,4BAA4B;QAiC5BC,wBAAwB,GAAxBA,wBAAwB;AAzM/B,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACD,IAAA,UAAY,kCAAZ,YAAY,EAAA;AACb,IAAA,KAAM,kCAAN,MAAM,EAAA;AACG,IAAA,kBAAqB,kCAArB,qBAAqB,EAAA;AACvB,IAAA,YAAc,kCAAd,cAAc,EAAA;AAEY,IAAA,oBAA6B,WAA7B,6BAA6B,CAAA;AAC5C,IAAA,mBAAiC,WAAjC,iCAAiC,CAAA;AAChC,IAAA,aAA2B,WAA3B,2BAA2B,CAAA;AACnC,IAAA,mBAAsB,WAAtB,sBAAsB,CAAA;AACxB,IAAA,KAAkB,WAAlB,kBAAkB,CAAA;AACjB,IAAA,MAAmB,WAAnB,mBAAmB,CAAA;AAClB,IAAA,OAAoB,WAApB,oBAAoB,CAAA;AACxB,IAAA,GAAgB,WAAhB,gBAAgB,CAAA;AAChB,IAAA,QAAqB,WAArB,qBAAqB,CAAA;;;;;;AAa7C,MAAMC,cAAc,SAASC,KAAK;IAChCC,YACEC,OAAe,EACRC,SAAc,CACrB;QACA,KAAK,CAACD,OAAO,CAAC,CAAC;aAFRC,SAAc,GAAdA,SAAc;KAGtB;CACF;AAED,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,iCAAiC,CAAC,AAAsB,AAAC;AAExF,MAAMC,gBAAgB,GAA8C,IAAIC,GAAG,EAAE,AAAC;AAE9E,+BAA+B;AAC/BF,OAAO,CAAC,oBAAoB,CAAC,CAACG,OAAO,CAAC;IACpCC,iBAAiB,EAACC,MAAc,EAAE;QAChC,IAAIJ,gBAAgB,CAACK,GAAG,CAACD,MAAM,CAAC,EAAE;YAChC,OAAOJ,gBAAgB,CAACM,GAAG,CAACF,MAAM,CAAC,CAAC;SACrC;QACD,OAAO,IAAI,CAAC;KACb;CACF,CAAC,CAAC;AAEH,SAASG,UAAU,CAACC,GAAW,EAAE;IAC/B,uDAAuD;IACvD,mDAAmD;IACnD,6FAA6F;IAC7F,OAAOA,GAAG,CAACC,OAAO,qBAAqB,qBAAqB,CAAC,CAAC;CAC/D;AAED,4DAA4D;AAC5D,MAAMC,iBAAiB,GAAG,CAACC,WAAmB,GAAa;IACzD,MAAMC,QAAQ,GAAGC,YAAW,QAAA,CAACC,MAAM,CAACH,WAAW,EAAE,4BAA4B,CAAC,AAAC;IAC/E,IAAI,CAACC,QAAQ,EAAE;QACb,MAAM,IAAIlB,KAAK,CACb,CAAC,8EAA8E,CAAC,CACjF,CAAC;KACH;IAED,OAAOkB,QAAQ,CAAC;CACjB,AAAC;AAEF,MAAMG,wBAAwB,GAAGC,CAAAA,GAAAA,GAAO,AAUtC,CAAA,QAVsC,CAAC,OAAOL,WAAmB,EAAEM,gBAAwB,GAAK;IAChG,kEAAkE;IAClE,wDAAwD;IACxD,MAAMC,OAAO,GAAGC,KAAI,QAAA,CAACC,IAAI,CAACT,WAAW,EAAE,cAAc,CAAC,AAAC;IACvD,MAAMU,GAAE,QAAA,CAACC,QAAQ,CAACC,KAAK,CAACL,OAAO,EAAE;QAAEM,SAAS,EAAE,IAAI;KAAE,CAAC,CAAC;IACtD,MAAMZ,QAAQ,GAAGO,KAAI,QAAA,CAACC,IAAI,CAACF,OAAO,EAAE,WAAW,CAAC,AAAC;IACjD,MAAMG,GAAE,QAAA,CAACC,QAAQ,CAACG,SAAS,CAACb,QAAQ,EAAE,MAAMS,GAAE,QAAA,CAACC,QAAQ,CAACI,QAAQ,CAACT,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC;IAC5F,oDAAoD;IACpD,MAAMU,CAAAA,GAAAA,MAAU,AAAI,CAAA,WAAJ,CAAC,EAAE,CAAC,CAAC;IACrB,OAAOf,QAAQ,CAAC;CACjB,CAAC,AAAC;AAEH,yFAAyF,CACzF,eAAegB,oCAAoC,CACjDjB,WAAmB,EACnBkB,YAAoB,EACpB,EAAEC,GAAG,EAAG,KAAK,CAAA,EAAEC,MAAM,EAAG,KAAK,CAAA,EAAEC,WAAW,CAAA,EAAEC,OAAO,CAAA,EAAEC,UAAU,CAAA,EAAuB,EAC1C;IAC5C,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,mBAAkB,AAAa,CAAA,mBAAb,CAACzB,WAAW,CAAC,AAAC;IAC7C,MAAMM,gBAAgB,GAAGP,iBAAiB,CAACyB,IAAI,CAAC,AAAC;IACjD,IAAIvB,QAAQ,GAAGK,gBAAgB,AAAC;IAEhC,wEAAwE;IACxE,uCAAuC;IACvC,IAAIE,KAAI,QAAA,CAACkB,QAAQ,CAACF,IAAI,EAAEvB,QAAQ,CAAC,CAAC0B,UAAU,CAAC,IAAI,CAAC,EAAE;QAClD1B,QAAQ,GAAG,MAAMG,wBAAwB,CAACJ,WAAW,EAAEM,gBAAgB,CAAC,CAAC;KAC1E;IAED,OAAO1B,4BAA4B,CAAC4C,IAAI,EAAEN,YAAY,EAAEjB,QAAQ,EAAE;QAChEkB,GAAG;QACHC,MAAM;QACNC,WAAW;QACXC,OAAO;QACPC,UAAU;KACX,CAAC,CAAC;CACJ;AAED,eAAeK,yBAAyB,CAAC5B,WAAmB,EAAE6B,SAAiB,EAAE;IAC/E,wEAAwE;IACxE,uCAAuC;IACvC,IAAI,CAACrB,KAAI,QAAA,CAACkB,QAAQ,CAAC1B,WAAW,EAAE6B,SAAS,CAAC,CAACF,UAAU,CAAC,IAAI,GAAGnB,KAAI,QAAA,CAACsB,GAAG,CAAC,EAAE;QACtE,OAAOD,SAAS,CAAC;KAClB;IAED,kEAAkE;IAClE,wDAAwD;IACxD,MAAMtB,OAAO,GAAGC,KAAI,QAAA,CAACC,IAAI,CAACT,WAAW,EAAE,kBAAkB,CAAC,AAAC;IAC3D,MAAMU,GAAE,QAAA,CAACC,QAAQ,CAACC,KAAK,CAACL,OAAO,EAAE;QAAEM,SAAS,EAAE,IAAI;KAAE,CAAC,CAAC;IACtD,MAAMZ,QAAQ,GAAGO,KAAI,QAAA,CAACC,IAAI,CAACF,OAAO,EAAEC,KAAI,QAAA,CAACuB,QAAQ,CAACF,SAAS,CAAC,CAAC,AAAC;IAC9D,MAAMnB,GAAE,QAAA,CAACC,QAAQ,CAACG,SAAS,CAACb,QAAQ,EAAE,MAAMS,GAAE,QAAA,CAACC,QAAQ,CAACI,QAAQ,CAACc,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;IACrF,oDAAoD;IACpD,MAAMb,CAAAA,GAAAA,MAAU,AAAI,CAAA,WAAJ,CAAC,EAAE,CAAC,CAAC;IACrB,OAAOf,QAAQ,CAAC;CACjB;AAEM,eAAetB,wBAAwB,CAC5CqB,WAAmB,EACnBkB,YAAoB,EACpBc,gBAAwB,EACxB,EACEb,GAAG,EAAG,KAAK,CAAA,EACXc,QAAQ,EAAG,KAAK,CAAA,EAChBb,MAAM,EAAG,KAAK,CAAA,EACdC,WAAW,CAAA,EACXa,MAAM,EAAG,QAAQ,CAAA,EACjBZ,OAAO,CAAA,EACPC,UAAU,CAAA,EACU,EACL;IACjB,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,mBAAkB,AAAa,CAAA,mBAAb,CAACzB,WAAW,CAAC,AAAC;IAC7C,MAAMmC,aAAa,GAAG,MAAMP,yBAAyB,CAAC5B,WAAW,EAAEgC,gBAAgB,CAAC,AAAC;IACrF,MAAMI,UAAU,GAAG5B,KAAI,QAAA,CAACkB,QAAQ,CAACF,IAAI,EAAEW,aAAa,CAAC,CAACrC,OAAO,eAAe,EAAE,CAAC,AAAC;IAEhF,MAAMuC,WAAW,GAAGC,CAAAA,GAAAA,aAAmB,AAcrC,CAAA,oBAdqC,CAAC;QACtCL,QAAQ;QACRM,IAAI,EAAEpB,GAAG,GAAG,aAAa,GAAG,YAAY;QACxCqB,cAAc,EAAEJ,UAAU;QAC1BF,MAAM;QACNb,WAAW;QACXoB,IAAI,EAAE,KAAK;QACXrB,MAAM;QACNE,OAAO;QACPoB,WAAW,EAAE,IAAI;QACjBC,WAAW,EAAE,KAAK;QAClBpB,UAAU;QACVqB,eAAe,EAAE,KAAK;QACtBC,QAAQ,EAAE,KAAK;KAChB,CAAC,AAAC;IAEH,IAAIC,GAAG,AAAQ,AAAC;IAChB,IAAI5B,YAAY,EAAE;QAChB4B,GAAG,GAAG,IAAIC,GAAG,CAACV,WAAW,CAACvC,OAAO,QAAQ,EAAE,CAAC,EAAEoB,YAAY,CAAC,CAAC8B,QAAQ,EAAE,CAAC;KACxE,MAAM;QACLF,GAAG,GAAG,GAAG,GAAGT,WAAW,CAACvC,OAAO,SAAS,EAAE,CAAC,CAAC;KAC7C;IACDX,KAAK,CAAC,sBAAsB,EAAEqC,IAAI,EAAEY,UAAU,EAAEU,GAAG,CAAC,CAAC;IACrD,OAAOA,GAAG,CAAC;CACZ;AAEM,eAAelE,4BAA4B,CAChDoB,WAAmB,EACnBkB,YAAoB,EACpBc,gBAAwB,EACxBiB,KAA0B,EACkB;IAC5C,MAAMH,GAAG,GAAG,MAAMnE,wBAAwB,CAACqB,WAAW,EAAEkB,YAAY,EAAEc,gBAAgB,EAAEiB,KAAK,CAAC,AAAC;IAE/F,MAAMC,GAAG,GAAG,MAAMC,CAAAA,GAAAA,UAAK,AAAK,CAAA,QAAL,CAACL,GAAG,CAAC,AAAC;IAE7B,+BAA+B;IAC/B,IAAII,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;QACtB,MAAMC,IAAI,GAAG,MAAMH,GAAG,CAACG,IAAI,EAAE,AAAC;QAC9B,IAAIA,IAAI,CAAC1B,UAAU,CAAC,qBAAqB,CAAC,IAAI0B,IAAI,CAAC1B,UAAU,CAAC,0BAA0B,CAAC,EAAE;YACzF,MAAM2B,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC,AAAC;gBAEZI,GAA8B;YAAvD,MAAM,IAAI3E,cAAc,CAAC2E,CAAAA,GAA8B,GAA9BA,CAAAA,GAAAA,KAAS,AAAqB,CAAA,UAArB,CAACH,WAAW,CAACrE,OAAO,CAAC,YAA9BwE,GAA8B,GAAIH,WAAW,CAACrE,OAAO,EAAEqE,WAAW,CAAC,CAAC;SAC9F;QACD,MAAM,IAAIvE,KAAK,CAAC,CAAC,CAAC,EAAEmE,GAAG,CAACE,MAAM,CAAC,GAAG,EAAEF,GAAG,CAACQ,UAAU,CAAC,EAAE,EAAEL,IAAI,CAAC,CAAC,CAAC,CAAC;KAChE;IAED,IAAI,CAACH,GAAG,CAACS,EAAE,EAAE;QACX,MAAM,IAAI5E,KAAK,CAAC,CAAC,4CAA4C,EAAEmE,GAAG,CAACE,MAAM,CAAC,CAAC,EAAEF,GAAG,CAACQ,UAAU,CAAC,CAAC,CAAC,CAAC;KAChG;IAED,MAAME,OAAO,GAAG,MAAMV,GAAG,CAACG,IAAI,EAAE,AAAC;IAEjC,MAAMQ,GAAG,GAAG,MAAMV,CAAAA,GAAAA,UAAK,AAAkC,CAAA,QAAlC,CAACL,GAAG,CAAChD,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAACgE,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACC,IAAI,EAAE;IAAA,CAAC,AAAC;IAChF3E,gBAAgB,CAAC4E,GAAG,CAACnB,GAAG,EAAE;QAAEA,GAAG,EAAE9C,WAAW;QAAE6D,GAAG;KAAE,CAAC,CAAC;IAErD,OAAO;QAAEK,GAAG,EAAEtE,UAAU,CAACgE,OAAO,CAAC;QAAEO,QAAQ,EAAErB,GAAG;KAAE,CAAC;CACpD;AAEM,eAAejE,wBAAwB,CAC5CmB,WAAmB,EACnBkB,YAAoB,EACpBkD,OAA4B,EAC+B;IAC3D,MAAM,EAAEF,GAAG,EAAEG,cAAc,CAAA,EAAEF,QAAQ,CAAA,EAAE,GAAG,MAAMlD,oCAAoC,CAClFjB,WAAW,EACXkB,YAAY,EACZkD,OAAO,CACR,AAAC;IAEF,OAAOE,yBAAyB,CAACtE,WAAW,EAAEqE,cAAc,EAAEF,QAAQ,CAAC,CAAC;CACzE;AAED,SAASG,yBAAyB,CAChCtE,WAAmB,EACnBuE,MAAc,EACdJ,QAAgB,EACJ;IACZ,MAAMK,QAAQ,GAAGC,SAAS,CAACzE,WAAW,EAAEuE,MAAM,EAAEJ,QAAQ,CAAC,AAAC;IAE1D,wEAAwE;IACxE,OAAOO,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,GAAK;QAChD,MAAMC,EAAE,GAAGP,QAAQ,CAACM,GAAG,CAAC,AAAC;QACzB,IAAI,OAAOC,EAAE,KAAK,UAAU,EAAE;YAC5B,OAAO;gBAAE,GAAGF,GAAG;gBAAE,CAACC,GAAG,CAAC,EAAEC,EAAE;aAAE,CAAC;SAC9B;QAEDF,GAAG,CAACC,GAAG,CAAC,GAAG,eAAgB,GAAG7B,KAAK,AAAO,EAAE;YAC1C,IAAI;gBACF,OAAO,MAAM8B,EAAE,CAACC,KAAK,CAAC,IAAI,EAAE/B,KAAK,CAAC,CAAC;aACpC,CAAC,OAAOgC,KAAK,EAAO;gBACnB,MAAMC,CAAAA,GAAAA,oBAAa,AAAwB,CAAA,cAAxB,CAAClF,WAAW,EAAE;oBAAEiF,KAAK;iBAAE,CAAC,CAAC;gBAC5C,MAAM,IAAIE,OAAW,YAAA,CAACF,KAAK,CAAC,CAAC;aAC9B;SACF,CAAC;QACF,OAAOJ,GAAG,CAAC;KACZ,EAAE,EAAE,CAAQ,CAAC;CACf;AAED,SAASJ,SAAS,CAACzE,WAAmB,EAAEkE,GAAW,EAAEC,QAAgB,EAAE;IACrEiB,CAAAA,GAAAA,mBAAW,AAAa,CAAA,YAAb,CAACpF,WAAW,CAAC,CAAC;IACzB,IAAI;QACF,OAAOqF,CAAAA,GAAAA,QAAO,AAAoC,CAAA,QAApC,CAACC,kBAAa,QAAA,EAAE,mBAAmB,CAAC,CAACpB,GAAG,EAAEC,QAAQ,CAAC,CAAC;KACnE,CAAC,OAAOc,KAAK,EAAO;QACnB,4EAA4E;QAC5E,IAAIA,KAAK,YAAYlG,KAAK,EAAE;YAC1BwG,CAAAA,GAAAA,oBAAkB,AAAwB,CAAA,mBAAxB,CAAC;gBAAEvF,WAAW;gBAAEiF,KAAK;aAAE,CAAC,CAACO,KAAK,CAAC,CAACC,aAAa,GAAK;gBAClEtG,KAAK,CAAC,4BAA4B,EAAEsG,aAAa,CAAC,CAAC;gBACnD,MAAMR,KAAK,CAAC;aACb,CAAC,CAAC;SACJ,MAAM;YACL,MAAMA,KAAK,CAAC;SACb;KACF,QAAS,EACT;CACF"}