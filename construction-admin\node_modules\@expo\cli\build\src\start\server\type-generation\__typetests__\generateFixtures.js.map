{"version": 3, "sources": ["../../../../../../src/start/server/type-generation/__typetests__/generateFixtures.ts"], "sourcesContent": ["import { writeFile } from 'fs/promises';\nimport { join } from 'path';\n\nimport { getTemplateString } from '../routes';\n\ntype Fixture = {\n  staticRoutes: string[];\n  dynamicRoutes: string[];\n  dynamicRouteTemplates: string[];\n};\n\nconst fixtures: Record<string, Fixture> = {\n  basic: {\n    staticRoutes: ['/apple', '/banana'],\n    dynamicRoutes: [\n      '/colors/${SingleRoutePart<T>}',\n      '/animals/${CatchAllRoutePart<T>}',\n      '/mix/${SingleRoutePart<T>}/${SingleRoutePart<T>}/${CatchAllRoutePart<T>}',\n    ],\n    dynamicRouteTemplates: [\n      '/colors/[color]',\n      '/animals/[...animal]',\n      '/mix/[fruit]/[color]/[...animals]',\n    ],\n  },\n};\n\nexport default async function () {\n  await Promise.all(\n    Object.entries(fixtures).map(async ([key, value]) => {\n      const template = getTemplateString(\n        new Set(value.staticRoutes),\n        new Set(value.dynamicRoutes),\n        new Set(value.dynamicRouteTemplates)\n      )\n        // The Template produces a global module .d.ts declaration\n        // These replacements turn it into a local module\n        .replaceAll(/^  /gm, '')\n        .replace(/declare module \"expo-router\" {/, '')\n        .replaceAll(/export function/g, 'export declare function')\n        .replaceAll(/export const/g, 'export declare const')\n        // Remove the last `}`\n        .slice(0, -2);\n\n      return writeFile(join(__dirname, './fixtures/', key + '.ts'), template);\n    })\n  );\n\n  console.log('done');\n}\n"], "names": ["fixtures", "basic", "staticRoutes", "dynamicRoutes", "dynamicRouteTemplates", "Promise", "all", "Object", "entries", "map", "key", "value", "template", "getTemplateString", "Set", "replaceAll", "replace", "slice", "writeFile", "join", "__dirname", "console", "log"], "mappings": "AAAA;;;;;AAA0B,IAAA,SAAa,WAAb,aAAa,CAAA;AAClB,IAAA,KAAM,WAAN,MAAM,CAAA;AAEO,IAAA,OAAW,WAAX,WAAW,CAAA;AAQ7C,MAAMA,QAAQ,GAA4B;IACxCC,KAAK,EAAE;QACLC,YAAY,EAAE;YAAC,QAAQ;YAAE,SAAS;SAAC;QACnCC,aAAa,EAAE;YACb,+BAA+B;YAC/B,kCAAkC;YAClC,0EAA0E;SAC3E;QACDC,qBAAqB,EAAE;YACrB,iBAAiB;YACjB,sBAAsB;YACtB,mCAAmC;SACpC;KACF;CACF,AAAC;AAEa,0BAAkB;IAC/B,MAAMC,OAAO,CAACC,GAAG,CACfC,MAAM,CAACC,OAAO,CAACR,QAAQ,CAAC,CAACS,GAAG,CAAC,OAAO,CAACC,GAAG,EAAEC,KAAK,CAAC,GAAK;QACnD,MAAMC,QAAQ,GAAGC,CAAAA,GAAAA,OAAiB,AAIjC,CAAA,kBAJiC,CAChC,IAAIC,GAAG,CAACH,KAAK,CAACT,YAAY,CAAC,EAC3B,IAAIY,GAAG,CAACH,KAAK,CAACR,aAAa,CAAC,EAC5B,IAAIW,GAAG,CAACH,KAAK,CAACP,qBAAqB,CAAC,CACrC,AACC,0DAA0D;QAC1D,iDAAiD;SAChDW,UAAU,UAAU,EAAE,CAAC,CACvBC,OAAO,mCAAmC,EAAE,CAAC,CAC7CD,UAAU,qBAAqB,yBAAyB,CAAC,CACzDA,UAAU,kBAAkB,sBAAsB,CAAC,AACpD,sBAAsB;SACrBE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,AAAC;QAEhB,OAAOC,CAAAA,GAAAA,SAAS,AAAuD,CAAA,UAAvD,CAACC,CAAAA,GAAAA,KAAI,AAAuC,CAAA,KAAvC,CAACC,SAAS,EAAE,aAAa,EAAEV,GAAG,GAAG,KAAK,CAAC,EAAEE,QAAQ,CAAC,CAAC;KACzE,CAAC,CACH,CAAC;IAEFS,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC,CAAC;CACrB"}