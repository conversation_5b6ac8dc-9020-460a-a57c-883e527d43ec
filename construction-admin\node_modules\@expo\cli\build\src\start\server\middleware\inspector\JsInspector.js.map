{"version": 3, "sources": ["../../../../../../src/start/server/middleware/inspector/JsInspector.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport fetch from 'node-fetch';\n\nimport { launchInspectorBrowserAsync, type LaunchBrowserInstance } from './LaunchBrowser';\nimport { Log } from '../../../../log';\nimport { env } from '../../../../utils/env';\n\nexport interface MetroInspectorProxyApp {\n  id: string;\n  description: string;\n  title: string;\n  faviconUrl: string;\n  devtoolsFrontendUrl: string;\n  type: 'node';\n  webSocketDebuggerUrl: string;\n  vm: 'Hermes' | \"don't use\";\n  /** Added since React Native 0.73.x */\n  deviceName?: string;\n}\n\nlet openingBrowserInstance: LaunchBrowserInstance | null = null;\n\nexport function openJsInspector(metroBaseUrl: string, app: MetroInspectorProxyApp) {\n  if (env.EXPO_USE_UNSTABLE_DEBUGGER) {\n    return openExperimentalJsInspector(metroBaseUrl, app);\n  } else {\n    return openClassicJsInspector(app);\n  }\n}\n\nasync function openExperimentalJsInspector(metroBaseUrl: string, app: MetroInspectorProxyApp) {\n  const device = encodeURIComponent(app.id);\n  const appId = encodeURIComponent(app.description);\n  await fetch(`${metroBaseUrl}/open-debugger?device=${device}&appId=${appId}`, { method: 'POST' });\n}\n\n/**\n * Chrome DevTools UI implemented for SDK <49.\n * TODO(cedric): Remove this when we fully swap over to the new React Native JS Inspector.\n */\nasync function openClassicJsInspector(app: MetroInspectorProxyApp) {\n  Log.log(chalk`{bold Debug:} Opening JavaScript inspector in the browser...`);\n\n  // To update devtoolsFrontendRev, find the full commit hash in the url:\n  // https://chromium.googlesource.com/chromium/src.git/+log/refs/tags/{CHROME_VERSION}/chrome/VERSION\n  //\n  // 1. Replace {CHROME_VERSION} with the target chrome version\n  // 2. Click the first log item in the webpage\n  // 3. The full commit hash is the desired revision\n  const devtoolsFrontendRev = 'd9568d04d7dd79269c5a655d7ada69650c5a8336'; // Chrome 100.0.4896.75\n\n  const urlBase = `https://chrome-devtools-frontend.appspot.com/serve_rev/@${devtoolsFrontendRev}/devtools_app.html`;\n  const ws = app.webSocketDebuggerUrl.replace(/^ws:\\/\\//, '');\n  const url = `${urlBase}?panel=console&ws=${encodeURIComponent(ws)}`;\n  await closeJsInspector();\n  openingBrowserInstance = await launchInspectorBrowserAsync(url);\n}\n\nexport async function closeJsInspector() {\n  await openingBrowserInstance?.close();\n  openingBrowserInstance = null;\n}\n\nexport async function queryInspectorAppAsync(\n  metroServerOrigin: string,\n  appId: string\n): Promise<MetroInspectorProxyApp | null> {\n  const apps = await queryAllInspectorAppsAsync(metroServerOrigin);\n  return apps.find((app) => app.description === appId) ?? null;\n}\n\nexport async function queryAllInspectorAppsAsync(\n  metroServerOrigin: string\n): Promise<MetroInspectorProxyApp[]> {\n  const resp = await fetch(`${metroServerOrigin}/json/list`);\n  const apps: MetroInspectorProxyApp[] = transformApps(await resp.json());\n  // Only use targets with better reloading support\n  return apps.filter((app) => app.title === 'React Native Experimental (Improved Chrome Reloads)');\n}\n\n// The description of `React Native Experimental (Improved Chrome Reloads)` target is `don't use` from metro.\n// This function tries to transform the unmeaningful description to appId\nfunction transformApps(apps: MetroInspectorProxyApp[]): MetroInspectorProxyApp[] {\n  const deviceIdToAppId: Record<string, string> = {};\n\n  for (const app of apps) {\n    if (app.description !== \"don't use\") {\n      const deviceId = app.id.split('-')[0];\n      const appId = app.description;\n      deviceIdToAppId[deviceId] = appId;\n    }\n  }\n\n  return apps.map((app) => {\n    if (app.description === \"don't use\") {\n      const deviceId = app.id.split('-')[0];\n      app.description = deviceIdToAppId[deviceId] ?? app.description;\n    }\n    return app;\n  });\n}\n"], "names": ["openJsInspector", "closeJsInspector", "queryInspectorAppAsync", "queryAllInspectorAppsAsync", "openingBrowserInstance", "metroBaseUrl", "app", "env", "EXPO_USE_UNSTABLE_DEBUGGER", "openExperimentalJsInspector", "openClassicJsInspector", "device", "encodeURIComponent", "id", "appId", "description", "fetch", "method", "Log", "log", "chalk", "devtoolsFrontendRev", "urlBase", "ws", "webSocketDebuggerUrl", "replace", "url", "launchInspectorBrowserAsync", "close", "metroServerOrigin", "apps", "find", "resp", "transformApps", "json", "filter", "title", "deviceIdToAppId", "deviceId", "split", "map"], "mappings": "AAAA;;;;QAsBgBA,eAAe,GAAfA,eAAe;QAoCTC,gBAAgB,GAAhBA,gBAAgB;QAKhBC,sBAAsB,GAAtBA,sBAAsB;QAQtBC,0BAA0B,GAA1BA,0BAA0B;AAvE9B,IAAA,MAAO,kCAAP,OAAO,EAAA;AACP,IAAA,UAAY,kCAAZ,YAAY,EAAA;AAE0C,IAAA,cAAiB,WAAjB,iBAAiB,CAAA;AACrE,IAAA,IAAiB,WAAjB,iBAAiB,CAAA;AACjB,IAAA,IAAuB,WAAvB,uBAAuB,CAAA;;;;;;AAe3C,IAAIC,sBAAsB,GAAiC,IAAI,AAAC;AAEzD,SAASJ,eAAe,CAACK,YAAoB,EAAEC,GAA2B,EAAE;IACjF,IAAIC,IAAG,IAAA,CAACC,0BAA0B,EAAE;QAClC,OAAOC,2BAA2B,CAACJ,YAAY,EAAEC,GAAG,CAAC,CAAC;KACvD,MAAM;QACL,OAAOI,sBAAsB,CAACJ,GAAG,CAAC,CAAC;KACpC;CACF;AAED,eAAeG,2BAA2B,CAACJ,YAAoB,EAAEC,GAA2B,EAAE;IAC5F,MAAMK,MAAM,GAAGC,kBAAkB,CAACN,GAAG,CAACO,EAAE,CAAC,AAAC;IAC1C,MAAMC,KAAK,GAAGF,kBAAkB,CAACN,GAAG,CAACS,WAAW,CAAC,AAAC;IAClD,MAAMC,CAAAA,GAAAA,UAAK,AAAqF,CAAA,QAArF,CAAC,CAAC,EAAEX,YAAY,CAAC,sBAAsB,EAAEM,MAAM,CAAC,OAAO,EAAEG,KAAK,CAAC,CAAC,EAAE;QAAEG,MAAM,EAAE,MAAM;KAAE,CAAC,CAAC;CAClG;AAED;;;GAGG,CACH,eAAeP,sBAAsB,CAACJ,GAA2B,EAAE;IACjEY,IAAG,IAAA,CAACC,GAAG,CAACC,MAAK,QAAA,CAAC,4DAA4D,CAAC,CAAC,CAAC;IAE7E,uEAAuE;IACvE,oGAAoG;IACpG,EAAE;IACF,6DAA6D;IAC7D,6CAA6C;IAC7C,kDAAkD;IAClD,MAAMC,mBAAmB,GAAG,0CAA0C,AAAC,EAAC,uBAAuB;IAE/F,MAAMC,OAAO,GAAG,CAAC,wDAAwD,EAAED,mBAAmB,CAAC,kBAAkB,CAAC,AAAC;IACnH,MAAME,EAAE,GAAGjB,GAAG,CAACkB,oBAAoB,CAACC,OAAO,aAAa,EAAE,CAAC,AAAC;IAC5D,MAAMC,GAAG,GAAG,CAAC,EAAEJ,OAAO,CAAC,kBAAkB,EAAEV,kBAAkB,CAACW,EAAE,CAAC,CAAC,CAAC,AAAC;IACpE,MAAMtB,gBAAgB,EAAE,CAAC;IACzBG,sBAAsB,GAAG,MAAMuB,CAAAA,GAAAA,cAA2B,AAAK,CAAA,4BAAL,CAACD,GAAG,CAAC,CAAC;CACjE;AAEM,eAAezB,gBAAgB,GAAG;IACvC,OAAMG,sBAAsB,QAAO,GAA7BA,KAAAA,CAA6B,GAA7BA,sBAAsB,CAAEwB,KAAK,EAAE,CAAA,CAAC;IACtCxB,sBAAsB,GAAG,IAAI,CAAC;CAC/B;AAEM,eAAeF,sBAAsB,CAC1C2B,iBAAyB,EACzBf,KAAa,EAC2B;IACxC,MAAMgB,IAAI,GAAG,MAAM3B,0BAA0B,CAAC0B,iBAAiB,CAAC,AAAC;QAC1DC,GAA6C;IAApD,OAAOA,CAAAA,GAA6C,GAA7CA,IAAI,CAACC,IAAI,CAAC,CAACzB,GAAG,GAAKA,GAAG,CAACS,WAAW,KAAKD,KAAK;IAAA,CAAC,YAA7CgB,GAA6C,GAAI,IAAI,CAAC;CAC9D;AAEM,eAAe3B,0BAA0B,CAC9C0B,iBAAyB,EACU;IACnC,MAAMG,IAAI,GAAG,MAAMhB,CAAAA,GAAAA,UAAK,AAAkC,CAAA,QAAlC,CAAC,CAAC,EAAEa,iBAAiB,CAAC,UAAU,CAAC,CAAC,AAAC;IAC3D,MAAMC,IAAI,GAA6BG,aAAa,CAAC,MAAMD,IAAI,CAACE,IAAI,EAAE,CAAC,AAAC;IACxE,iDAAiD;IACjD,OAAOJ,IAAI,CAACK,MAAM,CAAC,CAAC7B,GAAG,GAAKA,GAAG,CAAC8B,KAAK,KAAK,qDAAqD;IAAA,CAAC,CAAC;CAClG;AAED,6GAA6G;AAC7G,yEAAyE;AACzE,SAASH,aAAa,CAACH,IAA8B,EAA4B;IAC/E,MAAMO,eAAe,GAA2B,EAAE,AAAC;IAEnD,KAAK,MAAM/B,IAAG,IAAIwB,IAAI,CAAE;QACtB,IAAIxB,IAAG,CAACS,WAAW,KAAK,WAAW,EAAE;YACnC,MAAMuB,QAAQ,GAAGhC,IAAG,CAACO,EAAE,CAAC0B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,AAAC;YACtC,MAAMzB,KAAK,GAAGR,IAAG,CAACS,WAAW,AAAC;YAC9BsB,eAAe,CAACC,QAAQ,CAAC,GAAGxB,KAAK,CAAC;SACnC;KACF;IAED,OAAOgB,IAAI,CAACU,GAAG,CAAC,CAAClC,GAAG,GAAK;QACvB,IAAIA,GAAG,CAACS,WAAW,KAAK,WAAW,EAAE;YACnC,MAAMuB,QAAQ,GAAGhC,GAAG,CAACO,EAAE,CAAC0B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,AAAC;gBACpBF,SAAyB;YAA3C/B,GAAG,CAACS,WAAW,GAAGsB,CAAAA,SAAyB,GAAzBA,eAAe,CAACC,QAAQ,CAAC,YAAzBD,SAAyB,GAAI/B,GAAG,CAACS,WAAW,CAAC;SAChE;QACD,OAAOT,GAAG,CAAC;KACZ,CAAC,CAAC;CACJ"}