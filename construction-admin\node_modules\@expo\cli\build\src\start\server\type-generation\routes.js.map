{"version": 3, "sources": ["../../../../../src/start/server/type-generation/routes.ts"], "sourcesContent": ["import fs from 'fs/promises';\nimport debounce from 'lodash.debounce';\nimport { Server } from 'metro';\nimport path from 'path';\n\nimport { directoryExistsAsync } from '../../../utils/dir';\nimport { unsafeTemplate } from '../../../utils/template';\nimport { ServerLike } from '../BundlerDevServer';\nimport { metroWatchTypeScriptFiles } from '../metro/metroWatchTypeScriptFiles';\n\n// /test/[...param1]/[param2]/[param3] - captures [\"param1\", \"param2\", \"param3\"]\nexport const CAPTURE_DYNAMIC_PARAMS = /\\[(?:\\.{3})?(\\w*?)[\\]$]/g;\n// /[...param1]/ - Match [...param1]\nexport const CATCH_ALL = /\\[\\.\\.\\..+?\\]/g;\n// /[param1] - Match [param1]\nexport const SLUG = /\\[.+?\\]/g;\n// /(group1,group2,group3)/test - match (group1,group2,group3)\nexport const ARRAY_GROUP_REGEX = /\\(\\s*\\w[\\w\\s]*?,.*?\\)/g;\n// /(group1,group2,group3)/test - captures [\"group1\", \"group2\", \"group3\"]\nexport const CAPTURE_GROUP_REGEX = /[\\\\(,]\\s*(\\w[\\w\\s]*?)\\s*(?=[,\\\\)])/g;\n/**\n * Match:\n *  - _layout files, +html, +not-found, string+api, etc\n *  - Routes can still use `+`, but it cannot be in the last segment.\n */\nexport const TYPED_ROUTES_EXCLUSION_REGEX = /(_layout|[^/]*?\\+[^/]*?)\\.[tj]sx?$/;\n\nexport interface SetupTypedRoutesOptions {\n  server?: ServerLike;\n  metro?: Server | null;\n  typesDirectory: string;\n  projectRoot: string;\n  /** Absolute expo router routes directory. */\n  routerDirectory: string;\n}\n\nexport async function setupTypedRoutes({\n  server,\n  metro,\n  typesDirectory,\n  projectRoot,\n  routerDirectory,\n}: SetupTypedRoutesOptions) {\n  const { filePathToRoute, staticRoutes, dynamicRoutes, addFilePath, isRouteFile } =\n    getTypedRoutesUtils(routerDirectory);\n\n  if (metro && server) {\n    // Setup out watcher first\n    metroWatchTypeScriptFiles({\n      projectRoot,\n      server,\n      metro,\n      eventTypes: ['add', 'delete', 'change'],\n      async callback({ filePath, type }) {\n        if (!isRouteFile(filePath)) {\n          return;\n        }\n\n        let shouldRegenerate = false;\n\n        if (type === 'delete') {\n          const route = filePathToRoute(filePath);\n          staticRoutes.delete(route);\n          dynamicRoutes.delete(route);\n          shouldRegenerate = true;\n        } else {\n          shouldRegenerate = addFilePath(filePath);\n        }\n\n        if (shouldRegenerate) {\n          regenerateRouterDotTS(\n            typesDirectory,\n            new Set([...staticRoutes.values()].flatMap((v) => Array.from(v))),\n            new Set([...dynamicRoutes.values()].flatMap((v) => Array.from(v))),\n            new Set(dynamicRoutes.keys())\n          );\n        }\n      },\n    });\n  }\n\n  if (await directoryExistsAsync(routerDirectory)) {\n    // Do we need to walk the entire tree on startup?\n    // Idea: Store the list of files in the last write, then simply check Git for what files have changed\n    await walk(routerDirectory, addFilePath);\n  }\n\n  regenerateRouterDotTS(\n    typesDirectory,\n    new Set([...staticRoutes.values()].flatMap((v) => Array.from(v))),\n    new Set([...dynamicRoutes.values()].flatMap((v) => Array.from(v))),\n    new Set(dynamicRoutes.keys())\n  );\n}\n\n/**\n * Generate a router.d.ts file that contains all of the routes in the project.\n * Should be debounced as its very common for developers to make changes to multiple files at once (eg Save All)\n */\nconst regenerateRouterDotTS = debounce(\n  async (\n    typesDir: string,\n    staticRoutes: Set<string>,\n    dynamicRoutes: Set<string>,\n    dynamicRouteTemplates: Set<string>\n  ) => {\n    await fs.mkdir(typesDir, { recursive: true });\n    await fs.writeFile(\n      path.resolve(typesDir, './router.d.ts'),\n      getTemplateString(staticRoutes, dynamicRoutes, dynamicRouteTemplates)\n    );\n  },\n  100\n);\n\n/*\n * This is exported for testing purposes\n */\nexport function getTemplateString(\n  staticRoutes: Set<string>,\n  dynamicRoutes: Set<string>,\n  dynamicRouteTemplates: Set<string>\n) {\n  return routerDotTSTemplate({\n    staticRoutes: setToUnionType(staticRoutes),\n    dynamicRoutes: setToUnionType(dynamicRoutes),\n    dynamicRouteParams: setToUnionType(dynamicRouteTemplates),\n  });\n}\n\n/**\n * Utility functions for typed routes\n *\n * These are extracted for easier testing\n */\nexport function getTypedRoutesUtils(appRoot: string, filePathSeperator = path.sep) {\n  /*\n   * staticRoutes are a map where the key if the route without groups and the value\n   *   is another set of all group versions of the route. e.g,\n   *    Map([\n   *      [\"/\", [\"/(app)/(notes)\", \"/(app)/(profile)\"]\n   *    ])\n   */\n  const staticRoutes = new Map<string, Set<string>>([['/', new Set('/')]]);\n  /*\n   * dynamicRoutes are the same as staticRoutes (key if the resolved route,\n   *   and the value is a set of possible routes). e.g:\n   *\n   * /[...fruits] -> /${CatchAllRoutePart<T>}\n   * /color/[color] -> /color/${SingleRoutePart<T>}\n   *\n   * The keys of this map are also important, as they can be used as \"static\" types\n   * <Link href={{ pathname: \"/[...fruits]\",params: { fruits: [\"apple\"] } }} />\n   */\n  const dynamicRoutes = new Map<string, Set<string>>();\n\n  function normalizedFilePath(filePath: string) {\n    return filePath.replaceAll(filePathSeperator, '/');\n  }\n\n  const normalizedAppRoot = normalizedFilePath(appRoot);\n\n  const filePathToRoute = (filePath: string) => {\n    return normalizedFilePath(filePath)\n      .replace(normalizedAppRoot, '')\n      .replace(/index\\.[jt]sx?/, '')\n      .replace(/\\.[jt]sx?$/, '');\n  };\n\n  const isRouteFile = (filePath: string) => {\n    if (filePath.match(TYPED_ROUTES_EXCLUSION_REGEX)) {\n      return false;\n    }\n\n    // Route files must be nested with in the appRoot\n    const relative = path.relative(appRoot, filePath);\n    return relative && !relative.startsWith('..') && !path.isAbsolute(relative);\n  };\n\n  const addFilePath = (filePath: string): boolean => {\n    if (!isRouteFile(filePath)) {\n      return false;\n    }\n\n    const route = filePathToRoute(filePath);\n\n    // We have already processed this file\n    if (staticRoutes.has(route) || dynamicRoutes.has(route)) {\n      return false;\n    }\n\n    const dynamicParams = new Set(\n      [...route.matchAll(CAPTURE_DYNAMIC_PARAMS)].map((match) => match[1])\n    );\n    const isDynamic = dynamicParams.size > 0;\n\n    const addRoute = (originalRoute: string, route: string) => {\n      if (isDynamic) {\n        let set = dynamicRoutes.get(originalRoute);\n\n        if (!set) {\n          set = new Set();\n          dynamicRoutes.set(originalRoute, set);\n        }\n\n        set.add(\n          route\n            .replaceAll(CATCH_ALL, '${CatchAllRoutePart<T>}')\n            .replaceAll(SLUG, '${SingleRoutePart<T>}')\n        );\n      } else {\n        let set = staticRoutes.get(originalRoute);\n\n        if (!set) {\n          set = new Set();\n          staticRoutes.set(originalRoute, set);\n        }\n\n        set.add(route);\n      }\n    };\n\n    if (!route.match(ARRAY_GROUP_REGEX)) {\n      addRoute(route, route);\n    }\n\n    // Does this route have a group? eg /(group)\n    if (route.includes('/(')) {\n      const routeWithoutGroups = route.replace(/\\/\\(.+?\\)/g, '');\n      addRoute(route, routeWithoutGroups);\n\n      // If there are multiple groups, we need to expand them\n      // eg /(test1,test2)/page => /test1/page & /test2/page\n      for (const routeWithSingleGroup of extrapolateGroupRoutes(route)) {\n        addRoute(route, routeWithSingleGroup);\n      }\n    }\n\n    return true;\n  };\n\n  return {\n    staticRoutes,\n    dynamicRoutes,\n    filePathToRoute,\n    addFilePath,\n    isRouteFile,\n  };\n}\n\nexport const setToUnionType = <T>(set: Set<T>) => {\n  return set.size > 0 ? [...set].map((s) => `\\`${s}\\``).join(' | ') : 'never';\n};\n\n/**\n * Recursively walk a directory and call the callback with the file path.\n */\nasync function walk(directory: string, callback: (filePath: string) => void) {\n  const files = await fs.readdir(directory);\n  for (const file of files) {\n    const p = path.join(directory, file);\n    if ((await fs.stat(p)).isDirectory()) {\n      await walk(p, callback);\n    } else {\n      // Normalise the paths so they are easier to convert to URLs\n      const normalizedPath = p.replaceAll(path.sep, '/');\n      callback(normalizedPath);\n    }\n  }\n}\n\n/**\n * Given a route, return all possible routes that could be generated from it.\n */\nexport function extrapolateGroupRoutes(\n  route: string,\n  routes: Set<string> = new Set()\n): Set<string> {\n  // Create a version with no groups. We will then need to cleanup double and/or trailing slashes\n  routes.add(route.replaceAll(ARRAY_GROUP_REGEX, '').replaceAll(/\\/+/g, '/').replace(/\\/$/, ''));\n\n  const match = route.match(ARRAY_GROUP_REGEX);\n\n  if (!match) {\n    routes.add(route);\n    return routes;\n  }\n\n  const groupsMatch = match[0];\n\n  for (const group of groupsMatch.matchAll(CAPTURE_GROUP_REGEX)) {\n    extrapolateGroupRoutes(route.replace(groupsMatch, `(${group[1].trim()})`), routes);\n  }\n\n  return routes;\n}\n\n/**\n * NOTE: This code refers to a specific version of `expo-router` and is therefore unsafe to\n * mix with arbitrary versions.\n * TODO: Version this code with `expo-router` or version expo-router with `@expo/cli`.\n */\nconst routerDotTSTemplate = unsafeTemplate`/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable import/export */\n/* eslint-disable @typescript-eslint/ban-types */\ndeclare module \"expo-router\" {\n  import type { LinkProps as OriginalLinkProps } from 'expo-router/build/link/Link';\n  import type { Router as OriginalRouter } from 'expo-router/build/types';\n  export * from 'expo-router/build';\n\n  // prettier-ignore\n  type StaticRoutes = ${'staticRoutes'};\n  // prettier-ignore\n  type DynamicRoutes<T extends string> = ${'dynamicRoutes'};\n  // prettier-ignore\n  type DynamicRouteTemplate = ${'dynamicRouteParams'};\n\n  type RelativePathString = \\`./\\${string}\\` | \\`../\\${string}\\` | '..';\n  type AbsoluteRoute = DynamicRouteTemplate | StaticRoutes;\n  type ExternalPathString = \\`\\${string}:\\${string}\\`;\n\n  type ExpoRouterRoutes = DynamicRouteTemplate | StaticRoutes | RelativePathString;\n  export type AllRoutes = ExpoRouterRoutes | ExternalPathString;\n\n  /****************\n   * Route Utils  *\n   ****************/\n\n  type SearchOrHash = \\`?\\${string}\\` | \\`#\\${string}\\`;\n  type UnknownInputParams = Record<string, string | number | (string | number)[]>;\n  type UnknownOutputParams = Record<string, string | string[]>;\n\n  /**\n   * Return only the RoutePart of a string. If the string has multiple parts return never\n   *\n   * string   | type\n   * ---------|------\n   * 123      | 123\n   * /123/abc | never\n   * 123?abc  | never\n   * ./123    | never\n   * /123     | never\n   * 123/../  | never\n   */\n  type SingleRoutePart<S extends string> = S extends \\`\\${string}/\\${string}\\`\n    ? never\n    : S extends \\`\\${string}\\${SearchOrHash}\\`\n    ? never\n    : S extends ''\n    ? never\n    : S extends \\`(\\${string})\\`\n    ? never\n    : S extends \\`[\\${string}]\\`\n    ? never\n    : S;\n\n  /**\n   * Return only the CatchAll router part. If the string has search parameters or a hash return never\n   */\n  type CatchAllRoutePart<S extends string> = S extends \\`\\${string}\\${SearchOrHash}\\`\n    ? never\n    : S extends ''\n    ? never\n    : S extends \\`\\${string}(\\${string})\\${string}\\`\n    ? never\n    : S extends \\`\\${string}[\\${string}]\\${string}\\`\n    ? never\n    : S;\n\n  // type OptionalCatchAllRoutePart<S extends string> = S extends \\`\\${string}\\${SearchOrHash}\\` ? never : S\n\n  /**\n   * Return the name of a route parameter\n   * '[test]'    -> 'test'\n   * 'test'      -> never\n   * '[...test]' -> '...test'\n   */\n  type IsParameter<Part> = Part extends \\`[\\${infer ParamName}]\\` ? ParamName : never;\n\n  /**\n   * Return a union of all parameter names. If there are no names return never\n   *\n   * /[test]         -> 'test'\n   * /[abc]/[...def] -> 'abc'|'...def'\n   */\n  type ParameterNames<Path> = Path extends \\`\\${infer PartA}/\\${infer PartB}\\`\n    ? IsParameter<PartA> | ParameterNames<PartB>\n    : IsParameter<Path>;\n\n  /**\n   * Returns all segements of a route.\n   *\n   * /(group)/123/abc/[id]/[...rest] -> ['(group)', '123', 'abc', '[id]', '[...rest]'\n   */\n  type RouteSegments<Path> = Path extends \\`\\${infer PartA}/\\${infer PartB}\\`\n    ? PartA extends '' | '.'\n      ? [...RouteSegments<PartB>]\n      : [PartA, ...RouteSegments<PartB>]\n    : Path extends ''\n    ? []\n    : [Path];\n\n  /**\n   * Returns a Record of the routes parameters as strings and CatchAll parameters\n   *\n   * There are two versions, input and output, as you can input 'string | number' but\n   *  the output will always be 'string'\n   *\n   * /[id]/[...rest] -> { id: string, rest: string[] }\n   * /no-params      -> {}\n   */\n  type InputRouteParams<Path> = {\n    [Key in ParameterNames<Path> as Key extends \\`...\\${infer Name}\\`\n      ? Name\n      : Key]: Key extends \\`...\\${string}\\` ? (string | number)[] : string | number;\n  } & UnknownInputParams;\n\n  type OutputRouteParams<Path> = {\n    [Key in ParameterNames<Path> as Key extends \\`...\\${infer Name}\\`\n      ? Name\n      : Key]: Key extends \\`...\\${string}\\` ? string[] : string;\n  } & UnknownOutputParams;\n\n  /**\n   * Returns the search parameters for a route.\n   */\n  export type SearchParams<T extends AllRoutes> = T extends DynamicRouteTemplate\n    ? OutputRouteParams<T>\n    : T extends StaticRoutes\n    ? never\n    : UnknownOutputParams;\n\n  /**\n   * Route is mostly used as part of Href to ensure that a valid route is provided\n   *\n   * Given a dynamic route, this will return never. This is helpful for conditional logic\n   *\n   * /test         -> /test, /test2, etc\n   * /test/[abc]   -> never\n   * /test/resolve -> /test, /test2, etc\n   *\n   * Note that if we provide a value for [abc] then the route is allowed\n   *\n   * This is named Route to prevent confusion, as users they will often see it in tooltips\n   */\n  export type Route<T> = T extends string\n    ? T extends DynamicRouteTemplate\n      ? never\n      :\n          | StaticRoutes\n          | RelativePathString\n          | ExternalPathString\n          | (T extends \\`\\${infer P}\\${SearchOrHash}\\`\n              ? P extends DynamicRoutes<infer _>\n                ? T\n                : never\n              : T extends DynamicRoutes<infer _>\n              ? T\n              : never)\n    : never;\n\n  /*********\n   * Href  *\n   *********/\n\n  export type Href<T> = T extends Record<'pathname', string> ? HrefObject<T> : Route<T>;\n\n  export type HrefObject<\n    R extends Record<'pathname', string>,\n    P = R['pathname'],\n  > = P extends DynamicRouteTemplate\n    ? { pathname: P; params: InputRouteParams<P> }\n    : P extends Route<P>\n    ? { pathname: Route<P> | DynamicRouteTemplate; params?: never | InputRouteParams<never> }\n    : never;\n\n  /***********************\n   * Expo Router Exports *\n   ***********************/\n\n  export type Router = Omit<OriginalRouter, 'push' | 'replace' | 'setParams'> & {\n    /** Navigate to the provided href. */\n    push: <T>(href: Href<T>) => void;\n    /** Navigate to route without appending to the history. */\n    replace: <T>(href: Href<T>) => void;\n    /** Update the current route query params. */\n    setParams: <T = ''>(params?: T extends '' ? Record<string, string> : InputRouteParams<T>) => void;\n  };\n\n  /** The imperative router. */\n  export const router: Router;\n\n  /************\n   * <Link /> *\n   ************/\n  export interface LinkProps<T> extends OriginalLinkProps {\n    href: Href<T>;\n  }\n\n  export interface LinkComponent {\n    <T>(props: React.PropsWithChildren<LinkProps<T>>): JSX.Element;\n    /** Helper method to resolve an Href object into a string. */\n    resolveHref: <T>(href: Href<T>) => string;\n  }\n\n  /**\n   * Component to render link to another route using a path.\n   * Uses an anchor tag on the web.\n   *\n   * @param props.href Absolute path to route (e.g. \\`/feeds/hot\\`).\n   * @param props.replace Should replace the current route without adding to the history.\n   * @param props.asChild Forward props to child component. Useful for custom buttons.\n   * @param props.children Child elements to render the content.\n   * @param props.className On web, this sets the HTML \\`class\\` directly. On native, this can be used with CSS interop tools like Nativewind.\n   */\n  export const Link: LinkComponent;\n\n  /** Redirects to the href as soon as the component is mounted. */\n  export const Redirect: <T>(\n    props: React.PropsWithChildren<{ href: Href<T> }>\n  ) => JSX.Element;\n\n  /************\n   * Hooks *\n   ************/\n  export function useRouter(): Router;\n\n  export function useLocalSearchParams<\n    T extends AllRoutes | UnknownOutputParams = UnknownOutputParams,\n  >(): T extends AllRoutes ? SearchParams<T> : T;\n\n  /** @deprecated renamed to \\`useGlobalSearchParams\\` */\n  export function useSearchParams<\n    T extends AllRoutes | UnknownOutputParams = UnknownOutputParams,\n  >(): T extends AllRoutes ? SearchParams<T> : T;\n\n  export function useGlobalSearchParams<\n    T extends AllRoutes | UnknownOutputParams = UnknownOutputParams,\n  >(): T extends AllRoutes ? SearchParams<T> : T;\n\n  export function useSegments<\n    T extends AbsoluteRoute | RouteSegments<AbsoluteRoute> | RelativePathString,\n  >(): T extends AbsoluteRoute ? RouteSegments<T> : T extends string ? string[] : T;\n}\n`;\n"], "names": ["setupTypedRoutes", "getTemplateString", "getTypedRoutesUtils", "extrapolateGroupRoutes", "CAPTURE_DYNAMIC_PARAMS", "CATCH_ALL", "SLUG", "ARRAY_GROUP_REGEX", "CAPTURE_GROUP_REGEX", "TYPED_ROUTES_EXCLUSION_REGEX", "server", "metro", "typesDirectory", "projectRoot", "routerDirectory", "filePathToRoute", "staticRoutes", "dynamicRoutes", "addFilePath", "isRouteFile", "metroWatchTypeScriptFiles", "eventTypes", "callback", "filePath", "type", "shouldRegenerate", "route", "delete", "regenerateRouterDotTS", "Set", "values", "flatMap", "v", "Array", "from", "keys", "directoryExistsAsync", "walk", "debounce", "typesDir", "dynamicRouteTemplates", "fs", "mkdir", "recursive", "writeFile", "path", "resolve", "routerDotTSTemplate", "setToUnionType", "dynamicRouteParams", "appRoot", "filePathSeperator", "sep", "Map", "normalizedFilePath", "replaceAll", "normalizedAppRoot", "replace", "match", "relative", "startsWith", "isAbsolute", "has", "dynamicParams", "matchAll", "map", "isDynamic", "size", "addRoute", "originalRoute", "set", "get", "add", "includes", "routeWithoutGroups", "routeWithSingleGroup", "s", "join", "directory", "files", "readdir", "file", "p", "stat", "isDirectory", "normalizedPath", "routes", "groupsMatch", "group", "trim", "unsafeTemplate"], "mappings": "AAAA;;;;QAoCsBA,gBAAgB,GAAhBA,gBAAgB;QAkFtBC,iBAAiB,GAAjBA,iBAAiB;QAiBjBC,mBAAmB,GAAnBA,mBAAmB;QA2InBC,sBAAsB,GAAtBA,sBAAsB;;AAlRvB,IAAA,SAAa,kCAAb,aAAa,EAAA;AACP,IAAA,eAAiB,kCAAjB,iBAAiB,EAAA;AAErB,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEc,IAAA,IAAoB,WAApB,oBAAoB,CAAA;AAC1B,IAAA,SAAyB,WAAzB,yBAAyB,CAAA;AAEd,IAAA,0BAAoC,WAApC,oCAAoC,CAAA;;;;;;AAGvE,MAAMC,sBAAsB,6BAA6B,AAAC;QAApDA,sBAAsB,GAAtBA,sBAAsB;AAE5B,MAAMC,SAAS,mBAAmB,AAAC;QAA7BA,SAAS,GAATA,SAAS;AAEf,MAAMC,IAAI,aAAa,AAAC;QAAlBA,IAAI,GAAJA,IAAI;AAEV,MAAMC,iBAAiB,2BAA2B,AAAC;QAA7CA,iBAAiB,GAAjBA,iBAAiB;AAEvB,MAAMC,mBAAmB,wCAAwC,AAAC;QAA5DA,mBAAmB,GAAnBA,mBAAmB;AAMzB,MAAMC,4BAA4B,uCAAuC,AAAC;QAApEA,4BAA4B,GAA5BA,4BAA4B;AAWlC,eAAeT,gBAAgB,CAAC,EACrCU,MAAM,CAAA,EACNC,KAAK,CAAA,EACLC,cAAc,CAAA,EACdC,WAAW,CAAA,EACXC,eAAe,CAAA,EACS,EAAE;IAC1B,MAAM,EAAEC,eAAe,CAAA,EAAEC,YAAY,CAAA,EAAEC,aAAa,CAAA,EAAEC,WAAW,CAAA,EAAEC,WAAW,CAAA,EAAE,GAC9EjB,mBAAmB,CAACY,eAAe,CAAC,AAAC;IAEvC,IAAIH,KAAK,IAAID,MAAM,EAAE;QACnB,0BAA0B;QAC1BU,CAAAA,GAAAA,0BAAyB,AA8BvB,CAAA,0BA9BuB,CAAC;YACxBP,WAAW;YACXH,MAAM;YACNC,KAAK;YACLU,UAAU,EAAE;gBAAC,KAAK;gBAAE,QAAQ;gBAAE,QAAQ;aAAC;YACvC,MAAMC,QAAQ,EAAC,EAAEC,QAAQ,CAAA,EAAEC,IAAI,CAAA,EAAE,EAAE;gBACjC,IAAI,CAACL,WAAW,CAACI,QAAQ,CAAC,EAAE;oBAC1B,OAAO;iBACR;gBAED,IAAIE,gBAAgB,GAAG,KAAK,AAAC;gBAE7B,IAAID,IAAI,KAAK,QAAQ,EAAE;oBACrB,MAAME,KAAK,GAAGX,eAAe,CAACQ,QAAQ,CAAC,AAAC;oBACxCP,YAAY,CAACW,MAAM,CAACD,KAAK,CAAC,CAAC;oBAC3BT,aAAa,CAACU,MAAM,CAACD,KAAK,CAAC,CAAC;oBAC5BD,gBAAgB,GAAG,IAAI,CAAC;iBACzB,MAAM;oBACLA,gBAAgB,GAAGP,WAAW,CAACK,QAAQ,CAAC,CAAC;iBAC1C;gBAED,IAAIE,gBAAgB,EAAE;oBACpBG,qBAAqB,CACnBhB,cAAc,EACd,IAAIiB,GAAG,CAAC;2BAAIb,YAAY,CAACc,MAAM,EAAE;qBAAC,CAACC,OAAO,CAAC,CAACC,CAAC,GAAKC,KAAK,CAACC,IAAI,CAACF,CAAC,CAAC;oBAAA,CAAC,CAAC,EACjE,IAAIH,GAAG,CAAC;2BAAIZ,aAAa,CAACa,MAAM,EAAE;qBAAC,CAACC,OAAO,CAAC,CAACC,CAAC,GAAKC,KAAK,CAACC,IAAI,CAACF,CAAC,CAAC;oBAAA,CAAC,CAAC,EAClE,IAAIH,GAAG,CAACZ,aAAa,CAACkB,IAAI,EAAE,CAAC,CAC9B,CAAC;iBACH;aACF;SACF,CAAC,CAAC;KACJ;IAED,IAAI,MAAMC,CAAAA,GAAAA,IAAoB,AAAiB,CAAA,qBAAjB,CAACtB,eAAe,CAAC,EAAE;QAC/C,iDAAiD;QACjD,qGAAqG;QACrG,MAAMuB,IAAI,CAACvB,eAAe,EAAEI,WAAW,CAAC,CAAC;KAC1C;IAEDU,qBAAqB,CACnBhB,cAAc,EACd,IAAIiB,GAAG,CAAC;WAAIb,YAAY,CAACc,MAAM,EAAE;KAAC,CAACC,OAAO,CAAC,CAACC,CAAC,GAAKC,KAAK,CAACC,IAAI,CAACF,CAAC,CAAC;IAAA,CAAC,CAAC,EACjE,IAAIH,GAAG,CAAC;WAAIZ,aAAa,CAACa,MAAM,EAAE;KAAC,CAACC,OAAO,CAAC,CAACC,CAAC,GAAKC,KAAK,CAACC,IAAI,CAACF,CAAC,CAAC;IAAA,CAAC,CAAC,EAClE,IAAIH,GAAG,CAACZ,aAAa,CAACkB,IAAI,EAAE,CAAC,CAC9B,CAAC;CACH;AAED;;;GAGG,CACH,MAAMP,qBAAqB,GAAGU,CAAAA,GAAAA,eAAQ,AAcrC,CAAA,QAdqC,CACpC,OACEC,QAAgB,EAChBvB,YAAyB,EACzBC,aAA0B,EAC1BuB,qBAAkC,GAC/B;IACH,MAAMC,SAAE,QAAA,CAACC,KAAK,CAACH,QAAQ,EAAE;QAAEI,SAAS,EAAE,IAAI;KAAE,CAAC,CAAC;IAC9C,MAAMF,SAAE,QAAA,CAACG,SAAS,CAChBC,KAAI,QAAA,CAACC,OAAO,CAACP,QAAQ,EAAE,eAAe,CAAC,EACvCtC,iBAAiB,CAACe,YAAY,EAAEC,aAAa,EAAEuB,qBAAqB,CAAC,CACtE,CAAC;CACH,EACD,GAAG,CACJ,AAAC;AAKK,SAASvC,iBAAiB,CAC/Be,YAAyB,EACzBC,aAA0B,EAC1BuB,qBAAkC,EAClC;IACA,OAAOO,mBAAmB,CAAC;QACzB/B,YAAY,EAAEgC,cAAc,CAAChC,YAAY,CAAC;QAC1CC,aAAa,EAAE+B,cAAc,CAAC/B,aAAa,CAAC;QAC5CgC,kBAAkB,EAAED,cAAc,CAACR,qBAAqB,CAAC;KAC1D,CAAC,CAAC;CACJ;AAOM,SAAStC,mBAAmB,CAACgD,OAAe,EAAEC,iBAAiB,GAAGN,KAAI,QAAA,CAACO,GAAG,EAAE;IACjF;;;;;;KAMG,CACH,MAAMpC,YAAY,GAAG,IAAIqC,GAAG,CAAsB;QAAC;YAAC,GAAG;YAAE,IAAIxB,GAAG,CAAC,GAAG,CAAC;SAAC;KAAC,CAAC,AAAC;IACzE;;;;;;;;;KASG,CACH,MAAMZ,aAAa,GAAG,IAAIoC,GAAG,EAAuB,AAAC;IAErD,SAASC,kBAAkB,CAAC/B,QAAgB,EAAE;QAC5C,OAAOA,QAAQ,CAACgC,UAAU,CAACJ,iBAAiB,EAAE,GAAG,CAAC,CAAC;KACpD;IAED,MAAMK,iBAAiB,GAAGF,kBAAkB,CAACJ,OAAO,CAAC,AAAC;IAEtD,MAAMnC,eAAe,GAAG,CAACQ,QAAgB,GAAK;QAC5C,OAAO+B,kBAAkB,CAAC/B,QAAQ,CAAC,CAChCkC,OAAO,CAACD,iBAAiB,EAAE,EAAE,CAAC,CAC9BC,OAAO,mBAAmB,EAAE,CAAC,CAC7BA,OAAO,eAAe,EAAE,CAAC,CAAC;KAC9B,AAAC;IAEF,MAAMtC,WAAW,GAAG,CAACI,QAAgB,GAAK;QACxC,IAAIA,QAAQ,CAACmC,KAAK,CAACjD,4BAA4B,CAAC,EAAE;YAChD,OAAO,KAAK,CAAC;SACd;QAED,iDAAiD;QACjD,MAAMkD,QAAQ,GAAGd,KAAI,QAAA,CAACc,QAAQ,CAACT,OAAO,EAAE3B,QAAQ,CAAC,AAAC;QAClD,OAAOoC,QAAQ,IAAI,CAACA,QAAQ,CAACC,UAAU,CAAC,IAAI,CAAC,IAAI,CAACf,KAAI,QAAA,CAACgB,UAAU,CAACF,QAAQ,CAAC,CAAC;KAC7E,AAAC;IAEF,MAAMzC,WAAW,GAAG,CAACK,QAAgB,GAAc;QACjD,IAAI,CAACJ,WAAW,CAACI,QAAQ,CAAC,EAAE;YAC1B,OAAO,KAAK,CAAC;SACd;QAED,MAAMG,MAAK,GAAGX,eAAe,CAACQ,QAAQ,CAAC,AAAC;QAExC,sCAAsC;QACtC,IAAIP,YAAY,CAAC8C,GAAG,CAACpC,MAAK,CAAC,IAAIT,aAAa,CAAC6C,GAAG,CAACpC,MAAK,CAAC,EAAE;YACvD,OAAO,KAAK,CAAC;SACd;QAED,MAAMqC,aAAa,GAAG,IAAIlC,GAAG,CAC3B;eAAIH,MAAK,CAACsC,QAAQ,CAAC5D,sBAAsB,CAAC;SAAC,CAAC6D,GAAG,CAAC,CAACP,KAAK,GAAKA,KAAK,CAAC,CAAC,CAAC;QAAA,CAAC,CACrE,AAAC;QACF,MAAMQ,SAAS,GAAGH,aAAa,CAACI,IAAI,GAAG,CAAC,AAAC;QAEzC,MAAMC,QAAQ,GAAG,CAACC,aAAqB,EAAE3C,KAAa,GAAK;YACzD,IAAIwC,SAAS,EAAE;gBACb,IAAII,GAAG,GAAGrD,aAAa,CAACsD,GAAG,CAACF,aAAa,CAAC,AAAC;gBAE3C,IAAI,CAACC,GAAG,EAAE;oBACRA,GAAG,GAAG,IAAIzC,GAAG,EAAE,CAAC;oBAChBZ,aAAa,CAACqD,GAAG,CAACD,aAAa,EAAEC,GAAG,CAAC,CAAC;iBACvC;gBAEDA,GAAG,CAACE,GAAG,CACL9C,KAAK,CACF6B,UAAU,CAAClD,SAAS,EAAE,yBAAyB,CAAC,CAChDkD,UAAU,CAACjD,IAAI,EAAE,uBAAuB,CAAC,CAC7C,CAAC;aACH,MAAM;gBACL,IAAIgE,GAAG,GAAGtD,YAAY,CAACuD,GAAG,CAACF,aAAa,CAAC,AAAC;gBAE1C,IAAI,CAACC,GAAG,EAAE;oBACRA,GAAG,GAAG,IAAIzC,GAAG,EAAE,CAAC;oBAChBb,YAAY,CAACsD,GAAG,CAACD,aAAa,EAAEC,GAAG,CAAC,CAAC;iBACtC;gBAEDA,GAAG,CAACE,GAAG,CAAC9C,KAAK,CAAC,CAAC;aAChB;SACF,AAAC;QAEF,IAAI,CAACA,MAAK,CAACgC,KAAK,CAACnD,iBAAiB,CAAC,EAAE;YACnC6D,QAAQ,CAAC1C,MAAK,EAAEA,MAAK,CAAC,CAAC;SACxB;QAED,4CAA4C;QAC5C,IAAIA,MAAK,CAAC+C,QAAQ,CAAC,IAAI,CAAC,EAAE;YACxB,MAAMC,kBAAkB,GAAGhD,MAAK,CAAC+B,OAAO,eAAe,EAAE,CAAC,AAAC;YAC3DW,QAAQ,CAAC1C,MAAK,EAAEgD,kBAAkB,CAAC,CAAC;YAEpC,uDAAuD;YACvD,sDAAsD;YACtD,KAAK,MAAMC,oBAAoB,IAAIxE,sBAAsB,CAACuB,MAAK,CAAC,CAAE;gBAChE0C,QAAQ,CAAC1C,MAAK,EAAEiD,oBAAoB,CAAC,CAAC;aACvC;SACF;QAED,OAAO,IAAI,CAAC;KACb,AAAC;IAEF,OAAO;QACL3D,YAAY;QACZC,aAAa;QACbF,eAAe;QACfG,WAAW;QACXC,WAAW;KACZ,CAAC;CACH;AAEM,MAAM6B,cAAc,GAAG,CAAIsB,GAAW,GAAK;IAChD,OAAOA,GAAG,CAACH,IAAI,GAAG,CAAC,GAAG;WAAIG,GAAG;KAAC,CAACL,GAAG,CAAC,CAACW,CAAC,GAAK,CAAC,EAAE,EAAEA,CAAC,CAAC,EAAE,CAAC;IAAA,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;CAC7E,AAAC;QAFW7B,cAAc,GAAdA,cAAc;AAI3B;;GAEG,CACH,eAAeX,IAAI,CAACyC,SAAiB,EAAExD,QAAoC,EAAE;IAC3E,MAAMyD,KAAK,GAAG,MAAMtC,SAAE,QAAA,CAACuC,OAAO,CAACF,SAAS,CAAC,AAAC;IAC1C,KAAK,MAAMG,IAAI,IAAIF,KAAK,CAAE;QACxB,MAAMG,CAAC,GAAGrC,KAAI,QAAA,CAACgC,IAAI,CAACC,SAAS,EAAEG,IAAI,CAAC,AAAC;QACrC,IAAI,CAAC,MAAMxC,SAAE,QAAA,CAAC0C,IAAI,CAACD,CAAC,CAAC,CAAC,CAACE,WAAW,EAAE,EAAE;YACpC,MAAM/C,IAAI,CAAC6C,CAAC,EAAE5D,QAAQ,CAAC,CAAC;SACzB,MAAM;YACL,4DAA4D;YAC5D,MAAM+D,cAAc,GAAGH,CAAC,CAAC3B,UAAU,CAACV,KAAI,QAAA,CAACO,GAAG,EAAE,GAAG,CAAC,AAAC;YACnD9B,QAAQ,CAAC+D,cAAc,CAAC,CAAC;SAC1B;KACF;CACF;AAKM,SAASlF,sBAAsB,CACpCuB,KAAa,EACb4D,MAAmB,GAAG,IAAIzD,GAAG,EAAE,EAClB;IACb,+FAA+F;IAC/FyD,MAAM,CAACd,GAAG,CAAC9C,KAAK,CAAC6B,UAAU,CAAChD,iBAAiB,EAAE,EAAE,CAAC,CAACgD,UAAU,SAAS,GAAG,CAAC,CAACE,OAAO,QAAQ,EAAE,CAAC,CAAC,CAAC;IAE/F,MAAMC,KAAK,GAAGhC,KAAK,CAACgC,KAAK,CAACnD,iBAAiB,CAAC,AAAC;IAE7C,IAAI,CAACmD,KAAK,EAAE;QACV4B,MAAM,CAACd,GAAG,CAAC9C,KAAK,CAAC,CAAC;QAClB,OAAO4D,MAAM,CAAC;KACf;IAED,MAAMC,WAAW,GAAG7B,KAAK,CAAC,CAAC,CAAC,AAAC;IAE7B,KAAK,MAAM8B,KAAK,IAAID,WAAW,CAACvB,QAAQ,CAACxD,mBAAmB,CAAC,CAAE;QAC7DL,sBAAsB,CAACuB,KAAK,CAAC+B,OAAO,CAAC8B,WAAW,EAAE,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC,CAAC;KACpF;IAED,OAAOA,MAAM,CAAC;CACf;AAED;;;;GAIG,CACH,MAAMvC,mBAAmB,GAAG2C,SAAc,eAAA,CAAC;;;;;;;;;sBASrB,EAAE,cAAc,CAAC;;yCAEE,EAAE,eAAe,CAAC;;8BAE7B,EAAE,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqOrD,CAAC,AAAC"}