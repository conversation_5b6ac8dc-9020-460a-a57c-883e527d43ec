{"version": 3, "sources": ["../../../../../src/start/server/middleware/FaviconMiddleware.ts"], "sourcesContent": ["import { ExpoMiddleware } from './ExpoMiddleware';\nimport { ServerNext, ServerRequest, ServerResponse } from './server.types';\nimport { getFaviconFromExpoConfigAsync } from '../../../export/favicon';\n\nconst debug = require('debug')('expo:start:server:middleware:favicon') as typeof console.log;\n\n/**\n * Middleware for generating a favicon.ico file for the current project if one doesn't exist.\n *\n * Test by making a get request with:\n * curl -v http://localhost:8081/favicon.ico\n */\nexport class FaviconMiddleware extends ExpoMiddleware {\n  constructor(protected projectRoot: string) {\n    super(projectRoot, ['/favicon.ico']);\n  }\n\n  async handleRequestAsync(\n    req: ServerRequest,\n    res: ServerResponse,\n    next: ServerNext\n  ): Promise<void> {\n    if (!['GET', 'HEAD'].includes(req.method || '')) {\n      return next();\n    }\n\n    let faviconImageData: Buffer | null;\n    try {\n      const data = await getFaviconFromExpoConfigAsync(this.projectRoot, { force: true });\n      if (!data) {\n        debug('No favicon defined in the Expo Config, skipping generation.');\n        return next();\n      }\n      faviconImageData = data.source;\n      debug('✅ Generated favicon successfully.');\n    } catch (error: any) {\n      debug('Failed to generate favicon from Expo config:', error);\n      return next(error);\n    }\n    // Respond with the generated favicon file\n    res.setHeader('Content-Type', 'image/x-icon');\n    res.end(faviconImageData);\n  }\n}\n"], "names": ["debug", "require", "FaviconMiddleware", "ExpoMiddleware", "constructor", "projectRoot", "handleRequestAsync", "req", "res", "next", "includes", "method", "faviconImageData", "data", "getFaviconFromExpoConfigAsync", "force", "source", "error", "<PERSON><PERSON><PERSON><PERSON>", "end"], "mappings": "AAAA;;;;AAA+B,IAAA,eAAkB,WAAlB,kBAAkB,CAAA;AAEH,IAAA,QAAyB,WAAzB,yBAAyB,CAAA;AAEvE,MAAMA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,sCAAsC,CAAC,AAAsB,AAAC;AAQtF,MAAMC,iBAAiB,SAASC,eAAc,eAAA;IACnDC,YAAsBC,WAAmB,CAAE;QACzC,KAAK,CAACA,WAAW,EAAE;YAAC,cAAc;SAAC,CAAC,CAAC;aADjBA,WAAmB,GAAnBA,WAAmB;KAExC;IAED,MAAMC,kBAAkB,CACtBC,GAAkB,EAClBC,GAAmB,EACnBC,IAAgB,EACD;QACf,IAAI,CAAC;YAAC,KAAK;YAAE,MAAM;SAAC,CAACC,QAAQ,CAACH,GAAG,CAACI,MAAM,IAAI,EAAE,CAAC,EAAE;YAC/C,OAAOF,IAAI,EAAE,CAAC;SACf;QAED,IAAIG,gBAAgB,AAAe,AAAC;QACpC,IAAI;YACF,MAAMC,IAAI,GAAG,MAAMC,CAAAA,GAAAA,QAA6B,AAAmC,CAAA,8BAAnC,CAAC,IAAI,CAACT,WAAW,EAAE;gBAAEU,KAAK,EAAE,IAAI;aAAE,CAAC,AAAC;YACpF,IAAI,CAACF,IAAI,EAAE;gBACTb,KAAK,CAAC,6DAA6D,CAAC,CAAC;gBACrE,OAAOS,IAAI,EAAE,CAAC;aACf;YACDG,gBAAgB,GAAGC,IAAI,CAACG,MAAM,CAAC;YAC/BhB,KAAK,CAAC,wCAAmC,CAAC,CAAC;SAC5C,CAAC,OAAOiB,KAAK,EAAO;YACnBjB,KAAK,CAAC,8CAA8C,EAAEiB,KAAK,CAAC,CAAC;YAC7D,OAAOR,IAAI,CAACQ,KAAK,CAAC,CAAC;SACpB;QACD,0CAA0C;QAC1CT,GAAG,CAACU,SAAS,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;QAC9CV,GAAG,CAACW,GAAG,CAACP,gBAAgB,CAAC,CAAC;KAC3B;CACF;QA/BYV,iBAAiB,GAAjBA,iBAAiB"}