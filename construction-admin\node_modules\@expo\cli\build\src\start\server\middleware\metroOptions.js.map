{"version": 3, "sources": ["../../../../../src/start/server/middleware/metroOptions.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport type { BundleOptions as MetroBundleOptions } from 'metro/src/shared/types';\nimport resolveFrom from 'resolve-from';\n\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { getRouterDirectoryModuleIdWithManifest } from '../metro/router';\n\nconst debug = require('debug')('expo:metro:options') as typeof console.log;\n\nexport type ExpoMetroOptions = {\n  platform: string;\n  mainModuleName: string;\n  mode: string;\n  minify?: boolean;\n  environment?: string;\n  serializerOutput?: 'static';\n  serializerIncludeMaps?: boolean;\n  lazy?: boolean;\n  engine?: 'hermes';\n  preserveEnvVars?: boolean;\n  bytecode: boolean;\n  asyncRoutes?: boolean;\n\n  baseUrl?: string;\n  isExporting: boolean;\n  /** Module ID relative to the projectRoot for the Expo Router app directory. */\n  routerRoot: string;\n  inlineSourceMap?: boolean;\n};\n\nexport type SerializerOptions = {\n  includeSourceMaps?: boolean;\n  output?: 'static';\n};\n\nexport type ExpoMetroBundleOptions = MetroBundleOptions & {\n  serializerOptions?: SerializerOptions;\n};\n\nexport function shouldEnableAsyncImports(projectRoot: string): boolean {\n  if (env.EXPO_NO_METRO_LAZY) {\n    return false;\n  }\n\n  // `@expo/metro-runtime` includes support for the fetch + eval runtime code required\n  // to support async imports. If it's not installed, we can't support async imports.\n  // If it is installed, the user MUST import it somewhere in their project.\n  // Expo Router automatically pulls this in, so we can check for it.\n  return resolveFrom.silent(projectRoot, '@expo/metro-runtime') != null;\n}\n\nfunction withDefaults({\n  mode = 'development',\n  minify = mode === 'production',\n  preserveEnvVars = env.EXPO_NO_CLIENT_ENV_VARS,\n  lazy,\n  ...props\n}: ExpoMetroOptions): ExpoMetroOptions {\n  if (props.bytecode) {\n    if (props.platform === 'web') {\n      throw new CommandError('Cannot use bytecode with the web platform');\n    }\n    if (props.engine !== 'hermes') {\n      throw new CommandError('Bytecode is only supported with the Hermes engine');\n    }\n  }\n\n  return {\n    mode,\n    minify,\n    preserveEnvVars,\n    lazy: !props.isExporting && lazy,\n    ...props,\n  };\n}\n\nexport function getBaseUrlFromExpoConfig(exp: ExpoConfig) {\n  return exp.experiments?.baseUrl?.trim().replace(/\\/+$/, '') ?? '';\n}\nexport function getAsyncRoutesFromExpoConfig(exp: ExpoConfig, mode: string, platform: string) {\n  let asyncRoutesSetting;\n\n  if (exp.extra?.router?.asyncRoutes) {\n    const asyncRoutes = exp.extra?.router?.asyncRoutes;\n    if (['boolean', 'string'].includes(typeof asyncRoutes)) {\n      asyncRoutesSetting = asyncRoutes;\n    } else if (typeof asyncRoutes === 'object') {\n      asyncRoutesSetting = asyncRoutes[platform] ?? asyncRoutes.default;\n    }\n  }\n\n  return [mode, true].includes(asyncRoutesSetting);\n}\n\nexport function getMetroDirectBundleOptionsForExpoConfig(\n  projectRoot: string,\n  exp: ExpoConfig,\n  options: Omit<ExpoMetroOptions, 'baseUrl' | 'routerRoot' | 'asyncRoutes'>\n): Partial<ExpoMetroBundleOptions> {\n  return getMetroDirectBundleOptions({\n    ...options,\n    baseUrl: getBaseUrlFromExpoConfig(exp),\n    routerRoot: getRouterDirectoryModuleIdWithManifest(projectRoot, exp),\n    asyncRoutes: getAsyncRoutesFromExpoConfig(exp, options.mode, options.platform),\n  });\n}\n\nexport function getMetroDirectBundleOptions(\n  options: ExpoMetroOptions\n): Partial<ExpoMetroBundleOptions> {\n  const {\n    mainModuleName,\n    platform,\n    mode,\n    minify,\n    environment,\n    serializerOutput,\n    serializerIncludeMaps,\n    bytecode,\n    lazy,\n    engine,\n    preserveEnvVars,\n    asyncRoutes,\n    baseUrl,\n    routerRoot,\n    isExporting,\n    inlineSourceMap,\n  } = withDefaults(options);\n\n  const dev = mode !== 'production';\n  const isHermes = engine === 'hermes';\n\n  if (isExporting) {\n    debug('Disabling lazy bundling for export build');\n    options.lazy = false;\n  }\n\n  let fakeSourceUrl: string | undefined;\n  let fakeSourceMapUrl: string | undefined;\n\n  // TODO: Upstream support to Metro for passing custom serializer options.\n  if (serializerIncludeMaps != null || serializerOutput != null) {\n    fakeSourceUrl = new URL(\n      createBundleUrlPath(options).replace(/^\\//, ''),\n      'http://localhost:8081'\n    ).toString();\n    if (serializerIncludeMaps) {\n      fakeSourceMapUrl = fakeSourceUrl.replace('.bundle?', '.map?');\n    }\n  }\n\n  const bundleOptions: Partial<ExpoMetroBundleOptions> = {\n    platform,\n    entryFile: mainModuleName,\n    dev,\n    minify: minify ?? !dev,\n    inlineSourceMap: inlineSourceMap ?? false,\n    lazy,\n    unstable_transformProfile: isHermes ? 'hermes-stable' : 'default',\n    customTransformOptions: {\n      __proto__: null,\n      engine,\n      preserveEnvVars,\n      asyncRoutes,\n      environment,\n      baseUrl,\n      routerRoot,\n      bytecode,\n    },\n    customResolverOptions: {\n      __proto__: null,\n      environment,\n    },\n    sourceMapUrl: fakeSourceMapUrl,\n    sourceUrl: fakeSourceUrl,\n    serializerOptions: {\n      output: serializerOutput,\n      includeSourceMaps: serializerIncludeMaps,\n    },\n  };\n\n  return bundleOptions;\n}\n\nexport function createBundleUrlPathFromExpoConfig(\n  projectRoot: string,\n  exp: ExpoConfig,\n  options: Omit<ExpoMetroOptions, 'baseUrl' | 'routerRoot'>\n): string {\n  return createBundleUrlPath({\n    ...options,\n    baseUrl: getBaseUrlFromExpoConfig(exp),\n    routerRoot: getRouterDirectoryModuleIdWithManifest(projectRoot, exp),\n  });\n}\n\nexport function createBundleUrlPath(options: ExpoMetroOptions): string {\n  const {\n    platform,\n    mainModuleName,\n    mode,\n    minify,\n    environment,\n    serializerOutput,\n    serializerIncludeMaps,\n    lazy,\n    bytecode,\n    engine,\n    preserveEnvVars,\n    asyncRoutes,\n    baseUrl,\n    routerRoot,\n    inlineSourceMap,\n    isExporting,\n  } = withDefaults(options);\n\n  const dev = String(mode !== 'production');\n  const queryParams = new URLSearchParams({\n    platform: encodeURIComponent(platform),\n    dev,\n    // TODO: Is this still needed?\n    hot: String(false),\n  });\n\n  // Lazy bundling must be disabled for bundle splitting to work.\n  if (!isExporting && lazy) {\n    queryParams.append('lazy', String(lazy));\n  }\n\n  if (inlineSourceMap) {\n    queryParams.append('inlineSourceMap', String(inlineSourceMap));\n  }\n\n  if (minify) {\n    queryParams.append('minify', String(minify));\n  }\n\n  // We split bytecode from the engine since you could technically use Hermes without bytecode.\n  // Hermes indicates the type of language features you want to transform out of the JS, whereas bytecode\n  // indicates whether you want to use the Hermes bytecode format.\n  if (engine) {\n    queryParams.append('transform.engine', engine);\n  }\n  if (bytecode) {\n    queryParams.append('transform.bytecode', String(bytecode));\n  }\n\n  if (asyncRoutes) {\n    queryParams.append('transform.asyncRoutes', String(asyncRoutes));\n  }\n  if (preserveEnvVars) {\n    queryParams.append('transform.preserveEnvVars', String(preserveEnvVars));\n  }\n  if (baseUrl) {\n    queryParams.append('transform.baseUrl', baseUrl);\n  }\n  if (routerRoot != null) {\n    queryParams.append('transform.routerRoot', routerRoot);\n  }\n\n  if (environment) {\n    queryParams.append('resolver.environment', environment);\n    queryParams.append('transform.environment', environment);\n  }\n\n  if (serializerOutput) {\n    queryParams.append('serializer.output', serializerOutput);\n  }\n  if (serializerIncludeMaps) {\n    queryParams.append('serializer.map', String(serializerIncludeMaps));\n  }\n\n  return `/${encodeURI(mainModuleName)}.bundle?${queryParams.toString()}`;\n}\n"], "names": ["shouldEnableAsyncImports", "getBaseUrlFromExpoConfig", "getAsyncRoutesFromExpoConfig", "getMetroDirectBundleOptionsForExpoConfig", "getMetroDirectBundleOptions", "createBundleUrlPathFromExpoConfig", "createBundleUrlPath", "debug", "require", "projectRoot", "env", "EXPO_NO_METRO_LAZY", "resolveFrom", "silent", "with<PERSON><PERSON><PERSON><PERSON>", "mode", "minify", "preserveEnvVars", "EXPO_NO_CLIENT_ENV_VARS", "lazy", "props", "bytecode", "platform", "CommandError", "engine", "isExporting", "exp", "experiments", "baseUrl", "trim", "replace", "asyncRoutesSetting", "extra", "router", "asyncRoutes", "includes", "default", "options", "routerRoot", "getRouterDirectoryModuleIdWithManifest", "mainModuleName", "environment", "serializerOutput", "serializerIncludeMaps", "inlineSourceMap", "dev", "isHermes", "fakeSourceUrl", "fakeSourceMapUrl", "URL", "toString", "bundleOptions", "entryFile", "unstable_transformProfile", "customTransformOptions", "__proto__", "customResolverOptions", "sourceMapUrl", "sourceUrl", "serializerOptions", "output", "includeSourceMaps", "String", "queryParams", "URLSearchParams", "encodeURIComponent", "hot", "append", "encodeURI"], "mappings": "AAAA;;;;QAwCgBA,wBAAwB,GAAxBA,wBAAwB;QAqCxBC,wBAAwB,GAAxBA,wBAAwB;QAGxBC,4BAA4B,GAA5BA,4BAA4B;QAe5BC,wCAAwC,GAAxCA,wCAAwC;QAaxCC,2BAA2B,GAA3BA,2BAA2B;QA6E3BC,iCAAiC,GAAjCA,iCAAiC;QAYjCC,mBAAmB,GAAnBA,mBAAmB;AAnMX,IAAA,YAAc,kCAAd,cAAc,EAAA;AAElB,IAAA,IAAoB,WAApB,oBAAoB,CAAA;AACX,IAAA,OAAuB,WAAvB,uBAAuB,CAAA;AACG,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;;;;;;AAExE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,AAAsB,AAAC;AAgCpE,SAASR,wBAAwB,CAACS,WAAmB,EAAW;IACrE,IAAIC,IAAG,IAAA,CAACC,kBAAkB,EAAE;QAC1B,OAAO,KAAK,CAAC;KACd;IAED,oFAAoF;IACpF,mFAAmF;IACnF,0EAA0E;IAC1E,mEAAmE;IACnE,OAAOC,YAAW,QAAA,CAACC,MAAM,CAACJ,WAAW,EAAE,qBAAqB,CAAC,IAAI,IAAI,CAAC;CACvE;AAED,SAASK,YAAY,CAAC,EACpBC,IAAI,EAAG,aAAa,CAAA,EACpBC,MAAM,EAAGD,IAAI,KAAK,YAAY,CAAA,EAC9BE,eAAe,EAAGP,IAAG,IAAA,CAACQ,uBAAuB,CAAA,EAC7CC,IAAI,CAAA,EACJ,GAAGC,KAAK,EACS,EAAoB;IACrC,IAAIA,KAAK,CAACC,QAAQ,EAAE;QAClB,IAAID,KAAK,CAACE,QAAQ,KAAK,KAAK,EAAE;YAC5B,MAAM,IAAIC,OAAY,aAAA,CAAC,2CAA2C,CAAC,CAAC;SACrE;QACD,IAAIH,KAAK,CAACI,MAAM,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAID,OAAY,aAAA,CAAC,mDAAmD,CAAC,CAAC;SAC7E;KACF;IAED,OAAO;QACLR,IAAI;QACJC,MAAM;QACNC,eAAe;QACfE,IAAI,EAAE,CAACC,KAAK,CAACK,WAAW,IAAIN,IAAI;QAChC,GAAGC,KAAK;KACT,CAAC;CACH;AAEM,SAASnB,wBAAwB,CAACyB,GAAe,EAAE;QACjDA,GAAe;QAAfA,IAAoD;IAA3D,OAAOA,CAAAA,IAAoD,GAApDA,CAAAA,GAAe,GAAfA,GAAG,CAACC,WAAW,SAAS,GAAxBD,KAAAA,CAAwB,GAAxBA,QAAAA,GAAe,CAAEE,OAAO,SAAA,GAAxBF,KAAAA,CAAwB,GAAxBA,KAA0BG,IAAI,EAAE,CAACC,OAAO,SAAS,EAAE,CAAC,YAApDJ,IAAoD,GAAI,EAAE,CAAC;CACnE;AACM,SAASxB,4BAA4B,CAACwB,GAAe,EAAEX,IAAY,EAAEO,QAAgB,EAAE;QAGxFI,GAAS;IAFb,IAAIK,kBAAkB,AAAC;IAEvB,IAAIL,CAAAA,GAAS,GAATA,GAAG,CAACM,KAAK,SAAQ,GAAjBN,KAAAA,CAAiB,GAAjBA,QAAAA,GAAS,CAAEO,MAAM,SAAA,GAAjBP,KAAAA,CAAiB,QAAEQ,WAAW,AAAb,EAAe;YACdR,IAAS;QAA7B,MAAMQ,WAAW,GAAGR,CAAAA,IAAS,GAATA,GAAG,CAACM,KAAK,SAAQ,GAAjBN,KAAAA,CAAiB,GAAjBA,QAAAA,IAAS,CAAEO,MAAM,SAAA,GAAjBP,KAAAA,CAAiB,QAAEQ,WAAW,AAAb,AAAc;QACnD,IAAI;YAAC,SAAS;YAAE,QAAQ;SAAC,CAACC,QAAQ,CAAC,OAAOD,WAAW,CAAC,EAAE;YACtDH,kBAAkB,GAAGG,WAAW,CAAC;SAClC,MAAM,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;gBACrBA,SAAqB;YAA1CH,kBAAkB,GAAGG,CAAAA,SAAqB,GAArBA,WAAW,CAACZ,QAAQ,CAAC,YAArBY,SAAqB,GAAIA,WAAW,CAACE,OAAO,CAAC;SACnE;KACF;IAED,OAAO;QAACrB,IAAI;QAAE,IAAI;KAAC,CAACoB,QAAQ,CAACJ,kBAAkB,CAAC,CAAC;CAClD;AAEM,SAAS5B,wCAAwC,CACtDM,WAAmB,EACnBiB,GAAe,EACfW,OAAyE,EACxC;IACjC,OAAOjC,2BAA2B,CAAC;QACjC,GAAGiC,OAAO;QACVT,OAAO,EAAE3B,wBAAwB,CAACyB,GAAG,CAAC;QACtCY,UAAU,EAAEC,CAAAA,GAAAA,OAAsC,AAAkB,CAAA,uCAAlB,CAAC9B,WAAW,EAAEiB,GAAG,CAAC;QACpEQ,WAAW,EAAEhC,4BAA4B,CAACwB,GAAG,EAAEW,OAAO,CAACtB,IAAI,EAAEsB,OAAO,CAACf,QAAQ,CAAC;KAC/E,CAAC,CAAC;CACJ;AAEM,SAASlB,2BAA2B,CACzCiC,OAAyB,EACQ;IACjC,MAAM,EACJG,cAAc,CAAA,EACdlB,QAAQ,CAAA,EACRP,IAAI,CAAA,EACJC,MAAM,CAAA,EACNyB,WAAW,CAAA,EACXC,gBAAgB,CAAA,EAChBC,qBAAqB,CAAA,EACrBtB,QAAQ,CAAA,EACRF,IAAI,CAAA,EACJK,MAAM,CAAA,EACNP,eAAe,CAAA,EACfiB,WAAW,CAAA,EACXN,OAAO,CAAA,EACPU,UAAU,CAAA,EACVb,WAAW,CAAA,EACXmB,eAAe,CAAA,IAChB,GAAG9B,YAAY,CAACuB,OAAO,CAAC,AAAC;IAE1B,MAAMQ,GAAG,GAAG9B,IAAI,KAAK,YAAY,AAAC;IAClC,MAAM+B,QAAQ,GAAGtB,MAAM,KAAK,QAAQ,AAAC;IAErC,IAAIC,WAAW,EAAE;QACflB,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAClD8B,OAAO,CAAClB,IAAI,GAAG,KAAK,CAAC;KACtB;IAED,IAAI4B,aAAa,AAAoB,AAAC;IACtC,IAAIC,gBAAgB,AAAoB,AAAC;IAEzC,yEAAyE;IACzE,IAAIL,qBAAqB,IAAI,IAAI,IAAID,gBAAgB,IAAI,IAAI,EAAE;QAC7DK,aAAa,GAAG,IAAIE,GAAG,CACrB3C,mBAAmB,CAAC+B,OAAO,CAAC,CAACP,OAAO,QAAQ,EAAE,CAAC,EAC/C,uBAAuB,CACxB,CAACoB,QAAQ,EAAE,CAAC;QACb,IAAIP,qBAAqB,EAAE;YACzBK,gBAAgB,GAAGD,aAAa,CAACjB,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SAC/D;KACF;IAED,MAAMqB,aAAa,GAAoC;QACrD7B,QAAQ;QACR8B,SAAS,EAAEZ,cAAc;QACzBK,GAAG;QACH7B,MAAM,EAAEA,MAAM,WAANA,MAAM,GAAI,CAAC6B,GAAG;QACtBD,eAAe,EAAEA,eAAe,WAAfA,eAAe,GAAI,KAAK;QACzCzB,IAAI;QACJkC,yBAAyB,EAAEP,QAAQ,GAAG,eAAe,GAAG,SAAS;QACjEQ,sBAAsB,EAAE;YACtBC,SAAS,EAAE,IAAI;YACf/B,MAAM;YACNP,eAAe;YACfiB,WAAW;YACXO,WAAW;YACXb,OAAO;YACPU,UAAU;YACVjB,QAAQ;SACT;QACDmC,qBAAqB,EAAE;YACrBD,SAAS,EAAE,IAAI;YACfd,WAAW;SACZ;QACDgB,YAAY,EAAET,gBAAgB;QAC9BU,SAAS,EAAEX,aAAa;QACxBY,iBAAiB,EAAE;YACjBC,MAAM,EAAElB,gBAAgB;YACxBmB,iBAAiB,EAAElB,qBAAqB;SACzC;KACF,AAAC;IAEF,OAAOQ,aAAa,CAAC;CACtB;AAEM,SAAS9C,iCAAiC,CAC/CI,WAAmB,EACnBiB,GAAe,EACfW,OAAyD,EACjD;IACR,OAAO/B,mBAAmB,CAAC;QACzB,GAAG+B,OAAO;QACVT,OAAO,EAAE3B,wBAAwB,CAACyB,GAAG,CAAC;QACtCY,UAAU,EAAEC,CAAAA,GAAAA,OAAsC,AAAkB,CAAA,uCAAlB,CAAC9B,WAAW,EAAEiB,GAAG,CAAC;KACrE,CAAC,CAAC;CACJ;AAEM,SAASpB,mBAAmB,CAAC+B,OAAyB,EAAU;IACrE,MAAM,EACJf,QAAQ,CAAA,EACRkB,cAAc,CAAA,EACdzB,IAAI,CAAA,EACJC,MAAM,CAAA,EACNyB,WAAW,CAAA,EACXC,gBAAgB,CAAA,EAChBC,qBAAqB,CAAA,EACrBxB,IAAI,CAAA,EACJE,QAAQ,CAAA,EACRG,MAAM,CAAA,EACNP,eAAe,CAAA,EACfiB,WAAW,CAAA,EACXN,OAAO,CAAA,EACPU,UAAU,CAAA,EACVM,eAAe,CAAA,EACfnB,WAAW,CAAA,IACZ,GAAGX,YAAY,CAACuB,OAAO,CAAC,AAAC;IAE1B,MAAMQ,GAAG,GAAGiB,MAAM,CAAC/C,IAAI,KAAK,YAAY,CAAC,AAAC;IAC1C,MAAMgD,WAAW,GAAG,IAAIC,eAAe,CAAC;QACtC1C,QAAQ,EAAE2C,kBAAkB,CAAC3C,QAAQ,CAAC;QACtCuB,GAAG;QACH,8BAA8B;QAC9BqB,GAAG,EAAEJ,MAAM,CAAC,KAAK,CAAC;KACnB,CAAC,AAAC;IAEH,+DAA+D;IAC/D,IAAI,CAACrC,WAAW,IAAIN,IAAI,EAAE;QACxB4C,WAAW,CAACI,MAAM,CAAC,MAAM,EAAEL,MAAM,CAAC3C,IAAI,CAAC,CAAC,CAAC;KAC1C;IAED,IAAIyB,eAAe,EAAE;QACnBmB,WAAW,CAACI,MAAM,CAAC,iBAAiB,EAAEL,MAAM,CAAClB,eAAe,CAAC,CAAC,CAAC;KAChE;IAED,IAAI5B,MAAM,EAAE;QACV+C,WAAW,CAACI,MAAM,CAAC,QAAQ,EAAEL,MAAM,CAAC9C,MAAM,CAAC,CAAC,CAAC;KAC9C;IAED,6FAA6F;IAC7F,uGAAuG;IACvG,gEAAgE;IAChE,IAAIQ,MAAM,EAAE;QACVuC,WAAW,CAACI,MAAM,CAAC,kBAAkB,EAAE3C,MAAM,CAAC,CAAC;KAChD;IACD,IAAIH,QAAQ,EAAE;QACZ0C,WAAW,CAACI,MAAM,CAAC,oBAAoB,EAAEL,MAAM,CAACzC,QAAQ,CAAC,CAAC,CAAC;KAC5D;IAED,IAAIa,WAAW,EAAE;QACf6B,WAAW,CAACI,MAAM,CAAC,uBAAuB,EAAEL,MAAM,CAAC5B,WAAW,CAAC,CAAC,CAAC;KAClE;IACD,IAAIjB,eAAe,EAAE;QACnB8C,WAAW,CAACI,MAAM,CAAC,2BAA2B,EAAEL,MAAM,CAAC7C,eAAe,CAAC,CAAC,CAAC;KAC1E;IACD,IAAIW,OAAO,EAAE;QACXmC,WAAW,CAACI,MAAM,CAAC,mBAAmB,EAAEvC,OAAO,CAAC,CAAC;KAClD;IACD,IAAIU,UAAU,IAAI,IAAI,EAAE;QACtByB,WAAW,CAACI,MAAM,CAAC,sBAAsB,EAAE7B,UAAU,CAAC,CAAC;KACxD;IAED,IAAIG,WAAW,EAAE;QACfsB,WAAW,CAACI,MAAM,CAAC,sBAAsB,EAAE1B,WAAW,CAAC,CAAC;QACxDsB,WAAW,CAACI,MAAM,CAAC,uBAAuB,EAAE1B,WAAW,CAAC,CAAC;KAC1D;IAED,IAAIC,gBAAgB,EAAE;QACpBqB,WAAW,CAACI,MAAM,CAAC,mBAAmB,EAAEzB,gBAAgB,CAAC,CAAC;KAC3D;IACD,IAAIC,qBAAqB,EAAE;QACzBoB,WAAW,CAACI,MAAM,CAAC,gBAAgB,EAAEL,MAAM,CAACnB,qBAAqB,CAAC,CAAC,CAAC;KACrE;IAED,OAAO,CAAC,CAAC,EAAEyB,SAAS,CAAC5B,cAAc,CAAC,CAAC,QAAQ,EAAEuB,WAAW,CAACb,QAAQ,EAAE,CAAC,CAAC,CAAC;CACzE"}