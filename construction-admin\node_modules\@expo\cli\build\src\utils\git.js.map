{"version": 3, "sources": ["../../../src/utils/git.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport chalk from 'chalk';\n\nimport { env } from './env';\nimport { isInteractive } from './interactive';\nimport { confirmAsync } from './prompts';\nimport * as Log from '../log';\n\nexport async function maybeBailOnGitStatusAsync(): Promise<boolean> {\n  if (env.EXPO_NO_GIT_STATUS) {\n    Log.warn(\n      'Git status is dirty but the command will continue because EXPO_NO_GIT_STATUS is enabled...'\n    );\n    return false;\n  }\n  const isGitStatusClean = await validateGitStatusAsync();\n\n  // Give people a chance to bail out if git working tree is dirty\n  if (!isGitStatusClean) {\n    if (!isInteractive()) {\n      Log.warn(\n        `Git status is dirty but the command will continue because the terminal is not interactive.`\n      );\n      return false;\n    }\n\n    Log.log();\n    const answer = await confirmAsync({\n      message: `Would you like to proceed?`,\n    });\n\n    if (!answer) {\n      return true;\n    }\n\n    Log.log();\n  }\n  return false;\n}\n\nexport async function validateGitStatusAsync(): Promise<boolean> {\n  let workingTreeStatus = 'unknown';\n  try {\n    const result = await spawnAsync('git', ['status', '--porcelain']);\n    workingTreeStatus = result.stdout === '' ? 'clean' : 'dirty';\n  } catch {\n    // Maybe git is not installed?\n    // Maybe this project is not using git?\n  }\n\n  if (workingTreeStatus === 'clean') {\n    Log.log(`Your git working tree is ${chalk.green('clean')}`);\n    Log.log('To revert the changes after this command completes, you can run the following:');\n    Log.log('  git clean --force && git reset --hard');\n    return true;\n  } else if (workingTreeStatus === 'dirty') {\n    Log.log(`${chalk.bold('Warning!')} Your git working tree is ${chalk.red('dirty')}.`);\n    Log.log(\n      `It's recommended to ${chalk.bold(\n        'commit all your changes before proceeding'\n      )}, so you can revert the changes made by this command if necessary.`\n    );\n  } else {\n    Log.log(\"We couldn't find a git repository in your project directory.\");\n    Log.log(\"It's recommended to back up your project before proceeding.\");\n  }\n\n  return false;\n}\n"], "names": ["maybeBailOnGitStatusAsync", "validateGitStatusAsync", "Log", "env", "EXPO_NO_GIT_STATUS", "warn", "isGitStatusClean", "isInteractive", "log", "answer", "<PERSON><PERSON><PERSON>", "message", "workingTreeStatus", "result", "spawnAsync", "stdout", "chalk", "green", "bold", "red"], "mappings": "AAAA;;;;QAQsBA,yBAAyB,GAAzBA,yBAAyB;QAgCzBC,sBAAsB,GAAtBA,sBAAsB;AAxCrB,IAAA,WAAmB,kCAAnB,mBAAmB,EAAA;AACxB,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEL,IAAA,IAAO,WAAP,OAAO,CAAA;AACG,IAAA,YAAe,WAAf,eAAe,CAAA;AAChB,IAAA,QAAW,WAAX,WAAW,CAAA;AAC5BC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;;;;;;;;;;;;;;;;;;;;;;;;;;;AAER,eAAeF,yBAAyB,GAAqB;IAClE,IAAIG,IAAG,IAAA,CAACC,kBAAkB,EAAE;QAC1BF,GAAG,CAACG,IAAI,CACN,4FAA4F,CAC7F,CAAC;QACF,OAAO,KAAK,CAAC;KACd;IACD,MAAMC,gBAAgB,GAAG,MAAML,sBAAsB,EAAE,AAAC;IAExD,gEAAgE;IAChE,IAAI,CAACK,gBAAgB,EAAE;QACrB,IAAI,CAACC,CAAAA,GAAAA,YAAa,AAAE,CAAA,cAAF,EAAE,EAAE;YACpBL,GAAG,CAACG,IAAI,CACN,CAAC,0FAA0F,CAAC,CAC7F,CAAC;YACF,OAAO,KAAK,CAAC;SACd;QAEDH,GAAG,CAACM,GAAG,EAAE,CAAC;QACV,MAAMC,MAAM,GAAG,MAAMC,CAAAA,GAAAA,QAAY,AAE/B,CAAA,aAF+B,CAAC;YAChCC,OAAO,EAAE,CAAC,0BAA0B,CAAC;SACtC,CAAC,AAAC;QAEH,IAAI,CAACF,MAAM,EAAE;YACX,OAAO,IAAI,CAAC;SACb;QAEDP,GAAG,CAACM,GAAG,EAAE,CAAC;KACX;IACD,OAAO,KAAK,CAAC;CACd;AAEM,eAAeP,sBAAsB,GAAqB;IAC/D,IAAIW,iBAAiB,GAAG,SAAS,AAAC;IAClC,IAAI;QACF,MAAMC,MAAM,GAAG,MAAMC,CAAAA,GAAAA,WAAU,AAAkC,CAAA,QAAlC,CAAC,KAAK,EAAE;YAAC,QAAQ;YAAE,aAAa;SAAC,CAAC,AAAC;QAClEF,iBAAiB,GAAGC,MAAM,CAACE,MAAM,KAAK,EAAE,GAAG,OAAO,GAAG,OAAO,CAAC;KAC9D,CAAC,OAAM;IACN,8BAA8B;IAC9B,uCAAuC;KACxC;IAED,IAAIH,iBAAiB,KAAK,OAAO,EAAE;QACjCV,GAAG,CAACM,GAAG,CAAC,CAAC,yBAAyB,EAAEQ,MAAK,QAAA,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5Df,GAAG,CAACM,GAAG,CAAC,gFAAgF,CAAC,CAAC;QAC1FN,GAAG,CAACM,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;KACb,MAAM,IAAII,iBAAiB,KAAK,OAAO,EAAE;QACxCV,GAAG,CAACM,GAAG,CAAC,CAAC,EAAEQ,MAAK,QAAA,CAACE,IAAI,CAAC,UAAU,CAAC,CAAC,0BAA0B,EAAEF,MAAK,QAAA,CAACG,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrFjB,GAAG,CAACM,GAAG,CACL,CAAC,oBAAoB,EAAEQ,MAAK,QAAA,CAACE,IAAI,CAC/B,2CAA2C,CAC5C,CAAC,kEAAkE,CAAC,CACtE,CAAC;KACH,MAAM;QACLhB,GAAG,CAACM,GAAG,CAAC,8DAA8D,CAAC,CAAC;QACxEN,GAAG,CAACM,GAAG,CAAC,6DAA6D,CAAC,CAAC;KACxE;IAED,OAAO,KAAK,CAAC;CACd"}