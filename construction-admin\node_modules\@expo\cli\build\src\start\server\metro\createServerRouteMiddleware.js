"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.createRouteHandlerMiddleware = createRouteHandlerMiddleware;
var _requireFromString = _interopRequireDefault(require("require-from-string"));
var _resolve = _interopRequireDefault(require("resolve"));
var _resolveFrom = _interopRequireDefault(require("resolve-from"));
var _util = require("util");
var _metroBundlerDevServer = require("./MetroBundlerDevServer");
var _bundleApiRoutes = require("./bundleApiRoutes");
var _fetchRouterManifest = require("./fetchRouterManifest");
var _metroErrorInterface = require("./metroErrorInterface");
var _router = require("./router");
var _log = require("../../../log");
var _errors = require("../../../utils/errors");
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const debug = require("debug")("expo:start:server:metro");
const resolveAsync = (0, _util).promisify(_resolve.default);
function createRouteHandlerMiddleware(projectRoot, options) {
    if (!_resolveFrom.default.silent(projectRoot, "expo-router")) {
        throw new _errors.CommandError("static and server rendering requires the expo-router package to be installed in your project.");
    }
    const { ExpoResponse  } = require("@expo/server");
    const { createRequestHandler  } = require("@expo/server/build/vendor/http");
    return createRequestHandler({
        build: ""
    }, {
        async getRoutesManifest () {
            const manifest = await (0, _fetchRouterManifest).fetchManifest(projectRoot, options);
            debug("manifest", manifest);
            // NOTE: no app dir if null
            // TODO: Redirect to 404 page
            return manifest != null ? manifest : {
                // Support the onboarding screen if there's no manifest
                htmlRoutes: [
                    {
                        file: "index.js",
                        page: "/index",
                        routeKeys: {},
                        namedRegex: /^\/(?:index)?\/?$/i
                    }, 
                ],
                apiRoutes: [],
                notFoundRoutes: []
            };
        },
        async getHtml (request) {
            try {
                const { content  } = await options.getStaticPageAsync(request.url);
                return content;
            } catch (error) {
                // Forward the Metro server response as-is. It won't be pretty, but at least it will be accurate.
                if (error instanceof _metroBundlerDevServer.ForwardHtmlError) {
                    return new ExpoResponse(error.html, {
                        status: error.statusCode,
                        headers: {
                            "Content-Type": "text/html"
                        }
                    });
                }
                try {
                    return new ExpoResponse(await (0, _metroErrorInterface).getErrorOverlayHtmlAsync({
                        error,
                        projectRoot,
                        routerRoot: options.routerRoot
                    }), {
                        status: 500,
                        headers: {
                            "Content-Type": "text/html"
                        }
                    });
                } catch (staticError) {
                    debug("Failed to render static error overlay:", staticError);
                    // Fallback error for when Expo Router is misconfigured in the project.
                    return new ExpoResponse("<span><h3>Internal Error:</h3><b>Project is not setup correctly for static rendering (check terminal for more info):</b><br/>" + error.message + "<br/><br/>" + staticError.message + "</span>", {
                        status: 500,
                        headers: {
                            "Content-Type": "text/html"
                        }
                    });
                }
            }
        },
        logApiRouteExecutionError (error) {
            (0, _metroErrorInterface).logMetroError(projectRoot, {
                error
            });
        },
        async getApiRoute (route) {
            var ref;
            const { exp  } = options.config;
            if (((ref = exp.web) == null ? void 0 : ref.output) !== "server") {
                (0, _router).warnInvalidWebOutput();
            }
            const resolvedFunctionPath = await resolveAsync(route.page, {
                extensions: [
                    ".js",
                    ".jsx",
                    ".ts",
                    ".tsx"
                ],
                basedir: options.appDir
            });
            const middlewareContents = await (0, _bundleApiRoutes).bundleApiRoute(projectRoot, resolvedFunctionPath, options);
            if (!middlewareContents) {
                // TODO: Error handling
                return null;
            }
            try {
                debug(`Bundling middleware at: ${resolvedFunctionPath}`);
                return (0, _requireFromString).default(middlewareContents.src, middlewareContents.filename);
            } catch (error) {
                if (error instanceof Error) {
                    await (0, _metroErrorInterface).logMetroErrorAsync({
                        projectRoot,
                        error
                    });
                } else {
                    _log.Log.error("Failed to load middleware: " + error);
                }
                return new ExpoResponse("Failed to load middleware: " + resolvedFunctionPath + "\n\n" + error.message, {
                    status: 500,
                    headers: {
                        "Content-Type": "text/html"
                    }
                });
            }
        }
    });
}

//# sourceMappingURL=createServerRouteMiddleware.js.map