{"version": 3, "sources": ["../../../../../src/start/server/metro/MetroTerminalReporter.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport { Terminal } from 'metro-core';\nimport path from 'path';\n\nimport { logWarning, TerminalReporter } from './TerminalReporter';\nimport {\n  BuildPhase,\n  BundleDetails,\n  BundleProgress,\n  SnippetError,\n  TerminalReportableEvent,\n} from './TerminalReporter.types';\nimport { NODE_STDLIB_MODULES } from './externals';\nimport { learnMore } from '../../../utils/link';\n\nconst MAX_PROGRESS_BAR_CHAR_WIDTH = 16;\nconst DARK_BLOCK_CHAR = '\\u2593';\nconst LIGHT_BLOCK_CHAR = '\\u2591';\n/**\n * Extends the default Metro logger and adds some additional features.\n * Also removes the giant Metro logo from the output.\n */\nexport class MetroTerminalReporter extends TerminalReporter {\n  constructor(\n    public projectRoot: string,\n    terminal: Terminal\n  ) {\n    super(terminal);\n  }\n\n  // Used for testing\n  _getElapsedTime(startTime: number): number {\n    return Date.now() - startTime;\n  }\n  /**\n   * Extends the bundle progress to include the current platform that we're bundling.\n   *\n   * @returns `iOS path/to/bundle.js ▓▓▓▓▓░░░░░░░░░░░ 36.6% (4790/7922)`\n   */\n  _getBundleStatusMessage(progress: BundleProgress, phase: BuildPhase): string {\n    const env = getEnvironmentForBuildDetails(progress.bundleDetails);\n    const platform = env || getPlatformTagForBuildDetails(progress.bundleDetails);\n    const inProgress = phase === 'in_progress';\n\n    const localPath = progress.bundleDetails.entryFile.startsWith(path.sep)\n      ? path.relative(this.projectRoot, progress.bundleDetails.entryFile)\n      : progress.bundleDetails.entryFile;\n\n    if (!inProgress) {\n      const status = phase === 'done' ? `Bundled ` : `Bundling failed `;\n      const color = phase === 'done' ? chalk.green : chalk.red;\n\n      const startTime = this._bundleTimers.get(progress.bundleDetails.buildID!);\n      const time = startTime != null ? chalk.dim(this._getElapsedTime(startTime) + 'ms') : '';\n      // iOS Bundled 150ms\n      return color(platform + status) + time + chalk.reset.dim(' (' + localPath + ')');\n    }\n\n    const filledBar = Math.floor(progress.ratio * MAX_PROGRESS_BAR_CHAR_WIDTH);\n\n    const _progress = inProgress\n      ? chalk.green.bgGreen(DARK_BLOCK_CHAR.repeat(filledBar)) +\n        chalk.bgWhite.white(LIGHT_BLOCK_CHAR.repeat(MAX_PROGRESS_BAR_CHAR_WIDTH - filledBar)) +\n        chalk.bold(` ${(100 * progress.ratio).toFixed(1).padStart(4)}% `) +\n        chalk.dim(\n          `(${progress.transformedFileCount\n            .toString()\n            .padStart(progress.totalFileCount.toString().length)}/${progress.totalFileCount})`\n        )\n      : '';\n\n    return (\n      platform +\n      chalk.reset.dim(`${path.dirname(localPath)}/`) +\n      chalk.bold(path.basename(localPath)) +\n      ' ' +\n      _progress\n    );\n  }\n\n  _logInitializing(port: number, hasReducedPerformance: boolean): void {\n    // Don't print a giant logo...\n    this.terminal.log('Starting Metro Bundler');\n  }\n\n  shouldFilterClientLog(event: {\n    type: 'client_log';\n    level: 'trace' | 'info' | 'warn' | 'log' | 'group' | 'groupCollapsed' | 'groupEnd' | 'debug';\n    data: unknown[];\n  }): boolean {\n    return isAppRegistryStartupMessage(event.data);\n  }\n\n  shouldFilterBundleEvent(event: TerminalReportableEvent): boolean {\n    return 'bundleDetails' in event && event.bundleDetails?.bundleType === 'map';\n  }\n\n  /** Print the cache clear message. */\n  transformCacheReset(): void {\n    logWarning(\n      this.terminal,\n      chalk`Bundler cache is empty, rebuilding {dim (this may take a minute)}`\n    );\n  }\n\n  /** One of the first logs that will be printed */\n  dependencyGraphLoading(hasReducedPerformance: boolean): void {\n    // this.terminal.log('Dependency graph is loading...');\n    if (hasReducedPerformance) {\n      // Extends https://github.com/facebook/metro/blob/347b1d7ed87995d7951aaa9fd597c04b06013dac/packages/metro/src/lib/TerminalReporter.js#L283-L290\n      this.terminal.log(\n        chalk.red(\n          [\n            'Metro is operating with reduced performance.',\n            'Please fix the problem above and restart Metro.',\n          ].join('\\n')\n        )\n      );\n    }\n  }\n\n  _logBundlingError(error: SnippetError): void {\n    const moduleResolutionError = formatUsingNodeStandardLibraryError(this.projectRoot, error);\n    const cause = error.cause as undefined | { _expoImportStack?: string };\n    if (moduleResolutionError) {\n      let message = maybeAppendCodeFrame(moduleResolutionError, error.message);\n      if (cause?._expoImportStack) {\n        message += `\\n\\n${cause?._expoImportStack}`;\n      }\n      return this.terminal.log(message);\n    }\n    if (cause?._expoImportStack) {\n      error.message += `\\n\\n${cause._expoImportStack}`;\n    }\n    return super._logBundlingError(error);\n  }\n}\n\n/**\n * Formats an error where the user is attempting to import a module from the Node.js standard library.\n * Exposed for testing.\n *\n * @param error\n * @returns error message or null if not a module resolution error\n */\nexport function formatUsingNodeStandardLibraryError(\n  projectRoot: string,\n  error: SnippetError\n): string | null {\n  if (!error.message) {\n    return null;\n  }\n  const { targetModuleName, originModulePath } = error;\n  if (!targetModuleName || !originModulePath) {\n    return null;\n  }\n  const relativePath = path.relative(projectRoot, originModulePath);\n\n  const DOCS_PAGE_URL =\n    'https://docs.expo.dev/workflow/using-libraries/#using-third-party-libraries';\n\n  if (isNodeStdLibraryModule(targetModuleName)) {\n    if (originModulePath.includes('node_modules')) {\n      return [\n        `The package at \"${chalk.bold(\n          relativePath\n        )}\" attempted to import the Node standard library module \"${chalk.bold(\n          targetModuleName\n        )}\".`,\n        `It failed because the native React runtime does not include the Node standard library.`,\n        learnMore(DOCS_PAGE_URL),\n      ].join('\\n');\n    } else {\n      return [\n        `You attempted to import the Node standard library module \"${chalk.bold(\n          targetModuleName\n        )}\" from \"${chalk.bold(relativePath)}\".`,\n        `It failed because the native React runtime does not include the Node standard library.`,\n        learnMore(DOCS_PAGE_URL),\n      ].join('\\n');\n    }\n  }\n  return `Unable to resolve \"${targetModuleName}\" from \"${relativePath}\"`;\n}\n\nexport function isNodeStdLibraryModule(moduleName: string): boolean {\n  return /^node:/.test(moduleName) || NODE_STDLIB_MODULES.includes(moduleName);\n}\n\n/** If the code frame can be found then append it to the existing message.  */\nfunction maybeAppendCodeFrame(message: string, rawMessage: string): string {\n  const codeFrame = stripMetroInfo(rawMessage);\n  if (codeFrame) {\n    message += '\\n' + codeFrame;\n  }\n  return message;\n}\n\n/**\n * Remove the Metro cache clearing steps if they exist.\n * In future versions we won't need this.\n * Returns the remaining code frame logs.\n */\nexport function stripMetroInfo(errorMessage: string): string | null {\n  // Newer versions of Metro don't include the list.\n  if (!errorMessage.includes('4. Remove the cache')) {\n    return null;\n  }\n  const lines = errorMessage.split('\\n');\n  const index = lines.findIndex((line) => line.includes('4. Remove the cache'));\n  if (index === -1) {\n    return null;\n  }\n  return lines.slice(index + 1).join('\\n');\n}\n\n/** @returns if the message matches the initial startup log */\nfunction isAppRegistryStartupMessage(body: any[]): boolean {\n  return (\n    body.length === 1 &&\n    (/^Running application \"main\" with appParams:/.test(body[0]) ||\n      /^Running \"main\" with \\{/.test(body[0]))\n  );\n}\n\n/** @returns platform specific tag for a `BundleDetails` object */\nfunction getPlatformTagForBuildDetails(bundleDetails?: BundleDetails | null): string {\n  const platform = bundleDetails?.platform ?? null;\n  if (platform) {\n    const formatted = { ios: 'iOS', android: 'Android', web: 'Web' }[platform] || platform;\n    return `${chalk.bold(formatted)} `;\n  }\n\n  return '';\n}\n/** @returns platform specific tag for a `BundleDetails` object */\nfunction getEnvironmentForBuildDetails(bundleDetails?: BundleDetails | null): string {\n  // Expo CLI will pass `customTransformOptions.environment = 'node'` when bundling for the server.\n  const env = bundleDetails?.customTransformOptions?.environment ?? null;\n  if (env === 'node') {\n    return chalk.bold('λ') + ' ';\n  }\n\n  return '';\n}\n"], "names": ["formatUsingNodeStandardLibraryError", "isNodeStdLibraryModule", "stripMetroInfo", "MAX_PROGRESS_BAR_CHAR_WIDTH", "DARK_BLOCK_CHAR", "LIGHT_BLOCK_CHAR", "MetroTerminalReporter", "TerminalReporter", "constructor", "projectRoot", "terminal", "_getElapsedTime", "startTime", "Date", "now", "_getBundleStatusMessage", "progress", "phase", "env", "getEnvironmentForBuildDetails", "bundleDetails", "platform", "getPlatformTagForBuildDetails", "inProgress", "localPath", "entryFile", "startsWith", "path", "sep", "relative", "status", "color", "chalk", "green", "red", "_bundleTimers", "get", "buildID", "time", "dim", "reset", "<PERSON><PERSON><PERSON>", "Math", "floor", "ratio", "_progress", "bgGreen", "repeat", "bgWhite", "white", "bold", "toFixed", "padStart", "transformedFileCount", "toString", "totalFileCount", "length", "dirname", "basename", "_logInitializing", "port", "hasReducedPerformance", "log", "shouldFilterClientLog", "event", "isAppRegistryStartupMessage", "data", "shouldFilterBundleEvent", "bundleType", "transformCacheReset", "logWarning", "dependencyGraphLoading", "join", "_logBundlingError", "error", "moduleResolutionError", "cause", "message", "maybeAppendCodeFrame", "_expoImportStack", "targetModuleName", "originModulePath", "relativePath", "DOCS_PAGE_URL", "includes", "learnMore", "moduleName", "test", "NODE_STDLIB_MODULES", "rawMessage", "codeFrame", "errorMessage", "lines", "split", "index", "findIndex", "line", "slice", "body", "formatted", "ios", "android", "web", "customTransformOptions", "environment"], "mappings": "AAAA;;;;QAiJgBA,mCAAmC,GAAnCA,mCAAmC;QAwCnCC,sBAAsB,GAAtBA,sBAAsB;QAkBtBC,cAAc,GAAdA,cAAc;AA3MZ,IAAA,MAAO,kCAAP,OAAO,EAAA;AAER,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEsB,IAAA,iBAAoB,WAApB,oBAAoB,CAAA;AAQ7B,IAAA,UAAa,WAAb,aAAa,CAAA;AACvB,IAAA,KAAqB,WAArB,qBAAqB,CAAA;;;;;;AAE/C,MAAMC,2BAA2B,GAAG,EAAE,AAAC;AACvC,MAAMC,eAAe,GAAG,QAAQ,AAAC;AACjC,MAAMC,gBAAgB,GAAG,QAAQ,AAAC;AAK3B,MAAMC,qBAAqB,SAASC,iBAAgB,iBAAA;IACzDC,YACSC,WAAmB,EAC1BC,QAAkB,CAClB;QACA,KAAK,CAACA,QAAQ,CAAC,CAAC;aAHTD,WAAmB,GAAnBA,WAAmB;KAI3B;IAED,mBAAmB;IACnBE,eAAe,CAACC,SAAiB,EAAU;QACzC,OAAOC,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS,CAAC;KAC/B;IACD;;;;KAIG,CACHG,uBAAuB,CAACC,QAAwB,EAAEC,KAAiB,EAAU;QAC3E,MAAMC,GAAG,GAAGC,6BAA6B,CAACH,QAAQ,CAACI,aAAa,CAAC,AAAC;QAClE,MAAMC,QAAQ,GAAGH,GAAG,IAAII,6BAA6B,CAACN,QAAQ,CAACI,aAAa,CAAC,AAAC;QAC9E,MAAMG,UAAU,GAAGN,KAAK,KAAK,aAAa,AAAC;QAE3C,MAAMO,SAAS,GAAGR,QAAQ,CAACI,aAAa,CAACK,SAAS,CAACC,UAAU,CAACC,KAAI,QAAA,CAACC,GAAG,CAAC,GACnED,KAAI,QAAA,CAACE,QAAQ,CAAC,IAAI,CAACpB,WAAW,EAAEO,QAAQ,CAACI,aAAa,CAACK,SAAS,CAAC,GACjET,QAAQ,CAACI,aAAa,CAACK,SAAS,AAAC;QAErC,IAAI,CAACF,UAAU,EAAE;YACf,MAAMO,MAAM,GAAGb,KAAK,KAAK,MAAM,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,AAAC;YAClE,MAAMc,KAAK,GAAGd,KAAK,KAAK,MAAM,GAAGe,MAAK,QAAA,CAACC,KAAK,GAAGD,MAAK,QAAA,CAACE,GAAG,AAAC;YAEzD,MAAMtB,SAAS,GAAG,IAAI,CAACuB,aAAa,CAACC,GAAG,CAACpB,QAAQ,CAACI,aAAa,CAACiB,OAAO,CAAE,AAAC;YAC1E,MAAMC,IAAI,GAAG1B,SAAS,IAAI,IAAI,GAAGoB,MAAK,QAAA,CAACO,GAAG,CAAC,IAAI,CAAC5B,eAAe,CAACC,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,AAAC;YACxF,oBAAoB;YACpB,OAAOmB,KAAK,CAACV,QAAQ,GAAGS,MAAM,CAAC,GAAGQ,IAAI,GAAGN,MAAK,QAAA,CAACQ,KAAK,CAACD,GAAG,CAAC,IAAI,GAAGf,SAAS,GAAG,GAAG,CAAC,CAAC;SAClF;QAED,MAAMiB,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC3B,QAAQ,CAAC4B,KAAK,GAAGzC,2BAA2B,CAAC,AAAC;QAE3E,MAAM0C,SAAS,GAAGtB,UAAU,GACxBS,MAAK,QAAA,CAACC,KAAK,CAACa,OAAO,CAAC1C,eAAe,CAAC2C,MAAM,CAACN,SAAS,CAAC,CAAC,GACtDT,MAAK,QAAA,CAACgB,OAAO,CAACC,KAAK,CAAC5C,gBAAgB,CAAC0C,MAAM,CAAC5C,2BAA2B,GAAGsC,SAAS,CAAC,CAAC,GACrFT,MAAK,QAAA,CAACkB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAGlC,QAAQ,CAAC4B,KAAK,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GACjEpB,MAAK,QAAA,CAACO,GAAG,CACP,CAAC,CAAC,EAAEvB,QAAQ,CAACqC,oBAAoB,CAC9BC,QAAQ,EAAE,CACVF,QAAQ,CAACpC,QAAQ,CAACuC,cAAc,CAACD,QAAQ,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC,EAAExC,QAAQ,CAACuC,cAAc,CAAC,CAAC,CAAC,CACrF,GACD,EAAE,AAAC;QAEP,OACElC,QAAQ,GACRW,MAAK,QAAA,CAACQ,KAAK,CAACD,GAAG,CAAC,CAAC,EAAEZ,KAAI,QAAA,CAAC8B,OAAO,CAACjC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAC9CQ,MAAK,QAAA,CAACkB,IAAI,CAACvB,KAAI,QAAA,CAAC+B,QAAQ,CAAClC,SAAS,CAAC,CAAC,GACpC,GAAG,GACHqB,SAAS,CACT;KACH;IAEDc,gBAAgB,CAACC,IAAY,EAAEC,qBAA8B,EAAQ;QACnE,8BAA8B;QAC9B,IAAI,CAACnD,QAAQ,CAACoD,GAAG,CAAC,wBAAwB,CAAC,CAAC;KAC7C;IAEDC,qBAAqB,CAACC,KAIrB,EAAW;QACV,OAAOC,2BAA2B,CAACD,KAAK,CAACE,IAAI,CAAC,CAAC;KAChD;IAEDC,uBAAuB,CAACH,KAA8B,EAAW;YAC5BA,GAAmB;QAAtD,OAAO,eAAe,IAAIA,KAAK,IAAIA,CAAAA,CAAAA,GAAmB,GAAnBA,KAAK,CAAC5C,aAAa,SAAY,GAA/B4C,KAAAA,CAA+B,GAA/BA,GAAmB,CAAEI,UAAU,CAAA,KAAK,KAAK,CAAC;KAC9E;IAED,qCAAqC,CACrCC,mBAAmB,GAAS;QAC1BC,CAAAA,GAAAA,iBAAU,AAGT,CAAA,WAHS,CACR,IAAI,CAAC5D,QAAQ,EACbsB,MAAK,QAAA,CAAC,iEAAiE,CAAC,CACzE,CAAC;KACH;IAED,iDAAiD,CACjDuC,sBAAsB,CAACV,qBAA8B,EAAQ;QAC3D,uDAAuD;QACvD,IAAIA,qBAAqB,EAAE;YACzB,+IAA+I;YAC/I,IAAI,CAACnD,QAAQ,CAACoD,GAAG,CACf9B,MAAK,QAAA,CAACE,GAAG,CACP;gBACE,8CAA8C;gBAC9C,iDAAiD;aAClD,CAACsC,IAAI,CAAC,IAAI,CAAC,CACb,CACF,CAAC;SACH;KACF;IAEDC,iBAAiB,CAACC,KAAmB,EAAQ;QAC3C,MAAMC,qBAAqB,GAAG3E,mCAAmC,CAAC,IAAI,CAACS,WAAW,EAAEiE,KAAK,CAAC,AAAC;QAC3F,MAAME,KAAK,GAAGF,KAAK,CAACE,KAAK,AAA6C,AAAC;QACvE,IAAID,qBAAqB,EAAE;YACzB,IAAIE,OAAO,GAAGC,oBAAoB,CAACH,qBAAqB,EAAED,KAAK,CAACG,OAAO,CAAC,AAAC;YACzE,IAAID,KAAK,QAAkB,GAAvBA,KAAAA,CAAuB,GAAvBA,KAAK,CAAEG,gBAAgB,EAAE;gBAC3BF,OAAO,IAAI,CAAC,IAAI,EAAED,KAAK,QAAkB,GAAvBA,KAAAA,CAAuB,GAAvBA,KAAK,CAAEG,gBAAgB,CAAC,CAAC,CAAC;aAC7C;YACD,OAAO,IAAI,CAACrE,QAAQ,CAACoD,GAAG,CAACe,OAAO,CAAC,CAAC;SACnC;QACD,IAAID,KAAK,QAAkB,GAAvBA,KAAAA,CAAuB,GAAvBA,KAAK,CAAEG,gBAAgB,EAAE;YAC3BL,KAAK,CAACG,OAAO,IAAI,CAAC,IAAI,EAAED,KAAK,CAACG,gBAAgB,CAAC,CAAC,CAAC;SAClD;QACD,OAAO,KAAK,CAACN,iBAAiB,CAACC,KAAK,CAAC,CAAC;KACvC;CACF;QAlHYpE,qBAAqB,GAArBA,qBAAqB;AA2H3B,SAASN,mCAAmC,CACjDS,WAAmB,EACnBiE,KAAmB,EACJ;IACf,IAAI,CAACA,KAAK,CAACG,OAAO,EAAE;QAClB,OAAO,IAAI,CAAC;KACb;IACD,MAAM,EAAEG,gBAAgB,CAAA,EAAEC,gBAAgB,CAAA,EAAE,GAAGP,KAAK,AAAC;IACrD,IAAI,CAACM,gBAAgB,IAAI,CAACC,gBAAgB,EAAE;QAC1C,OAAO,IAAI,CAAC;KACb;IACD,MAAMC,YAAY,GAAGvD,KAAI,QAAA,CAACE,QAAQ,CAACpB,WAAW,EAAEwE,gBAAgB,CAAC,AAAC;IAElE,MAAME,aAAa,GACjB,6EAA6E,AAAC;IAEhF,IAAIlF,sBAAsB,CAAC+E,gBAAgB,CAAC,EAAE;QAC5C,IAAIC,gBAAgB,CAACG,QAAQ,CAAC,cAAc,CAAC,EAAE;YAC7C,OAAO;gBACL,CAAC,gBAAgB,EAAEpD,MAAK,QAAA,CAACkB,IAAI,CAC3BgC,YAAY,CACb,CAAC,wDAAwD,EAAElD,MAAK,QAAA,CAACkB,IAAI,CACpE8B,gBAAgB,CACjB,CAAC,EAAE,CAAC;gBACL,CAAC,sFAAsF,CAAC;gBACxFK,CAAAA,GAAAA,KAAS,AAAe,CAAA,UAAf,CAACF,aAAa,CAAC;aACzB,CAACX,IAAI,CAAC,IAAI,CAAC,CAAC;SACd,MAAM;YACL,OAAO;gBACL,CAAC,0DAA0D,EAAExC,MAAK,QAAA,CAACkB,IAAI,CACrE8B,gBAAgB,CACjB,CAAC,QAAQ,EAAEhD,MAAK,QAAA,CAACkB,IAAI,CAACgC,YAAY,CAAC,CAAC,EAAE,CAAC;gBACxC,CAAC,sFAAsF,CAAC;gBACxFG,CAAAA,GAAAA,KAAS,AAAe,CAAA,UAAf,CAACF,aAAa,CAAC;aACzB,CAACX,IAAI,CAAC,IAAI,CAAC,CAAC;SACd;KACF;IACD,OAAO,CAAC,mBAAmB,EAAEQ,gBAAgB,CAAC,QAAQ,EAAEE,YAAY,CAAC,CAAC,CAAC,CAAC;CACzE;AAEM,SAASjF,sBAAsB,CAACqF,UAAkB,EAAW;IAClE,OAAO,SAASC,IAAI,CAACD,UAAU,CAAC,IAAIE,UAAmB,oBAAA,CAACJ,QAAQ,CAACE,UAAU,CAAC,CAAC;CAC9E;AAED,8EAA8E,CAC9E,SAASR,oBAAoB,CAACD,OAAe,EAAEY,UAAkB,EAAU;IACzE,MAAMC,SAAS,GAAGxF,cAAc,CAACuF,UAAU,CAAC,AAAC;IAC7C,IAAIC,SAAS,EAAE;QACbb,OAAO,IAAI,IAAI,GAAGa,SAAS,CAAC;KAC7B;IACD,OAAOb,OAAO,CAAC;CAChB;AAOM,SAAS3E,cAAc,CAACyF,YAAoB,EAAiB;IAClE,kDAAkD;IAClD,IAAI,CAACA,YAAY,CAACP,QAAQ,CAAC,qBAAqB,CAAC,EAAE;QACjD,OAAO,IAAI,CAAC;KACb;IACD,MAAMQ,KAAK,GAAGD,YAAY,CAACE,KAAK,CAAC,IAAI,CAAC,AAAC;IACvC,MAAMC,KAAK,GAAGF,KAAK,CAACG,SAAS,CAAC,CAACC,IAAI,GAAKA,IAAI,CAACZ,QAAQ,CAAC,qBAAqB,CAAC;IAAA,CAAC,AAAC;IAC9E,IAAIU,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,OAAO,IAAI,CAAC;KACb;IACD,OAAOF,KAAK,CAACK,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC,CAACtB,IAAI,CAAC,IAAI,CAAC,CAAC;CAC1C;AAED,8DAA8D,CAC9D,SAASP,2BAA2B,CAACiC,IAAW,EAAW;IACzD,OACEA,IAAI,CAAC1C,MAAM,KAAK,CAAC,IACjB,CAAC,8CAA8C+B,IAAI,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC,IAC1D,0BAA0BX,IAAI,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1C;CACH;AAED,kEAAkE,CAClE,SAAS5E,6BAA6B,CAACF,aAAoC,EAAU;QAClEA,GAAuB;IAAxC,MAAMC,QAAQ,GAAGD,CAAAA,GAAuB,GAAvBA,aAAa,QAAU,GAAvBA,KAAAA,CAAuB,GAAvBA,aAAa,CAAEC,QAAQ,YAAvBD,GAAuB,GAAI,IAAI,AAAC;IACjD,IAAIC,QAAQ,EAAE;QACZ,MAAM8E,SAAS,GAAG;YAAEC,GAAG,EAAE,KAAK;YAAEC,OAAO,EAAE,SAAS;YAAEC,GAAG,EAAE,KAAK;SAAE,CAACjF,QAAQ,CAAC,IAAIA,QAAQ,AAAC;QACvF,OAAO,CAAC,EAAEW,MAAK,QAAA,CAACkB,IAAI,CAACiD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;KACpC;IAED,OAAO,EAAE,CAAC;CACX;AACD,kEAAkE,CAClE,SAAShF,6BAA6B,CAACC,aAAoC,EAAU;QAEvEA,GAAqC;QAArCA,IAAkD;IAD9D,iGAAiG;IACjG,MAAMF,GAAG,GAAGE,CAAAA,IAAkD,GAAlDA,aAAa,QAAwB,GAArCA,KAAAA,CAAqC,GAArCA,CAAAA,GAAqC,GAArCA,aAAa,CAAEmF,sBAAsB,SAAA,GAArCnF,KAAAA,CAAqC,GAArCA,GAAqC,CAAEoF,WAAW,AAAb,YAArCpF,IAAkD,GAAI,IAAI,AAAC;IACvE,IAAIF,GAAG,KAAK,MAAM,EAAE;QAClB,OAAOc,MAAK,QAAA,CAACkB,IAAI,CAAC,QAAG,CAAC,GAAG,GAAG,CAAC;KAC9B;IAED,OAAO,EAAE,CAAC;CACX"}