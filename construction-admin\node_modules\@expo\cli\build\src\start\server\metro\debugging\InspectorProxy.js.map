{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/InspectorProxy.ts"], "sourcesContent": ["import type { unstable_InspectorProxy, unstable_Device } from '@react-native/dev-middleware';\nimport url from 'url';\nimport WS from 'ws';\n\nconst debug = require('debug')('expo:metro:inspector-proxy:proxy') as typeof console.log;\n\n/** Web socket error code for unknown internal errors */\nconst INTERNAL_ERROR_CODE = 1011;\n\n/**\n * Create a new Expo proxy inspector class that uses the feature-extended device class.\n * Everything else is reused from the original class.\n *\n * @see https://github.com/facebook/react-native/blob/f1df4ceb8479a6fc9c30f7571f5aeec255b116d2/packages/dev-middleware/src/inspector-proxy/InspectorProxy.js\n */\nexport function createInspectorProxyClass(\n  MetroInspectorProxyClass: typeof unstable_InspectorProxy,\n  MetroDeviceClass: typeof unstable_Device\n): typeof unstable_InspectorProxy {\n  return class ExpoInspectorProxy extends MetroInspectorProxyClass {\n    /**\n     * This method is overwritten to inject our own device class.\n     * @see https://github.com/facebook/react-native/blob/f1df4ceb8479a6fc9c30f7571f5aeec255b116d2/packages/dev-middleware/src/inspector-proxy/InspectorProxy.js#L179-L227\n     */\n    _createDeviceConnectionWSServer() {\n      const wss = new WS.Server({\n        noServer: true,\n        perMessageDeflate: true,\n        // Don't crash on exceptionally large messages - assume the device is\n        // well-behaved and the debugger is prepared to handle large messages.\n        maxPayload: 0,\n      });\n\n      wss.on('connection', async (socket: WS, req) => {\n        try {\n          const fallbackDeviceId = String(this._deviceCounter++);\n\n          const query = url.parse(req.url || '', true).query || {};\n          const deviceId = asString(query.device) || fallbackDeviceId;\n          const deviceName = asString(query.name) || 'Unknown';\n          const appName = asString(query.app) || 'Unknown';\n\n          const oldDevice = this._devices.get(deviceId);\n          // FIX: Create a new device instance using our own extended class\n          const newDevice = new MetroDeviceClass(\n            deviceId,\n            deviceName,\n            appName,\n            socket,\n            this._projectRoot,\n            this._eventReporter\n          );\n\n          if (oldDevice) {\n            oldDevice.handleDuplicateDeviceConnection(newDevice);\n          }\n\n          this._devices.set(deviceId, newDevice);\n\n          debug(`Got new connection: name=${deviceName}, app=${appName}, device=${deviceId}`);\n\n          socket.on('close', () => {\n            // FIX: Only clean up the device reference, if not replaced by new device\n            if (this._devices.get(deviceId) === newDevice) {\n              this._devices.delete(deviceId);\n              debug(`Device ${deviceName} disconnected.`);\n            } else {\n              debug(`Device ${deviceName} reconnected.`);\n            }\n          });\n        } catch (e) {\n          console.error('error', e);\n          socket.close(INTERNAL_ERROR_CODE, e?.toString() ?? 'Unknown error');\n          // FIX: add missing event reporter\n          this._eventReporter?.logEvent({\n            type: 'connect_debugger_app',\n            status: 'error',\n            error: e,\n          });\n        }\n      });\n\n      return wss;\n    }\n\n    /**\n     * This method is overwritten to allow user agents to be passed as query parameter.\n     * The built-in debugger in vscode does not add any user agent headers.\n     * @see https://github.com/facebook/react-native/blob/f1df4ceb8479a6fc9c30f7571f5aeec255b116d2/packages/dev-middleware/src/inspector-proxy/InspectorProxy.js#L234-L272\n     */\n    _createDebuggerConnectionWSServer() {\n      const wss = new WS.Server({\n        noServer: true,\n        perMessageDeflate: false,\n        // Don't crash on exceptionally large messages - assume the debugger is\n        // well-behaved and the device is prepared to handle large messages.\n        maxPayload: 0,\n      });\n\n      wss.on('connection', async (socket: WS, req) => {\n        try {\n          const query = url.parse(req.url || '', true).query || {};\n          const deviceId = asString(query.device);\n          const pageId = asString(query.page);\n          // FIX: Determine the user agent from query paramter or header\n          const userAgent = asString(query.userAgent) || req.headers['user-agent'] || null;\n\n          if (deviceId == null || pageId == null) {\n            throw new Error('Incorrect URL - must provide device and page IDs');\n          }\n\n          const device = this._devices.get(deviceId);\n          if (device == null) {\n            throw new Error('Unknown device with ID ' + deviceId);\n          }\n\n          device.handleDebuggerConnection(socket, pageId, { userAgent });\n        } catch (e) {\n          console.error(e);\n          socket.close(INTERNAL_ERROR_CODE, e?.toString() ?? 'Unknown error');\n          this._eventReporter?.logEvent({\n            type: 'connect_debugger_frontend',\n            status: 'error',\n            error: e,\n          });\n        }\n      });\n\n      return wss;\n    }\n  };\n}\n\n/** Convert the query paramters to plain string */\nfunction asString(value: string | string[] = ''): string {\n  return Array.isArray(value) ? value.join() : value;\n}\n"], "names": ["createInspectorProxyClass", "debug", "require", "INTERNAL_ERROR_CODE", "MetroInspectorProxyClass", "MetroDeviceClass", "ExpoInspectorProxy", "_createDeviceConnectionWSServer", "wss", "WS", "Server", "noServer", "perMessageDeflate", "maxPayload", "on", "socket", "req", "fallbackDeviceId", "String", "_deviceCounter", "query", "url", "parse", "deviceId", "asString", "device", "deviceName", "name", "appName", "app", "oldDevice", "_devices", "get", "newDevice", "_projectRoot", "_eventReporter", "handleDuplicateDeviceConnection", "set", "delete", "e", "console", "error", "close", "toString", "logEvent", "type", "status", "_createDebuggerConnectionWSServer", "pageId", "page", "userAgent", "headers", "Error", "handleDebuggerConnection", "value", "Array", "isArray", "join"], "mappings": "AAAA;;;;QAegBA,yBAAyB,GAAzBA,yBAAyB;AAdzB,IAAA,IAAK,kCAAL,KAAK,EAAA;AACN,IAAA,GAAI,kCAAJ,IAAI,EAAA;;;;;;AAEnB,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,kCAAkC,CAAC,AAAsB,AAAC;AAEzF,wDAAwD,CACxD,MAAMC,mBAAmB,GAAG,IAAI,AAAC;AAQ1B,SAASH,yBAAyB,CACvCI,wBAAwD,EACxDC,gBAAwC,EACR;IAChC,OAAO,MAAMC,kBAAkB,SAASF,wBAAwB;QAC9D;;;OAGG,CACHG,+BAA+B,GAAG;YAChC,MAAMC,GAAG,GAAG,IAAIC,GAAE,QAAA,CAACC,MAAM,CAAC;gBACxBC,QAAQ,EAAE,IAAI;gBACdC,iBAAiB,EAAE,IAAI;gBACvB,qEAAqE;gBACrE,sEAAsE;gBACtEC,UAAU,EAAE,CAAC;aACd,CAAC,AAAC;YAEHL,GAAG,CAACM,EAAE,CAAC,YAAY,EAAE,OAAOC,MAAU,EAAEC,GAAG,GAAK;gBAC9C,IAAI;oBACF,MAAMC,gBAAgB,GAAGC,MAAM,CAAC,IAAI,CAACC,cAAc,EAAE,CAAC,AAAC;oBAEvD,MAAMC,KAAK,GAAGC,IAAG,QAAA,CAACC,KAAK,CAACN,GAAG,CAACK,GAAG,IAAI,EAAE,EAAE,IAAI,CAAC,CAACD,KAAK,IAAI,EAAE,AAAC;oBACzD,MAAMG,QAAQ,GAAGC,QAAQ,CAACJ,KAAK,CAACK,MAAM,CAAC,IAAIR,gBAAgB,AAAC;oBAC5D,MAAMS,UAAU,GAAGF,QAAQ,CAACJ,KAAK,CAACO,IAAI,CAAC,IAAI,SAAS,AAAC;oBACrD,MAAMC,OAAO,GAAGJ,QAAQ,CAACJ,KAAK,CAACS,GAAG,CAAC,IAAI,SAAS,AAAC;oBAEjD,MAAMC,SAAS,GAAG,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACT,QAAQ,CAAC,AAAC;oBAC9C,iEAAiE;oBACjE,MAAMU,SAAS,GAAG,IAAI5B,gBAAgB,CACpCkB,QAAQ,EACRG,UAAU,EACVE,OAAO,EACPb,MAAM,EACN,IAAI,CAACmB,YAAY,EACjB,IAAI,CAACC,cAAc,CACpB,AAAC;oBAEF,IAAIL,SAAS,EAAE;wBACbA,SAAS,CAACM,+BAA+B,CAACH,SAAS,CAAC,CAAC;qBACtD;oBAED,IAAI,CAACF,QAAQ,CAACM,GAAG,CAACd,QAAQ,EAAEU,SAAS,CAAC,CAAC;oBAEvChC,KAAK,CAAC,CAAC,yBAAyB,EAAEyB,UAAU,CAAC,MAAM,EAAEE,OAAO,CAAC,SAAS,EAAEL,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAEpFR,MAAM,CAACD,EAAE,CAAC,OAAO,EAAE,IAAM;wBACvB,yEAAyE;wBACzE,IAAI,IAAI,CAACiB,QAAQ,CAACC,GAAG,CAACT,QAAQ,CAAC,KAAKU,SAAS,EAAE;4BAC7C,IAAI,CAACF,QAAQ,CAACO,MAAM,CAACf,QAAQ,CAAC,CAAC;4BAC/BtB,KAAK,CAAC,CAAC,OAAO,EAAEyB,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC;yBAC7C,MAAM;4BACLzB,KAAK,CAAC,CAAC,OAAO,EAAEyB,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;yBAC5C;qBACF,CAAC,CAAC;iBACJ,CAAC,OAAOa,CAAC,EAAE;wBAGV,kCAAkC;oBAClC,GAAmB;oBAHnBC,OAAO,CAACC,KAAK,CAAC,OAAO,EAAEF,CAAC,CAAC,CAAC;wBACQA,IAAa;oBAA/CxB,MAAM,CAAC2B,KAAK,CAACvC,mBAAmB,EAAEoC,CAAAA,IAAa,GAAbA,CAAC,QAAU,GAAXA,KAAAA,CAAW,GAAXA,CAAC,CAAEI,QAAQ,EAAE,YAAbJ,IAAa,GAAI,eAAe,CAAC,CAAC;oBAEpE,CAAA,GAAmB,GAAnB,IAAI,CAACJ,cAAc,SAAU,GAA7B,KAAA,CAA6B,GAA7B,GAAmB,CAAES,QAAQ,CAAC;wBAC5BC,IAAI,EAAE,sBAAsB;wBAC5BC,MAAM,EAAE,OAAO;wBACfL,KAAK,EAAEF,CAAC;qBACT,CAAC,AA9EZ,CA8Ea;iBACJ;aACF,CAAC,CAAC;YAEH,OAAO/B,GAAG,CAAC;SACZ;QAED;;;;OAIG,CACHuC,iCAAiC,GAAG;YAClC,MAAMvC,GAAG,GAAG,IAAIC,GAAE,QAAA,CAACC,MAAM,CAAC;gBACxBC,QAAQ,EAAE,IAAI;gBACdC,iBAAiB,EAAE,KAAK;gBACxB,uEAAuE;gBACvE,oEAAoE;gBACpEC,UAAU,EAAE,CAAC;aACd,CAAC,AAAC;YAEHL,GAAG,CAACM,EAAE,CAAC,YAAY,EAAE,OAAOC,MAAU,EAAEC,GAAG,GAAK;gBAC9C,IAAI;oBACF,MAAMI,KAAK,GAAGC,IAAG,QAAA,CAACC,KAAK,CAACN,GAAG,CAACK,GAAG,IAAI,EAAE,EAAE,IAAI,CAAC,CAACD,KAAK,IAAI,EAAE,AAAC;oBACzD,MAAMG,QAAQ,GAAGC,QAAQ,CAACJ,KAAK,CAACK,MAAM,CAAC,AAAC;oBACxC,MAAMuB,MAAM,GAAGxB,QAAQ,CAACJ,KAAK,CAAC6B,IAAI,CAAC,AAAC;oBACpC,8DAA8D;oBAC9D,MAAMC,SAAS,GAAG1B,QAAQ,CAACJ,KAAK,CAAC8B,SAAS,CAAC,IAAIlC,GAAG,CAACmC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,AAAC;oBAEjF,IAAI5B,QAAQ,IAAI,IAAI,IAAIyB,MAAM,IAAI,IAAI,EAAE;wBACtC,MAAM,IAAII,KAAK,CAAC,kDAAkD,CAAC,CAAC;qBACrE;oBAED,MAAM3B,MAAM,GAAG,IAAI,CAACM,QAAQ,CAACC,GAAG,CAACT,QAAQ,CAAC,AAAC;oBAC3C,IAAIE,MAAM,IAAI,IAAI,EAAE;wBAClB,MAAM,IAAI2B,KAAK,CAAC,yBAAyB,GAAG7B,QAAQ,CAAC,CAAC;qBACvD;oBAEDE,MAAM,CAAC4B,wBAAwB,CAACtC,MAAM,EAAEiC,MAAM,EAAE;wBAAEE,SAAS;qBAAE,CAAC,CAAC;iBAChE,CAAC,OAAOX,CAAC,EAAE;wBAGV,GAAmB;oBAFnBC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC,CAAC;wBACiBA,IAAa;oBAA/CxB,MAAM,CAAC2B,KAAK,CAACvC,mBAAmB,EAAEoC,CAAAA,IAAa,GAAbA,CAAC,QAAU,GAAXA,KAAAA,CAAW,GAAXA,CAAC,CAAEI,QAAQ,EAAE,YAAbJ,IAAa,GAAI,eAAe,CAAC,CAAC;oBACpE,CAAA,GAAmB,GAAnB,IAAI,CAACJ,cAAc,SAAU,GAA7B,KAAA,CAA6B,GAA7B,GAAmB,CAAES,QAAQ,CAAC;wBAC5BC,IAAI,EAAE,2BAA2B;wBACjCC,MAAM,EAAE,OAAO;wBACfL,KAAK,EAAEF,CAAC;qBACT,CAAC,AA5HZ,CA4Ha;iBACJ;aACF,CAAC,CAAC;YAEH,OAAO/B,GAAG,CAAC;SACZ;KACF,CAAC;CACH;AAED,kDAAkD,CAClD,SAASgB,QAAQ,CAAC8B,KAAwB,GAAG,EAAE,EAAU;IACvD,OAAOC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGA,KAAK,CAACG,IAAI,EAAE,GAAGH,KAAK,CAAC;CACpD"}