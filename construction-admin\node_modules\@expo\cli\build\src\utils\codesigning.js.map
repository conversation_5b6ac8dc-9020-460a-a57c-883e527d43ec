{"version": 3, "sources": ["../../../src/utils/codesigning.ts"], "sourcesContent": ["import {\n  convertCertificatePEMToCertificate,\n  convertKeyPairToPEM,\n  convertCSRToCSRPEM,\n  generateKeyPair,\n  generateCSR,\n  convertPrivateKeyPEMToPrivateKey,\n  validateSelfSignedCertificate,\n  signBufferRSASHA256AndVerify,\n} from '@expo/code-signing-certificates';\nimport { ExpoConfig } from '@expo/config';\nimport { getExpoHomeDirectory } from '@expo/config/build/getUserState';\nimport JsonFile, { JSONObject } from '@expo/json-file';\nimport { CombinedError } from '@urql/core';\nimport { promises as fs } from 'fs';\nimport { GraphQLError } from 'graphql';\nimport { pki as PKI } from 'node-forge';\nimport path from 'path';\nimport { Dictionary, parseDictionary } from 'structured-headers';\n\nimport { env } from './env';\nimport { CommandError } from './errors';\nimport { getExpoGoIntermediateCertificateAsync } from '../api/getExpoGoIntermediateCertificate';\nimport { getProjectDevelopmentCertificateAsync } from '../api/getProjectDevelopmentCertificate';\nimport { AppQuery } from '../api/graphql/queries/AppQuery';\nimport { ensureLoggedInAsync } from '../api/user/actions';\nimport { Actor } from '../api/user/user';\nimport { AppByIdQuery, Permission } from '../graphql/generated';\nimport * as Log from '../log';\nimport { learnMore } from '../utils/link';\n\nconst debug = require('debug')('expo:codesigning') as typeof console.log;\n\nexport type CodeSigningInfo = {\n  keyId: string;\n  privateKey: string;\n  certificateForPrivateKey: string;\n  /**\n   * Chain of certificates to serve in the manifest multipart body \"certificate_chain\" part.\n   * The leaf certificate must be the 0th element of the array, followed by any intermediate certificates\n   * necessary to evaluate the chain of trust ending in the implicitly trusted root certificate embedded in\n   * the client.\n   *\n   * An empty array indicates that there is no need to serve the certificate chain in the multipart response.\n   */\n  certificateChainForResponse: string[];\n  /**\n   * Scope key cached for the project when certificate is development Expo Go code signing.\n   * For project-specific code signing (keyId == the project's generated keyId) this is undefined.\n   */\n  scopeKey: string | null;\n};\n\ntype StoredDevelopmentExpoRootCodeSigningInfo = {\n  easProjectId: string | null;\n  scopeKey: string | null;\n  privateKey: string | null;\n  certificateChain: string[] | null;\n};\nconst DEVELOPMENT_CODE_SIGNING_SETTINGS_FILE_NAME = 'development-code-signing-settings-2.json';\n\nexport function getDevelopmentCodeSigningDirectory(): string {\n  return path.join(getExpoHomeDirectory(), 'codesigning');\n}\n\nfunction getProjectDevelopmentCodeSigningInfoFile<T extends JSONObject>(defaults: T) {\n  function getFile(easProjectId: string): JsonFile<T> {\n    const filePath = path.join(\n      getDevelopmentCodeSigningDirectory(),\n      easProjectId,\n      DEVELOPMENT_CODE_SIGNING_SETTINGS_FILE_NAME\n    );\n    return new JsonFile<T>(filePath);\n  }\n\n  async function readAsync(easProjectId: string): Promise<T> {\n    let projectSettings;\n    try {\n      projectSettings = await getFile(easProjectId).readAsync();\n    } catch {\n      projectSettings = await getFile(easProjectId).writeAsync(defaults, { ensureDir: true });\n    }\n    // Set defaults for any missing fields\n    return { ...defaults, ...projectSettings };\n  }\n\n  async function setAsync(easProjectId: string, json: Partial<T>): Promise<T> {\n    try {\n      return await getFile(easProjectId).mergeAsync(json, {\n        cantReadFileDefault: defaults,\n      });\n    } catch {\n      return await getFile(easProjectId).writeAsync(\n        {\n          ...defaults,\n          ...json,\n        },\n        { ensureDir: true }\n      );\n    }\n  }\n\n  return {\n    getFile,\n    readAsync,\n    setAsync,\n  };\n}\n\nexport const DevelopmentCodeSigningInfoFile =\n  getProjectDevelopmentCodeSigningInfoFile<StoredDevelopmentExpoRootCodeSigningInfo>({\n    easProjectId: null,\n    scopeKey: null,\n    privateKey: null,\n    certificateChain: null,\n  });\n\n/**\n * Get info necessary to generate a response `expo-signature` header given a project and incoming request `expo-expect-signature` header.\n * This only knows how to serve two code signing keyids:\n * - `expo-root` indicates that it should use a development certificate in the `expo-root` chain. See {@link getExpoRootDevelopmentCodeSigningInfoAsync}\n * - <developer's expo-updates keyid> indicates that it should sign with the configured certificate. See {@link getProjectCodeSigningCertificateAsync}\n */\nexport async function getCodeSigningInfoAsync(\n  exp: ExpoConfig,\n  expectSignatureHeader: string | null,\n  privateKeyPath: string | undefined\n): Promise<CodeSigningInfo | null> {\n  if (!expectSignatureHeader) {\n    return null;\n  }\n\n  let parsedExpectSignature: Dictionary;\n  try {\n    parsedExpectSignature = parseDictionary(expectSignatureHeader);\n  } catch {\n    throw new CommandError('Invalid value for expo-expect-signature header');\n  }\n\n  const expectedKeyIdOuter = parsedExpectSignature.get('keyid');\n  if (!expectedKeyIdOuter) {\n    throw new CommandError('keyid not present in expo-expect-signature header');\n  }\n\n  const expectedKeyId = expectedKeyIdOuter[0];\n  if (typeof expectedKeyId !== 'string') {\n    throw new CommandError(\n      `Invalid value for keyid in expo-expect-signature header: ${expectedKeyId}`\n    );\n  }\n\n  let expectedAlg: string | null = null;\n  const expectedAlgOuter = parsedExpectSignature.get('alg');\n  if (expectedAlgOuter) {\n    const expectedAlgTemp = expectedAlgOuter[0];\n    if (typeof expectedAlgTemp !== 'string') {\n      throw new CommandError('Invalid value for alg in expo-expect-signature header');\n    }\n    expectedAlg = expectedAlgTemp;\n  }\n\n  if (expectedKeyId === 'expo-root') {\n    return await getExpoRootDevelopmentCodeSigningInfoAsync(exp);\n  } else if (expectedKeyId === 'expo-go') {\n    throw new CommandError(\n      'Invalid certificate requested: cannot sign with embedded keyid=expo-go key'\n    );\n  } else {\n    return await getProjectCodeSigningCertificateAsync(\n      exp,\n      privateKeyPath,\n      expectedKeyId,\n      expectedAlg\n    );\n  }\n}\n\n/**\n * Get a development code signing certificate for the expo-root -> expo-go -> (development certificate) certificate chain.\n * This requires the user be logged in and online, otherwise try to use the cached development certificate.\n */\nasync function getExpoRootDevelopmentCodeSigningInfoAsync(\n  exp: ExpoConfig\n): Promise<CodeSigningInfo | null> {\n  const easProjectId = exp.extra?.eas?.projectId;\n  // can't check for scope key validity since scope key is derived on the server from projectId and we may be offline.\n  // we rely upon the client certificate check to validate the scope key\n  if (!easProjectId) {\n    debug(\n      `WARN: Expo Application Services (EAS) is not configured for your project. Configuring EAS enables a more secure development experience amongst many other benefits. ${learnMore(\n        'https://docs.expo.dev/eas/'\n      )}`\n    );\n    return null;\n  }\n\n  const developmentCodeSigningInfoFromFile =\n    await DevelopmentCodeSigningInfoFile.readAsync(easProjectId);\n  const validatedCodeSigningInfo = validateStoredDevelopmentExpoRootCertificateCodeSigningInfo(\n    developmentCodeSigningInfoFromFile,\n    easProjectId\n  );\n\n  // 1. If online, ensure logged in, generate key pair and CSR, fetch and cache certificate chain for projectId\n  //    (overwriting existing dev cert in case projectId changed or it has expired)\n  if (!env.EXPO_OFFLINE) {\n    try {\n      return await fetchAndCacheNewDevelopmentCodeSigningInfoAsync(easProjectId);\n    } catch (e: any) {\n      if (validatedCodeSigningInfo) {\n        Log.warn(\n          'There was an error fetching the Expo development certificate, falling back to cached certificate'\n        );\n        return validatedCodeSigningInfo;\n      } else {\n        // need to return null here and say a message\n        throw e;\n      }\n    }\n  }\n\n  // 2. check for cached cert/private key matching projectId and scopeKey of project, if found and valid return private key and cert chain including expo-go cert\n  if (validatedCodeSigningInfo) {\n    return validatedCodeSigningInfo;\n  }\n\n  // 3. if offline, return null\n  Log.warn('Offline and no cached development certificate found, unable to sign manifest');\n  return null;\n}\n\n/**\n * Get the certificate configured for expo-updates for this project.\n */\nasync function getProjectCodeSigningCertificateAsync(\n  exp: ExpoConfig,\n  privateKeyPath: string | undefined,\n  expectedKeyId: string,\n  expectedAlg: string | null\n): Promise<CodeSigningInfo | null> {\n  const codeSigningCertificatePath = exp.updates?.codeSigningCertificate;\n  if (!codeSigningCertificatePath) {\n    return null;\n  }\n\n  if (!privateKeyPath) {\n    throw new CommandError(\n      'Must specify --private-key-path argument to sign development manifest for requested code signing key'\n    );\n  }\n\n  const codeSigningMetadata = exp.updates?.codeSigningMetadata;\n  if (!codeSigningMetadata) {\n    throw new CommandError(\n      'Must specify \"codeSigningMetadata\" under the \"updates\" field of your app config file to use EAS code signing'\n    );\n  }\n\n  const { alg, keyid } = codeSigningMetadata;\n  if (!alg || !keyid) {\n    throw new CommandError(\n      'Must specify \"keyid\" and \"alg\" in the \"codeSigningMetadata\" field under the \"updates\" field of your app config file to use EAS code signing'\n    );\n  }\n\n  if (expectedKeyId !== keyid) {\n    throw new CommandError(`keyid mismatch: client=${expectedKeyId}, project=${keyid}`);\n  }\n\n  if (expectedAlg && expectedAlg !== alg) {\n    throw new CommandError(`\"alg\" field mismatch (client=${expectedAlg}, project=${alg})`);\n  }\n\n  const { privateKeyPEM, certificatePEM } =\n    await getProjectPrivateKeyAndCertificateFromFilePathsAsync({\n      codeSigningCertificatePath,\n      privateKeyPath,\n    });\n\n  return {\n    keyId: keyid,\n    privateKey: privateKeyPEM,\n    certificateForPrivateKey: certificatePEM,\n    certificateChainForResponse: [],\n    scopeKey: null,\n  };\n}\n\nasync function readFileWithErrorAsync(path: string, errorMessage: string): Promise<string> {\n  try {\n    return await fs.readFile(path, 'utf8');\n  } catch {\n    throw new CommandError(errorMessage);\n  }\n}\n\nasync function getProjectPrivateKeyAndCertificateFromFilePathsAsync({\n  codeSigningCertificatePath,\n  privateKeyPath,\n}: {\n  codeSigningCertificatePath: string;\n  privateKeyPath: string;\n}): Promise<{ privateKeyPEM: string; certificatePEM: string }> {\n  const [codeSigningCertificatePEM, privateKeyPEM] = await Promise.all([\n    readFileWithErrorAsync(\n      codeSigningCertificatePath,\n      `Code signing certificate cannot be read from path: ${codeSigningCertificatePath}`\n    ),\n    readFileWithErrorAsync(\n      privateKeyPath,\n      `Code signing private key cannot be read from path: ${privateKeyPath}`\n    ),\n  ]);\n\n  const privateKey = convertPrivateKeyPEMToPrivateKey(privateKeyPEM);\n  const certificate = convertCertificatePEMToCertificate(codeSigningCertificatePEM);\n  validateSelfSignedCertificate(certificate, {\n    publicKey: certificate.publicKey as PKI.rsa.PublicKey,\n    privateKey,\n  });\n\n  return { privateKeyPEM, certificatePEM: codeSigningCertificatePEM };\n}\n\n/**\n * Validate that the cached code signing info is still valid for the current project and\n * that it hasn't expired. If invalid, return null.\n */\nfunction validateStoredDevelopmentExpoRootCertificateCodeSigningInfo(\n  codeSigningInfo: StoredDevelopmentExpoRootCodeSigningInfo,\n  easProjectId: string\n): CodeSigningInfo | null {\n  if (codeSigningInfo.easProjectId !== easProjectId) {\n    return null;\n  }\n\n  const {\n    privateKey: privateKeyPEM,\n    certificateChain: certificatePEMs,\n    scopeKey,\n  } = codeSigningInfo;\n  if (!privateKeyPEM || !certificatePEMs) {\n    return null;\n  }\n\n  const certificateChain = certificatePEMs.map((certificatePEM) =>\n    convertCertificatePEMToCertificate(certificatePEM)\n  );\n\n  // TODO(wschurman): maybe move to @expo/code-signing-certificates\n  const leafCertificate = certificateChain[0];\n  const now = new Date();\n  if (leafCertificate.validity.notBefore > now || leafCertificate.validity.notAfter < now) {\n    return null;\n  }\n\n  // TODO(wschurman): maybe do more validation, like validation of projectID and scopeKey within eas certificate extension\n\n  return {\n    keyId: 'expo-go',\n    certificateChainForResponse: certificatePEMs,\n    certificateForPrivateKey: certificatePEMs[0],\n    privateKey: privateKeyPEM,\n    scopeKey,\n  };\n}\n\nfunction actorCanGetProjectDevelopmentCertificate(actor: Actor, app: AppByIdQuery['app']['byId']) {\n  const owningAccountId = app.ownerAccount.id;\n\n  const owningAccountIsActorPrimaryAccount =\n    actor.__typename === 'User' || actor.__typename === 'SSOUser'\n      ? actor.primaryAccount.id === owningAccountId\n      : false;\n  const userHasPublishPermissionForOwningAccount = !!actor.accounts\n    .find((account) => account.id === owningAccountId)\n    ?.users?.find((userPermission) => userPermission.actor.id === actor.id)\n    ?.permissions?.includes(Permission.Publish);\n  return owningAccountIsActorPrimaryAccount || userHasPublishPermissionForOwningAccount;\n}\n\nasync function fetchAndCacheNewDevelopmentCodeSigningInfoAsync(\n  easProjectId: string\n): Promise<CodeSigningInfo | null> {\n  const actor = await ensureLoggedInAsync();\n  let app: AppByIdQuery['app']['byId'];\n  try {\n    app = await AppQuery.byIdAsync(easProjectId);\n  } catch (e) {\n    if (e instanceof GraphQLError || e instanceof CombinedError) {\n      return null;\n    }\n    throw e;\n  }\n  if (!actorCanGetProjectDevelopmentCertificate(actor, app)) {\n    return null;\n  }\n\n  const keyPair = generateKeyPair();\n  const keyPairPEM = convertKeyPairToPEM(keyPair);\n  const csr = generateCSR(keyPair, `Development Certificate for ${easProjectId}`);\n  const csrPEM = convertCSRToCSRPEM(csr);\n  const [developmentSigningCertificate, expoGoIntermediateCertificate] = await Promise.all([\n    getProjectDevelopmentCertificateAsync(easProjectId, csrPEM),\n    getExpoGoIntermediateCertificateAsync(easProjectId),\n  ]);\n\n  await DevelopmentCodeSigningInfoFile.setAsync(easProjectId, {\n    easProjectId,\n    scopeKey: app.scopeKey,\n    privateKey: keyPairPEM.privateKeyPEM,\n    certificateChain: [developmentSigningCertificate, expoGoIntermediateCertificate],\n  });\n\n  return {\n    keyId: 'expo-go',\n    certificateChainForResponse: [developmentSigningCertificate, expoGoIntermediateCertificate],\n    certificateForPrivateKey: developmentSigningCertificate,\n    privateKey: keyPairPEM.privateKeyPEM,\n    scopeKey: app.scopeKey,\n  };\n}\n/**\n * Generate the `expo-signature` header for a manifest and code signing info.\n */\nexport function signManifestString(\n  stringifiedManifest: string,\n  codeSigningInfo: CodeSigningInfo\n): string {\n  const privateKey = convertPrivateKeyPEMToPrivateKey(codeSigningInfo.privateKey);\n  const certificate = convertCertificatePEMToCertificate(codeSigningInfo.certificateForPrivateKey);\n  return signBufferRSASHA256AndVerify(\n    privateKey,\n    certificate,\n    Buffer.from(stringifiedManifest, 'utf8')\n  );\n}\n"], "names": ["getDevelopmentCodeSigningDirectory", "getCodeSigningInfoAsync", "signManifestString", "Log", "debug", "require", "DEVELOPMENT_CODE_SIGNING_SETTINGS_FILE_NAME", "path", "join", "getExpoHomeDirectory", "getProjectDevelopmentCodeSigningInfoFile", "defaults", "getFile", "easProjectId", "filePath", "JsonFile", "readAsync", "projectSettings", "writeAsync", "ensureDir", "setAsync", "json", "mergeAsync", "cantReadFileDefault", "DevelopmentCodeSigningInfoFile", "<PERSON><PERSON>ey", "privateKey", "certificate<PERSON>hain", "exp", "expectSignatureHeader", "privateKeyPath", "parsedExpectSignature", "parseDictionary", "CommandError", "expectedKeyIdOuter", "get", "expectedKeyId", "expectedAlg", "expectedAlgOuter", "expectedAlgTemp", "getExpoRootDevelopmentCodeSigningInfoAsync", "getProjectCodeSigningCertificateAsync", "extra", "eas", "projectId", "learnMore", "developmentCodeSigningInfoFromFile", "validatedCodeSigningInfo", "validateStoredDevelopmentExpoRootCertificateCodeSigningInfo", "env", "EXPO_OFFLINE", "fetchAndCacheNewDevelopmentCodeSigningInfoAsync", "e", "warn", "codeSigningCertificatePath", "updates", "codeSigningCertificate", "codeSigningMetadata", "alg", "keyid", "privateKeyPEM", "certificatePEM", "getProjectPrivateKeyAndCertificateFromFilePathsAsync", "keyId", "certificateForPrivateKey", "certificateChainForResponse", "readFileWithErrorAsync", "errorMessage", "fs", "readFile", "codeSigningCertificatePEM", "Promise", "all", "convertPrivateKeyPEMToPrivateKey", "certificate", "convertCertificatePEMToCertificate", "validateSelfSignedCertificate", "public<PERSON>ey", "codeSigningInfo", "certificatePEMs", "map", "leafCertificate", "now", "Date", "validity", "notBefore", "notAfter", "actorCanGetProjectDevelopmentCertificate", "actor", "app", "owningAccountId", "ownerAccount", "id", "owningAccountIsActorPrimaryAccount", "__typename", "primaryAccount", "userHasPublishPermissionForOwningAccount", "accounts", "find", "account", "users", "userPermission", "permissions", "includes", "Permission", "Publish", "ensureLoggedInAsync", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "byIdAsync", "GraphQLError", "CombinedError", "keyPair", "generateKeyPair", "keyPairPEM", "convertKeyPairToPEM", "csr", "generateCSR", "csrPEM", "convertCSRToCSRPEM", "developmentSigningCertificate", "expoGoIntermediateCertificate", "getProjectDevelopmentCertificateAsync", "getExpoGoIntermediateCertificateAsync", "stringifiedManifest", "signBufferRSASHA256AndVerify", "<PERSON><PERSON><PERSON>", "from"], "mappings": "AAAA;;;;QA6DgBA,kCAAkC,GAAlCA,kCAAkC;QA8D5BC,uBAAuB,GAAvBA,uBAAuB;QA8S7BC,kBAAkB,GAAlBA,kBAAkB;;AAha3B,IAAA,wBAAiC,WAAjC,iCAAiC,CAAA;AAEH,IAAA,aAAiC,WAAjC,iCAAiC,CAAA;AACjC,IAAA,SAAiB,kCAAjB,iBAAiB,EAAA;AACxB,IAAA,KAAY,WAAZ,YAAY,CAAA;AACX,IAAA,GAAI,WAAJ,IAAI,CAAA;AACN,IAAA,QAAS,WAAT,SAAS,CAAA;AAErB,IAAA,KAAM,kCAAN,MAAM,EAAA;AACqB,IAAA,kBAAoB,WAApB,oBAAoB,CAAA;AAE5C,IAAA,IAAO,WAAP,OAAO,CAAA;AACE,IAAA,OAAU,WAAV,UAAU,CAAA;AACe,IAAA,iCAAyC,WAAzC,yCAAyC,CAAA;AACzC,IAAA,iCAAyC,WAAzC,yCAAyC,CAAA;AACtE,IAAA,SAAiC,WAAjC,iCAAiC,CAAA;AACtB,IAAA,QAAqB,WAArB,qBAAqB,CAAA;AAEhB,IAAA,UAAsB,WAAtB,sBAAsB,CAAA;AACnDC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACW,IAAA,KAAe,WAAf,eAAe,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAC,AAAsB,AAAC;AA4BzE,MAAMC,2CAA2C,GAAG,0CAA0C,AAAC;AAExF,SAASN,kCAAkC,GAAW;IAC3D,OAAOO,KAAI,QAAA,CAACC,IAAI,CAACC,CAAAA,GAAAA,aAAoB,AAAE,CAAA,qBAAF,EAAE,EAAE,aAAa,CAAC,CAAC;CACzD;AAED,SAASC,wCAAwC,CAAuBC,QAAW,EAAE;IACnF,SAASC,OAAO,CAACC,YAAoB,EAAe;QAClD,MAAMC,QAAQ,GAAGP,KAAI,QAAA,CAACC,IAAI,CACxBR,kCAAkC,EAAE,EACpCa,YAAY,EACZP,2CAA2C,CAC5C,AAAC;QACF,OAAO,IAAIS,SAAQ,QAAA,CAAID,QAAQ,CAAC,CAAC;KAClC;IAED,eAAeE,SAAS,CAACH,YAAoB,EAAc;QACzD,IAAII,eAAe,AAAC;QACpB,IAAI;YACFA,eAAe,GAAG,MAAML,OAAO,CAACC,YAAY,CAAC,CAACG,SAAS,EAAE,CAAC;SAC3D,CAAC,OAAM;YACNC,eAAe,GAAG,MAAML,OAAO,CAACC,YAAY,CAAC,CAACK,UAAU,CAACP,QAAQ,EAAE;gBAAEQ,SAAS,EAAE,IAAI;aAAE,CAAC,CAAC;SACzF;QACD,sCAAsC;QACtC,OAAO;YAAE,GAAGR,QAAQ;YAAE,GAAGM,eAAe;SAAE,CAAC;KAC5C;IAED,eAAeG,QAAQ,CAACP,YAAoB,EAAEQ,IAAgB,EAAc;QAC1E,IAAI;YACF,OAAO,MAAMT,OAAO,CAACC,YAAY,CAAC,CAACS,UAAU,CAACD,IAAI,EAAE;gBAClDE,mBAAmB,EAAEZ,QAAQ;aAC9B,CAAC,CAAC;SACJ,CAAC,OAAM;YACN,OAAO,MAAMC,OAAO,CAACC,YAAY,CAAC,CAACK,UAAU,CAC3C;gBACE,GAAGP,QAAQ;gBACX,GAAGU,IAAI;aACR,EACD;gBAAEF,SAAS,EAAE,IAAI;aAAE,CACpB,CAAC;SACH;KACF;IAED,OAAO;QACLP,OAAO;QACPI,SAAS;QACTI,QAAQ;KACT,CAAC;CACH;AAEM,MAAMI,8BAA8B,GACzCd,wCAAwC,CAA2C;IACjFG,YAAY,EAAE,IAAI;IAClBY,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,gBAAgB,EAAE,IAAI;CACvB,CAAC,AAAC;QANQH,8BAA8B,GAA9BA,8BAA8B;AAcpC,eAAevB,uBAAuB,CAC3C2B,GAAe,EACfC,qBAAoC,EACpCC,cAAkC,EACD;IACjC,IAAI,CAACD,qBAAqB,EAAE;QAC1B,OAAO,IAAI,CAAC;KACb;IAED,IAAIE,qBAAqB,AAAY,AAAC;IACtC,IAAI;QACFA,qBAAqB,GAAGC,CAAAA,GAAAA,kBAAe,AAAuB,CAAA,gBAAvB,CAACH,qBAAqB,CAAC,CAAC;KAChE,CAAC,OAAM;QACN,MAAM,IAAII,OAAY,aAAA,CAAC,gDAAgD,CAAC,CAAC;KAC1E;IAED,MAAMC,kBAAkB,GAAGH,qBAAqB,CAACI,GAAG,CAAC,OAAO,CAAC,AAAC;IAC9D,IAAI,CAACD,kBAAkB,EAAE;QACvB,MAAM,IAAID,OAAY,aAAA,CAAC,mDAAmD,CAAC,CAAC;KAC7E;IAED,MAAMG,aAAa,GAAGF,kBAAkB,CAAC,CAAC,CAAC,AAAC;IAC5C,IAAI,OAAOE,aAAa,KAAK,QAAQ,EAAE;QACrC,MAAM,IAAIH,OAAY,aAAA,CACpB,CAAC,yDAAyD,EAAEG,aAAa,CAAC,CAAC,CAC5E,CAAC;KACH;IAED,IAAIC,WAAW,GAAkB,IAAI,AAAC;IACtC,MAAMC,gBAAgB,GAAGP,qBAAqB,CAACI,GAAG,CAAC,KAAK,CAAC,AAAC;IAC1D,IAAIG,gBAAgB,EAAE;QACpB,MAAMC,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC,AAAC;QAC5C,IAAI,OAAOC,eAAe,KAAK,QAAQ,EAAE;YACvC,MAAM,IAAIN,OAAY,aAAA,CAAC,uDAAuD,CAAC,CAAC;SACjF;QACDI,WAAW,GAAGE,eAAe,CAAC;KAC/B;IAED,IAAIH,aAAa,KAAK,WAAW,EAAE;QACjC,OAAO,MAAMI,0CAA0C,CAACZ,GAAG,CAAC,CAAC;KAC9D,MAAM,IAAIQ,aAAa,KAAK,SAAS,EAAE;QACtC,MAAM,IAAIH,OAAY,aAAA,CACpB,4EAA4E,CAC7E,CAAC;KACH,MAAM;QACL,OAAO,MAAMQ,qCAAqC,CAChDb,GAAG,EACHE,cAAc,EACdM,aAAa,EACbC,WAAW,CACZ,CAAC;KACH;CACF;AAED;;;GAGG,CACH,eAAeG,0CAA0C,CACvDZ,GAAe,EACkB;QACZA,GAAS;IAA9B,MAAMf,YAAY,GAAGe,CAAAA,GAAS,GAATA,GAAG,CAACc,KAAK,SAAK,GAAdd,KAAAA,CAAc,GAAdA,QAAAA,GAAS,CAAEe,GAAG,SAAA,GAAdf,KAAAA,CAAc,QAAEgB,SAAS,AAAX,AAAY;IAC/C,oHAAoH;IACpH,sEAAsE;IACtE,IAAI,CAAC/B,YAAY,EAAE;QACjBT,KAAK,CACH,CAAC,oKAAoK,EAAEyC,CAAAA,GAAAA,KAAS,AAE/K,CAAA,UAF+K,CAC9K,4BAA4B,CAC7B,CAAC,CAAC,CACJ,CAAC;QACF,OAAO,IAAI,CAAC;KACb;IAED,MAAMC,kCAAkC,GACtC,MAAMtB,8BAA8B,CAACR,SAAS,CAACH,YAAY,CAAC,AAAC;IAC/D,MAAMkC,wBAAwB,GAAGC,2DAA2D,CAC1FF,kCAAkC,EAClCjC,YAAY,CACb,AAAC;IAEF,6GAA6G;IAC7G,iFAAiF;IACjF,IAAI,CAACoC,IAAG,IAAA,CAACC,YAAY,EAAE;QACrB,IAAI;YACF,OAAO,MAAMC,+CAA+C,CAACtC,YAAY,CAAC,CAAC;SAC5E,CAAC,OAAOuC,CAAC,EAAO;YACf,IAAIL,wBAAwB,EAAE;gBAC5B5C,GAAG,CAACkD,IAAI,CACN,kGAAkG,CACnG,CAAC;gBACF,OAAON,wBAAwB,CAAC;aACjC,MAAM;gBACL,6CAA6C;gBAC7C,MAAMK,CAAC,CAAC;aACT;SACF;KACF;IAED,+JAA+J;IAC/J,IAAIL,wBAAwB,EAAE;QAC5B,OAAOA,wBAAwB,CAAC;KACjC;IAED,6BAA6B;IAC7B5C,GAAG,CAACkD,IAAI,CAAC,8EAA8E,CAAC,CAAC;IACzF,OAAO,IAAI,CAAC;CACb;AAED;;GAEG,CACH,eAAeZ,qCAAqC,CAClDb,GAAe,EACfE,cAAkC,EAClCM,aAAqB,EACrBC,WAA0B,EACO;QACET,GAAW,EAWlBA,IAAW;IAXvC,MAAM0B,0BAA0B,GAAG1B,CAAAA,GAAW,GAAXA,GAAG,CAAC2B,OAAO,SAAwB,GAAnC3B,KAAAA,CAAmC,GAAnCA,GAAW,CAAE4B,sBAAsB,AAAC;IACvE,IAAI,CAACF,0BAA0B,EAAE;QAC/B,OAAO,IAAI,CAAC;KACb;IAED,IAAI,CAACxB,cAAc,EAAE;QACnB,MAAM,IAAIG,OAAY,aAAA,CACpB,sGAAsG,CACvG,CAAC;KACH;IAED,MAAMwB,mBAAmB,GAAG7B,CAAAA,IAAW,GAAXA,GAAG,CAAC2B,OAAO,SAAqB,GAAhC3B,KAAAA,CAAgC,GAAhCA,IAAW,CAAE6B,mBAAmB,AAAC;IAC7D,IAAI,CAACA,mBAAmB,EAAE;QACxB,MAAM,IAAIxB,OAAY,aAAA,CACpB,8GAA8G,CAC/G,CAAC;KACH;IAED,MAAM,EAAEyB,GAAG,CAAA,EAAEC,KAAK,CAAA,EAAE,GAAGF,mBAAmB,AAAC;IAC3C,IAAI,CAACC,GAAG,IAAI,CAACC,KAAK,EAAE;QAClB,MAAM,IAAI1B,OAAY,aAAA,CACpB,6IAA6I,CAC9I,CAAC;KACH;IAED,IAAIG,aAAa,KAAKuB,KAAK,EAAE;QAC3B,MAAM,IAAI1B,OAAY,aAAA,CAAC,CAAC,uBAAuB,EAAEG,aAAa,CAAC,UAAU,EAAEuB,KAAK,CAAC,CAAC,CAAC,CAAC;KACrF;IAED,IAAItB,WAAW,IAAIA,WAAW,KAAKqB,GAAG,EAAE;QACtC,MAAM,IAAIzB,OAAY,aAAA,CAAC,CAAC,6BAA6B,EAAEI,WAAW,CAAC,UAAU,EAAEqB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACxF;IAED,MAAM,EAAEE,aAAa,CAAA,EAAEC,cAAc,CAAA,EAAE,GACrC,MAAMC,oDAAoD,CAAC;QACzDR,0BAA0B;QAC1BxB,cAAc;KACf,CAAC,AAAC;IAEL,OAAO;QACLiC,KAAK,EAAEJ,KAAK;QACZjC,UAAU,EAAEkC,aAAa;QACzBI,wBAAwB,EAAEH,cAAc;QACxCI,2BAA2B,EAAE,EAAE;QAC/BxC,QAAQ,EAAE,IAAI;KACf,CAAC;CACH;AAED,eAAeyC,sBAAsB,CAAC3D,IAAY,EAAE4D,YAAoB,EAAmB;IACzF,IAAI;QACF,OAAO,MAAMC,GAAE,SAAA,CAACC,QAAQ,CAAC9D,IAAI,EAAE,MAAM,CAAC,CAAC;KACxC,CAAC,OAAM;QACN,MAAM,IAAI0B,OAAY,aAAA,CAACkC,YAAY,CAAC,CAAC;KACtC;CACF;AAED,eAAeL,oDAAoD,CAAC,EAClER,0BAA0B,CAAA,EAC1BxB,cAAc,CAAA,EAIf,EAA8D;IAC7D,MAAM,CAACwC,yBAAyB,EAAEV,aAAa,CAAC,GAAG,MAAMW,OAAO,CAACC,GAAG,CAAC;QACnEN,sBAAsB,CACpBZ,0BAA0B,EAC1B,CAAC,mDAAmD,EAAEA,0BAA0B,CAAC,CAAC,CACnF;QACDY,sBAAsB,CACpBpC,cAAc,EACd,CAAC,mDAAmD,EAAEA,cAAc,CAAC,CAAC,CACvE;KACF,CAAC,AAAC;IAEH,MAAMJ,UAAU,GAAG+C,CAAAA,GAAAA,wBAAgC,AAAe,CAAA,iCAAf,CAACb,aAAa,CAAC,AAAC;IACnE,MAAMc,WAAW,GAAGC,CAAAA,GAAAA,wBAAkC,AAA2B,CAAA,mCAA3B,CAACL,yBAAyB,CAAC,AAAC;IAClFM,CAAAA,GAAAA,wBAA6B,AAG3B,CAAA,8BAH2B,CAACF,WAAW,EAAE;QACzCG,SAAS,EAAEH,WAAW,CAACG,SAAS;QAChCnD,UAAU;KACX,CAAC,CAAC;IAEH,OAAO;QAAEkC,aAAa;QAAEC,cAAc,EAAES,yBAAyB;KAAE,CAAC;CACrE;AAED;;;GAGG,CACH,SAAStB,2DAA2D,CAClE8B,eAAyD,EACzDjE,YAAoB,EACI;IACxB,IAAIiE,eAAe,CAACjE,YAAY,KAAKA,YAAY,EAAE;QACjD,OAAO,IAAI,CAAC;KACb;IAED,MAAM,EACJa,UAAU,EAAEkC,aAAa,CAAA,EACzBjC,gBAAgB,EAAEoD,eAAe,CAAA,EACjCtD,QAAQ,CAAA,IACT,GAAGqD,eAAe,AAAC;IACpB,IAAI,CAAClB,aAAa,IAAI,CAACmB,eAAe,EAAE;QACtC,OAAO,IAAI,CAAC;KACb;IAED,MAAMpD,gBAAgB,GAAGoD,eAAe,CAACC,GAAG,CAAC,CAACnB,cAAc,GAC1Dc,CAAAA,GAAAA,wBAAkC,AAAgB,CAAA,mCAAhB,CAACd,cAAc,CAAC;IAAA,CACnD,AAAC;IAEF,iEAAiE;IACjE,MAAMoB,eAAe,GAAGtD,gBAAgB,CAAC,CAAC,CAAC,AAAC;IAC5C,MAAMuD,GAAG,GAAG,IAAIC,IAAI,EAAE,AAAC;IACvB,IAAIF,eAAe,CAACG,QAAQ,CAACC,SAAS,GAAGH,GAAG,IAAID,eAAe,CAACG,QAAQ,CAACE,QAAQ,GAAGJ,GAAG,EAAE;QACvF,OAAO,IAAI,CAAC;KACb;IAED,wHAAwH;IAExH,OAAO;QACLnB,KAAK,EAAE,SAAS;QAChBE,2BAA2B,EAAEc,eAAe;QAC5Cf,wBAAwB,EAAEe,eAAe,CAAC,CAAC,CAAC;QAC5CrD,UAAU,EAAEkC,aAAa;QACzBnC,QAAQ;KACT,CAAC;CACH;AAED,SAAS8D,wCAAwC,CAACC,KAAY,EAAEC,GAAgC,EAAE;QAO7CD,GACC;IAPpD,MAAME,eAAe,GAAGD,GAAG,CAACE,YAAY,CAACC,EAAE,AAAC;IAE5C,MAAMC,kCAAkC,GACtCL,KAAK,CAACM,UAAU,KAAK,MAAM,IAAIN,KAAK,CAACM,UAAU,KAAK,SAAS,GACzDN,KAAK,CAACO,cAAc,CAACH,EAAE,KAAKF,eAAe,GAC3C,KAAK,AAAC;IACZ,MAAMM,wCAAwC,GAAG,CAAC,EAACR,QAAAA,CAAAA,GACC,GADDA,KAAK,CAACS,QAAQ,CAC9DC,IAAI,CAAC,CAACC,OAAO,GAAKA,OAAO,CAACP,EAAE,KAAKF,eAAe;IAAA,CAAC,SAC3C,GAF0CF,KAAAA,CAE1C,GAF0CA,QAAAA,GACC,CAChDY,KAAK,SAAA,GAF0CZ,KAAAA,CAE1C,GAF0CA,KAExCU,IAAI,CAAC,CAACG,cAAc,GAAKA,cAAc,CAACb,KAAK,CAACI,EAAE,KAAKJ,KAAK,CAACI,EAAE;IAAA,CAAC,SAC1D,GAHoCJ,KAAAA,CAGpC,GAHoCA,aAG/Cc,WAAW,SAAA,GAHoCd,KAAAA,CAGpC,GAHoCA,KAGlCe,QAAQ,CAACC,UAAU,WAAA,CAACC,OAAO,CAAC,CAAA,AAAC;IAC9C,OAAOZ,kCAAkC,IAAIG,wCAAwC,CAAC;CACvF;AAED,eAAe7C,+CAA+C,CAC5DtC,YAAoB,EACa;IACjC,MAAM2E,KAAK,GAAG,MAAMkB,CAAAA,GAAAA,QAAmB,AAAE,CAAA,oBAAF,EAAE,AAAC;IAC1C,IAAIjB,GAAG,AAA6B,AAAC;IACrC,IAAI;QACFA,GAAG,GAAG,MAAMkB,SAAQ,SAAA,CAACC,SAAS,CAAC/F,YAAY,CAAC,CAAC;KAC9C,CAAC,OAAOuC,CAAC,EAAE;QACV,IAAIA,CAAC,YAAYyD,QAAY,aAAA,IAAIzD,CAAC,YAAY0D,KAAa,cAAA,EAAE;YAC3D,OAAO,IAAI,CAAC;SACb;QACD,MAAM1D,CAAC,CAAC;KACT;IACD,IAAI,CAACmC,wCAAwC,CAACC,KAAK,EAAEC,GAAG,CAAC,EAAE;QACzD,OAAO,IAAI,CAAC;KACb;IAED,MAAMsB,OAAO,GAAGC,CAAAA,GAAAA,wBAAe,AAAE,CAAA,gBAAF,EAAE,AAAC;IAClC,MAAMC,UAAU,GAAGC,CAAAA,GAAAA,wBAAmB,AAAS,CAAA,oBAAT,CAACH,OAAO,CAAC,AAAC;IAChD,MAAMI,GAAG,GAAGC,CAAAA,GAAAA,wBAAW,AAAwD,CAAA,YAAxD,CAACL,OAAO,EAAE,CAAC,4BAA4B,EAAElG,YAAY,CAAC,CAAC,CAAC,AAAC;IAChF,MAAMwG,MAAM,GAAGC,CAAAA,GAAAA,wBAAkB,AAAK,CAAA,mBAAL,CAACH,GAAG,CAAC,AAAC;IACvC,MAAM,CAACI,6BAA6B,EAAEC,6BAA6B,CAAC,GAAG,MAAMjD,OAAO,CAACC,GAAG,CAAC;QACvFiD,CAAAA,GAAAA,iCAAqC,AAAsB,CAAA,sCAAtB,CAAC5G,YAAY,EAAEwG,MAAM,CAAC;QAC3DK,CAAAA,GAAAA,iCAAqC,AAAc,CAAA,sCAAd,CAAC7G,YAAY,CAAC;KACpD,CAAC,AAAC;IAEH,MAAMW,8BAA8B,CAACJ,QAAQ,CAACP,YAAY,EAAE;QAC1DA,YAAY;QACZY,QAAQ,EAAEgE,GAAG,CAAChE,QAAQ;QACtBC,UAAU,EAAEuF,UAAU,CAACrD,aAAa;QACpCjC,gBAAgB,EAAE;YAAC4F,6BAA6B;YAAEC,6BAA6B;SAAC;KACjF,CAAC,CAAC;IAEH,OAAO;QACLzD,KAAK,EAAE,SAAS;QAChBE,2BAA2B,EAAE;YAACsD,6BAA6B;YAAEC,6BAA6B;SAAC;QAC3FxD,wBAAwB,EAAEuD,6BAA6B;QACvD7F,UAAU,EAAEuF,UAAU,CAACrD,aAAa;QACpCnC,QAAQ,EAAEgE,GAAG,CAAChE,QAAQ;KACvB,CAAC;CACH;AAIM,SAASvB,kBAAkB,CAChCyH,mBAA2B,EAC3B7C,eAAgC,EACxB;IACR,MAAMpD,UAAU,GAAG+C,CAAAA,GAAAA,wBAAgC,AAA4B,CAAA,iCAA5B,CAACK,eAAe,CAACpD,UAAU,CAAC,AAAC;IAChF,MAAMgD,WAAW,GAAGC,CAAAA,GAAAA,wBAAkC,AAA0C,CAAA,mCAA1C,CAACG,eAAe,CAACd,wBAAwB,CAAC,AAAC;IACjG,OAAO4D,CAAAA,GAAAA,wBAA4B,AAIlC,CAAA,6BAJkC,CACjClG,UAAU,EACVgD,WAAW,EACXmD,MAAM,CAACC,IAAI,CAACH,mBAAmB,EAAE,MAAM,CAAC,CACzC,CAAC;CACH"}