{"version": 3, "sources": ["../../../src/utils/interactive.ts"], "sourcesContent": ["import { env } from './env';\n\n/** @returns `true` if the process is interactive. */\nexport function isInteractive(): boolean {\n  return !env.CI && process.stdout.isTTY;\n}\n"], "names": ["isInteractive", "env", "CI", "process", "stdout", "isTTY"], "mappings": "AAAA;;;;QAGgBA,aAAa,GAAbA,aAAa;AAHT,IAAA,IAAO,WAAP,OAAO,CAAA;AAGpB,SAASA,aAAa,GAAY;IACvC,OAAO,CAACC,IAAG,IAAA,CAACC,EAAE,IAAIC,OAAO,CAACC,MAAM,CAACC,KAAK,CAAC;CACxC"}