{"version": 3, "sources": ["../../../src/utils/cocoapods.ts"], "sourcesContent": ["import { getPackageJson, PackageJSONConfig } from '@expo/config';\nimport JsonFile from '@expo/json-file';\nimport * as PackageManager from '@expo/package-manager';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { ensureDirectoryAsync } from './dir';\nimport { env } from './env';\nimport { AbortCommandError } from './errors';\nimport { logNewSection } from './ora';\nimport * as Log from '../log';\nimport { hashForDependencyMap } from '../prebuild/updatePackageJson';\n\ntype PackageChecksums = {\n  /** checksum for the `package.json` dependency object. */\n  dependencies: string;\n  /** checksum for the `package.json` devDependency object. */\n  devDependencies: string;\n};\n\nconst PROJECT_PREBUILD_SETTINGS = '.expo/prebuild';\nconst CACHED_PACKAGE_JSON = 'cached-packages.json';\n\nfunction getTempPrebuildFolder(projectRoot: string): string {\n  return path.join(projectRoot, PROJECT_PREBUILD_SETTINGS);\n}\n\nfunction hasNewDependenciesSinceLastBuild(\n  projectRoot: string,\n  packageChecksums: PackageChecksums\n): boolean {\n  // TODO: Maybe comparing lock files would be better...\n  const templateDirectory = getTempPrebuildFolder(projectRoot);\n  const tempPkgJsonPath = path.join(templateDirectory, CACHED_PACKAGE_JSON);\n  if (!fs.existsSync(tempPkgJsonPath)) {\n    return true;\n  }\n  const { dependencies, devDependencies } = JsonFile.read(tempPkgJsonPath);\n  // Only change the dependencies if the normalized hash changes, this helps to reduce meaningless changes.\n  const hasNewDependencies = packageChecksums.dependencies !== dependencies;\n  const hasNewDevDependencies = packageChecksums.devDependencies !== devDependencies;\n\n  return hasNewDependencies || hasNewDevDependencies;\n}\n\nfunction createPackageChecksums(pkg: PackageJSONConfig): PackageChecksums {\n  return {\n    dependencies: hashForDependencyMap(pkg.dependencies || {}),\n    devDependencies: hashForDependencyMap(pkg.devDependencies || {}),\n  };\n}\n\n/** @returns `true` if the package.json dependency hash does not match the cached hash from the last run. */\nexport async function hasPackageJsonDependencyListChangedAsync(\n  projectRoot: string\n): Promise<boolean> {\n  const pkg = getPackageJson(projectRoot);\n\n  const packages = createPackageChecksums(pkg);\n  const hasNewDependencies = hasNewDependenciesSinceLastBuild(projectRoot, packages);\n\n  // Cache package.json\n  await ensureDirectoryAsync(getTempPrebuildFolder(projectRoot));\n  const templateDirectory = path.join(getTempPrebuildFolder(projectRoot), CACHED_PACKAGE_JSON);\n  await JsonFile.writeAsync(templateDirectory, packages);\n\n  return hasNewDependencies;\n}\n\nexport async function installCocoaPodsAsync(projectRoot: string): Promise<boolean> {\n  let step = logNewSection('Installing CocoaPods...');\n  if (process.platform !== 'darwin') {\n    step.succeed('Skipped installing CocoaPods because operating system is not on macOS.');\n    return false;\n  }\n\n  const packageManager = new PackageManager.CocoaPodsPackageManager({\n    cwd: path.join(projectRoot, 'ios'),\n    silent: !(env.EXPO_DEBUG || env.CI),\n  });\n\n  if (!(await packageManager.isCLIInstalledAsync())) {\n    try {\n      // prompt user -- do you want to install cocoapods right now?\n      step.text = 'CocoaPods CLI not found in your PATH, installing it now.';\n      step.stopAndPersist();\n      await PackageManager.CocoaPodsPackageManager.installCLIAsync({\n        nonInteractive: true,\n        spawnOptions: {\n          ...packageManager.options,\n          // Don't silence this part\n          stdio: ['inherit', 'inherit', 'pipe'],\n        },\n      });\n      step.succeed('Installed CocoaPods CLI.');\n      step = logNewSection('Running `pod install` in the `ios` directory.');\n    } catch (error: any) {\n      step.stopAndPersist({\n        symbol: '⚠️ ',\n        text: chalk.red('Unable to install the CocoaPods CLI.'),\n      });\n      if (error instanceof PackageManager.CocoaPodsError) {\n        Log.log(error.message);\n      } else {\n        Log.log(`Unknown error: ${error.message}`);\n      }\n      return false;\n    }\n  }\n\n  try {\n    await packageManager.installAsync({ spinner: step });\n    // Create cached list for later\n    await hasPackageJsonDependencyListChangedAsync(projectRoot).catch(() => null);\n    step.succeed('Installed CocoaPods');\n    return true;\n  } catch (error: any) {\n    step.stopAndPersist({\n      symbol: '⚠️ ',\n      text: chalk.red('Something went wrong running `pod install` in the `ios` directory.'),\n    });\n    if (error instanceof PackageManager.CocoaPodsError) {\n      Log.log(error.message);\n    } else {\n      Log.log(`Unknown error: ${error.message}`);\n    }\n    return false;\n  }\n}\n\nfunction doesProjectUseCocoaPods(projectRoot: string): boolean {\n  return fs.existsSync(path.join(projectRoot, 'ios', 'Podfile'));\n}\n\nfunction isLockfileCreated(projectRoot: string): boolean {\n  const podfileLockPath = path.join(projectRoot, 'ios', 'Podfile.lock');\n  return fs.existsSync(podfileLockPath);\n}\n\nfunction isPodFolderCreated(projectRoot: string): boolean {\n  const podFolderPath = path.join(projectRoot, 'ios', 'Pods');\n  return fs.existsSync(podFolderPath);\n}\n\n// TODO: Same process but with app.config changes + default plugins.\n// This will ensure the user is prompted for extra setup.\nexport async function maybePromptToSyncPodsAsync(projectRoot: string) {\n  if (!doesProjectUseCocoaPods(projectRoot)) {\n    // Project does not use CocoaPods\n    return;\n  }\n  if (!isLockfileCreated(projectRoot) || !isPodFolderCreated(projectRoot)) {\n    if (!(await installCocoaPodsAsync(projectRoot))) {\n      throw new AbortCommandError();\n    }\n    return;\n  }\n\n  // Getting autolinked packages can be heavy, optimize around checking every time.\n  if (!(await hasPackageJsonDependencyListChangedAsync(projectRoot))) {\n    return;\n  }\n\n  await promptToInstallPodsAsync(projectRoot, []);\n}\n\nasync function promptToInstallPodsAsync(projectRoot: string, missingPods?: string[]) {\n  if (missingPods?.length) {\n    Log.log(\n      `Could not find the following native modules: ${missingPods\n        .map((pod) => chalk.bold(pod))\n        .join(', ')}. Did you forget to run \"${chalk.bold('pod install')}\" ?`\n    );\n  }\n\n  try {\n    if (!(await installCocoaPodsAsync(projectRoot))) {\n      throw new AbortCommandError();\n    }\n  } catch (error) {\n    await fs.promises.rm(path.join(getTempPrebuildFolder(projectRoot), CACHED_PACKAGE_JSON), {\n      recursive: true,\n      force: true,\n    });\n    throw error;\n  }\n}\n"], "names": ["hasPackageJsonDependencyListChangedAsync", "installCocoaPodsAsync", "maybePromptToSyncPodsAsync", "PackageManager", "Log", "PROJECT_PREBUILD_SETTINGS", "CACHED_PACKAGE_JSON", "getTempPrebuildFolder", "projectRoot", "path", "join", "hasNewDependenciesSinceLastBuild", "packageChecksums", "templateDirectory", "tempPkgJsonPath", "fs", "existsSync", "dependencies", "devDependencies", "JsonFile", "read", "hasNewDependencies", "hasNewDevDependencies", "createPackageChecksums", "pkg", "hashForDependencyMap", "getPackageJson", "packages", "ensureDirectoryAsync", "writeAsync", "step", "logNewSection", "process", "platform", "succeed", "packageManager", "CocoaPodsPackageManager", "cwd", "silent", "env", "EXPO_DEBUG", "CI", "isCLIInstalledAsync", "text", "stopAndPersist", "installCLIAsync", "nonInteractive", "spawnOptions", "options", "stdio", "error", "symbol", "chalk", "red", "CocoaPodsError", "log", "message", "installAsync", "spinner", "catch", "doesProjectUseCocoaPods", "isLockfileCreated", "podfileLockPath", "isPodFolderCreated", "podFolderPath", "AbortCommandError", "promptToInstallPodsAsync", "missingPods", "length", "map", "pod", "bold", "promises", "rm", "recursive", "force"], "mappings": "AAAA;;;;QAsDsBA,wCAAwC,GAAxCA,wCAAwC;QAgBxCC,qBAAqB,GAArBA,qBAAqB;QA6ErBC,0BAA0B,GAA1BA,0BAA0B;AAnJE,IAAA,OAAc,WAAd,cAAc,CAAA;AAC3C,IAAA,SAAiB,kCAAjB,iBAAiB,EAAA;AAC1BC,IAAAA,cAAc,mCAAM,uBAAuB,EAA7B;AACR,IAAA,MAAO,kCAAP,OAAO,EAAA;AACV,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEc,IAAA,IAAO,WAAP,OAAO,CAAA;AACxB,IAAA,IAAO,WAAP,OAAO,CAAA;AACO,IAAA,OAAU,WAAV,UAAU,CAAA;AACd,IAAA,IAAO,WAAP,OAAO,CAAA;AACzBC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACsB,IAAA,kBAA+B,WAA/B,+BAA+B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE,MAAMC,yBAAyB,GAAG,gBAAgB,AAAC;AACnD,MAAMC,mBAAmB,GAAG,sBAAsB,AAAC;AAEnD,SAASC,qBAAqB,CAACC,WAAmB,EAAU;IAC1D,OAAOC,KAAI,QAAA,CAACC,IAAI,CAACF,WAAW,EAAEH,yBAAyB,CAAC,CAAC;CAC1D;AAED,SAASM,gCAAgC,CACvCH,WAAmB,EACnBI,gBAAkC,EACzB;IACT,sDAAsD;IACtD,MAAMC,iBAAiB,GAAGN,qBAAqB,CAACC,WAAW,CAAC,AAAC;IAC7D,MAAMM,eAAe,GAAGL,KAAI,QAAA,CAACC,IAAI,CAACG,iBAAiB,EAAEP,mBAAmB,CAAC,AAAC;IAC1E,IAAI,CAACS,GAAE,QAAA,CAACC,UAAU,CAACF,eAAe,CAAC,EAAE;QACnC,OAAO,IAAI,CAAC;KACb;IACD,MAAM,EAAEG,YAAY,CAAA,EAAEC,eAAe,CAAA,EAAE,GAAGC,SAAQ,QAAA,CAACC,IAAI,CAACN,eAAe,CAAC,AAAC;IACzE,yGAAyG;IACzG,MAAMO,kBAAkB,GAAGT,gBAAgB,CAACK,YAAY,KAAKA,YAAY,AAAC;IAC1E,MAAMK,qBAAqB,GAAGV,gBAAgB,CAACM,eAAe,KAAKA,eAAe,AAAC;IAEnF,OAAOG,kBAAkB,IAAIC,qBAAqB,CAAC;CACpD;AAED,SAASC,sBAAsB,CAACC,GAAsB,EAAoB;IACxE,OAAO;QACLP,YAAY,EAAEQ,CAAAA,GAAAA,kBAAoB,AAAwB,CAAA,qBAAxB,CAACD,GAAG,CAACP,YAAY,IAAI,EAAE,CAAC;QAC1DC,eAAe,EAAEO,CAAAA,GAAAA,kBAAoB,AAA2B,CAAA,qBAA3B,CAACD,GAAG,CAACN,eAAe,IAAI,EAAE,CAAC;KACjE,CAAC;CACH;AAGM,eAAelB,wCAAwC,CAC5DQ,WAAmB,EACD;IAClB,MAAMgB,GAAG,GAAGE,CAAAA,GAAAA,OAAc,AAAa,CAAA,eAAb,CAAClB,WAAW,CAAC,AAAC;IAExC,MAAMmB,QAAQ,GAAGJ,sBAAsB,CAACC,GAAG,CAAC,AAAC;IAC7C,MAAMH,kBAAkB,GAAGV,gCAAgC,CAACH,WAAW,EAAEmB,QAAQ,CAAC,AAAC;IAEnF,qBAAqB;IACrB,MAAMC,CAAAA,GAAAA,IAAoB,AAAoC,CAAA,qBAApC,CAACrB,qBAAqB,CAACC,WAAW,CAAC,CAAC,CAAC;IAC/D,MAAMK,iBAAiB,GAAGJ,KAAI,QAAA,CAACC,IAAI,CAACH,qBAAqB,CAACC,WAAW,CAAC,EAAEF,mBAAmB,CAAC,AAAC;IAC7F,MAAMa,SAAQ,QAAA,CAACU,UAAU,CAAChB,iBAAiB,EAAEc,QAAQ,CAAC,CAAC;IAEvD,OAAON,kBAAkB,CAAC;CAC3B;AAEM,eAAepB,qBAAqB,CAACO,WAAmB,EAAoB;IACjF,IAAIsB,IAAI,GAAGC,CAAAA,GAAAA,IAAa,AAA2B,CAAA,cAA3B,CAAC,yBAAyB,CAAC,AAAC;IACpD,IAAIC,OAAO,CAACC,QAAQ,KAAK,QAAQ,EAAE;QACjCH,IAAI,CAACI,OAAO,CAAC,wEAAwE,CAAC,CAAC;QACvF,OAAO,KAAK,CAAC;KACd;IAED,MAAMC,cAAc,GAAG,IAAIhC,cAAc,CAACiC,uBAAuB,CAAC;QAChEC,GAAG,EAAE5B,KAAI,QAAA,CAACC,IAAI,CAACF,WAAW,EAAE,KAAK,CAAC;QAClC8B,MAAM,EAAE,CAAC,CAACC,IAAG,IAAA,CAACC,UAAU,IAAID,IAAG,IAAA,CAACE,EAAE,CAAC;KACpC,CAAC,AAAC;IAEH,IAAI,CAAE,MAAMN,cAAc,CAACO,mBAAmB,EAAE,AAAC,EAAE;QACjD,IAAI;YACF,6DAA6D;YAC7DZ,IAAI,CAACa,IAAI,GAAG,0DAA0D,CAAC;YACvEb,IAAI,CAACc,cAAc,EAAE,CAAC;YACtB,MAAMzC,cAAc,CAACiC,uBAAuB,CAACS,eAAe,CAAC;gBAC3DC,cAAc,EAAE,IAAI;gBACpBC,YAAY,EAAE;oBACZ,GAAGZ,cAAc,CAACa,OAAO;oBACzB,0BAA0B;oBAC1BC,KAAK,EAAE;wBAAC,SAAS;wBAAE,SAAS;wBAAE,MAAM;qBAAC;iBACtC;aACF,CAAC,CAAC;YACHnB,IAAI,CAACI,OAAO,CAAC,0BAA0B,CAAC,CAAC;YACzCJ,IAAI,GAAGC,CAAAA,GAAAA,IAAa,AAAiD,CAAA,cAAjD,CAAC,+CAA+C,CAAC,CAAC;SACvE,CAAC,OAAOmB,KAAK,EAAO;YACnBpB,IAAI,CAACc,cAAc,CAAC;gBAClBO,MAAM,EAAE,eAAK;gBACTR,IAAA,EAAES,MAAK,QAAA,CAACC,GAAG,CAAC,sCAAsC,CAAC;aACxD,CAAC,CAAC;YACH,IAAIH,KAAK,YAAY/C,cAAc,CAACmD,cAAc,EAAE;gBAClDlD,GAAG,CAACmD,GAAG,CAACL,KAAK,CAACM,OAAO,CAAC,CAAC;aACxB,MAAM;gBACLpD,GAAG,CAACmD,GAAG,CAAC,CAAC,eAAe,EAAEL,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC;aAC5C;YACD,OAAO,KAAK,CAAC;SACd;KACF;IAED,IAAI;QACF,MAAMrB,cAAc,CAACsB,YAAY,CAAC;YAAEC,OAAO,EAAE5B,IAAI;SAAE,CAAC,CAAC;QACrD,+BAA+B;QAC/B,MAAM9B,wCAAwC,CAACQ,WAAW,CAAC,CAACmD,KAAK,CAAC,IAAM,IAAI;QAAA,CAAC,CAAC;QAC9E7B,IAAI,CAACI,OAAO,CAAC,qBAAqB,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;KACb,CAAC,OAAOgB,KAAK,EAAO;QACnBpB,IAAI,CAACc,cAAc,CAAC;YAClBO,MAAM,EAAE,eAAK;YACbR,IAAI,EAAES,MAAK,QAAA,CAACC,GAAG,CAAC,oEAAoE,CAAC;SACtF,CAAC,CAAC;QACH,IAAIH,KAAK,YAAY/C,cAAc,CAACmD,cAAc,EAAE;YAClDlD,GAAG,CAACmD,GAAG,CAACL,KAAK,CAACM,OAAO,CAAC,CAAC;SACxB,MAAM;YACLpD,GAAG,CAACmD,GAAG,CAAC,CAAC,eAAe,EAAEL,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC;SAC5C;QACD,OAAO,KAAK,CAAC;KACd;CACF;AAED,SAASI,uBAAuB,CAACpD,WAAmB,EAAW;IAC7D,OAAOO,GAAE,QAAA,CAACC,UAAU,CAACP,KAAI,QAAA,CAACC,IAAI,CAACF,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;CAChE;AAED,SAASqD,iBAAiB,CAACrD,WAAmB,EAAW;IACvD,MAAMsD,eAAe,GAAGrD,KAAI,QAAA,CAACC,IAAI,CAACF,WAAW,EAAE,KAAK,EAAE,cAAc,CAAC,AAAC;IACtE,OAAOO,GAAE,QAAA,CAACC,UAAU,CAAC8C,eAAe,CAAC,CAAC;CACvC;AAED,SAASC,kBAAkB,CAACvD,WAAmB,EAAW;IACxD,MAAMwD,aAAa,GAAGvD,KAAI,QAAA,CAACC,IAAI,CAACF,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC,AAAC;IAC5D,OAAOO,GAAE,QAAA,CAACC,UAAU,CAACgD,aAAa,CAAC,CAAC;CACrC;AAIM,eAAe9D,0BAA0B,CAACM,WAAmB,EAAE;IACpE,IAAI,CAACoD,uBAAuB,CAACpD,WAAW,CAAC,EAAE;QACzC,iCAAiC;QACjC,OAAO;KACR;IACD,IAAI,CAACqD,iBAAiB,CAACrD,WAAW,CAAC,IAAI,CAACuD,kBAAkB,CAACvD,WAAW,CAAC,EAAE;QACvE,IAAI,CAAE,MAAMP,qBAAqB,CAACO,WAAW,CAAC,AAAC,EAAE;YAC/C,MAAM,IAAIyD,OAAiB,kBAAA,EAAE,CAAC;SAC/B;QACD,OAAO;KACR;IAED,iFAAiF;IACjF,IAAI,CAAE,MAAMjE,wCAAwC,CAACQ,WAAW,CAAC,AAAC,EAAE;QAClE,OAAO;KACR;IAED,MAAM0D,wBAAwB,CAAC1D,WAAW,EAAE,EAAE,CAAC,CAAC;CACjD;AAED,eAAe0D,wBAAwB,CAAC1D,WAAmB,EAAE2D,WAAsB,EAAE;IACnF,IAAIA,WAAW,QAAQ,GAAnBA,KAAAA,CAAmB,GAAnBA,WAAW,CAAEC,MAAM,EAAE;QACvBhE,GAAG,CAACmD,GAAG,CACL,CAAC,6CAA6C,EAAEY,WAAW,CACxDE,GAAG,CAAC,CAACC,GAAG,GAAKlB,MAAK,QAAA,CAACmB,IAAI,CAACD,GAAG,CAAC;QAAA,CAAC,CAC7B5D,IAAI,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE0C,MAAK,QAAA,CAACmB,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CACxE,CAAC;KACH;IAED,IAAI;QACF,IAAI,CAAE,MAAMtE,qBAAqB,CAACO,WAAW,CAAC,AAAC,EAAE;YAC/C,MAAM,IAAIyD,OAAiB,kBAAA,EAAE,CAAC;SAC/B;KACF,CAAC,OAAOf,KAAK,EAAE;QACd,MAAMnC,GAAE,QAAA,CAACyD,QAAQ,CAACC,EAAE,CAAChE,KAAI,QAAA,CAACC,IAAI,CAACH,qBAAqB,CAACC,WAAW,CAAC,EAAEF,mBAAmB,CAAC,EAAE;YACvFoE,SAAS,EAAE,IAAI;YACfC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;QACH,MAAMzB,KAAK,CAAC;KACb;CACF"}