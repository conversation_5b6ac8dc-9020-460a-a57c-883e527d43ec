{"version": 3, "sources": ["../../../../../../src/start/server/middleware/inspector/LaunchBrowserImplMacOS.ts"], "sourcesContent": ["import * as osascript from '@expo/osascript';\nimport { spawn, type ChildProcess } from 'child_process';\nimport { sync as globSync } from 'glob';\nimport path from 'path';\n\nimport {\n  LaunchBrowserTypes,\n  type LaunchBrowser,\n  type LaunchBrowserInstance,\n  LaunchBrowserTypesEnum,\n} from './LaunchBrowser.types';\n\n/**\n * Browser implementation for macOS\n */\nexport default class LaunchBrowserImplMacOS implements LaunchBrowser, LaunchBrowserInstance {\n  private _process: ChildProcess | undefined;\n\n  MAP = {\n    [LaunchBrowserTypesEnum.CHROME]: 'google chrome',\n    [LaunchBrowserTypesEnum.EDGE]: 'microsoft edge',\n    [LaunchBrowserTypesEnum.BRAVE]: 'brave browser',\n  };\n\n  async isSupportedBrowser(browserType: LaunchBrowserTypes): Promise<boolean> {\n    let result = false;\n    try {\n      await osascript.execAsync(`id of application \"${this.MAP[browserType]}\"`);\n      result = true;\n    } catch {\n      result = false;\n    }\n    return result;\n  }\n\n  async createTempBrowserDir(baseDirName: string) {\n    return path.join(require('temp-dir'), baseDirName);\n  }\n\n  async launchAsync(\n    browserType: LaunchBrowserTypes,\n    args: string[]\n  ): Promise<LaunchBrowserInstance> {\n    const appDirectory = await osascript.execAsync(\n      `POSIX path of (path to application \"${this.MAP[browserType]}\")`\n    );\n    const appPath = globSync('Contents/MacOS/*', { cwd: appDirectory.trim(), absolute: true })?.[0];\n    if (!appPath) {\n      throw new Error(`Cannot find application path from ${appDirectory}Contents/MacOS`);\n    }\n    this._process = spawn(appPath, args, { stdio: 'ignore' });\n\n    return this;\n  }\n\n  async close(): Promise<void> {\n    this._process?.kill();\n    this._process = undefined;\n  }\n}\n"], "names": ["osascript", "LaunchBrowserImplMacOS", "MAP", "LaunchBrowserTypesEnum", "CHROME", "EDGE", "BRAVE", "isSupportedBrowser", "browserType", "result", "execAsync", "createTempBrowserDir", "baseDirName", "path", "join", "require", "launchAsync", "args", "globSync", "appDirectory", "appPath", "cwd", "trim", "absolute", "Error", "_process", "spawn", "stdio", "close", "kill", "undefined"], "mappings": "AAAA;;;;;AAAYA,IAAAA,SAAS,mCAAM,iBAAiB,EAAvB;AACoB,IAAA,aAAe,WAAf,eAAe,CAAA;AACvB,IAAA,KAAM,WAAN,MAAM,CAAA;AACtB,IAAA,KAAM,kCAAN,MAAM,EAAA;AAOhB,IAAA,mBAAuB,WAAvB,uBAAuB,CAAA;AAKf,MAAMC,sBAAsB;IAGzCC,GAAG,GAAG;QACJ,CAACC,mBAAsB,uBAAA,CAACC,MAAM,CAAC,EAAE,eAAe;QAChD,CAACD,mBAAsB,uBAAA,CAACE,IAAI,CAAC,EAAE,gBAAgB;QAC/C,CAACF,mBAAsB,uBAAA,CAACG,KAAK,CAAC,EAAE,eAAe;KAChD,CAAC;IAEF,MAAMC,kBAAkB,CAACC,WAA+B,EAAoB;QAC1E,IAAIC,MAAM,GAAG,KAAK,AAAC;QACnB,IAAI;YACF,MAAMT,SAAS,CAACU,SAAS,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAACR,GAAG,CAACM,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1EC,MAAM,GAAG,IAAI,CAAC;SACf,CAAC,OAAM;YACNA,MAAM,GAAG,KAAK,CAAC;SAChB;QACD,OAAOA,MAAM,CAAC;KACf;IAED,MAAME,oBAAoB,CAACC,WAAmB,EAAE;QAC9C,OAAOC,KAAI,QAAA,CAACC,IAAI,CAACC,OAAO,CAAC,UAAU,CAAC,EAAEH,WAAW,CAAC,CAAC;KACpD;IAED,MAAMI,WAAW,CACfR,WAA+B,EAC/BS,IAAc,EACkB;YAIhBC,GAA0E;QAH1F,MAAMC,YAAY,GAAG,MAAMnB,SAAS,CAACU,SAAS,CAC5C,CAAC,oCAAoC,EAAE,IAAI,CAACR,GAAG,CAACM,WAAW,CAAC,CAAC,EAAE,CAAC,CACjE,AAAC;QACF,MAAMY,OAAO,GAAGF,CAAAA,GAA0E,GAA1EA,CAAAA,GAAAA,KAAQ,AAAkE,CAAA,KAAlE,CAAC,kBAAkB,EAAE;YAAEG,GAAG,EAAEF,YAAY,CAACG,IAAI,EAAE;YAAEC,QAAQ,EAAE,IAAI;SAAE,CAAC,SAAK,GAA/EL,KAAAA,CAA+E,GAA/EA,GAA0E,AAAE,CAAC,CAAC,CAAC,AAAC;QAChG,IAAI,CAACE,OAAO,EAAE;YACZ,MAAM,IAAII,KAAK,CAAC,CAAC,kCAAkC,EAAEL,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC;SACpF;QACD,IAAI,CAACM,QAAQ,GAAGC,CAAAA,GAAAA,aAAK,AAAoC,CAAA,MAApC,CAACN,OAAO,EAAEH,IAAI,EAAE;YAAEU,KAAK,EAAE,QAAQ;SAAE,CAAC,CAAC;QAE1D,OAAO,IAAI,CAAC;KACb;IAED,MAAMC,KAAK,GAAkB;YAC3B,GAAa;QAAb,CAAA,GAAa,GAAb,IAAI,CAACH,QAAQ,SAAM,GAAnB,KAAA,CAAmB,GAAnB,GAAa,CAAEI,IAAI,EAAE,AAxDzB,CAwD0B;QACtB,IAAI,CAACJ,QAAQ,GAAGK,SAAS,CAAC;KAC3B;CACF;kBA5CoB7B,sBAAsB"}