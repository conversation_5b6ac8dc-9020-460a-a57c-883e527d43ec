{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/inspectorHandlers/NetworkResponse.ts"], "sourcesContent": ["import type { Protocol } from 'devtools-protocol';\n\nimport {\n  <PERSON>dp<PERSON><PERSON><PERSON>,\n  InspectorHandler,\n  DebuggerMetadata,\n  DeviceRequest,\n  DebuggerRequest,\n  DebuggerResponse,\n  DeviceResponse,\n} from './types';\nimport { respond } from './utils';\n\nexport class NetworkResponseHandler implements InspectorHandler {\n  /** All known responses, mapped by request id */\n  storage = new Map<string, DebuggerResponse<NetworkGetResponseBody>['result']>();\n\n  onDeviceMessage(message: DeviceRequest<NetworkReceivedResponseBody>) {\n    if (message.method === 'Expo(Network.receivedResponseBody)') {\n      const { requestId, ...requestInfo } = message.params;\n      this.storage.set(requestId, requestInfo);\n      return true;\n    }\n\n    return false;\n  }\n\n  onDebuggerMessage(\n    message: DebuggerRequest<NetworkGetResponseBody>,\n    { socket }: DebuggerMetadata\n  ) {\n    if (\n      message.method === 'Network.getResponseBody' &&\n      this.storage.has(message.params.requestId)\n    ) {\n      return respond<DeviceResponse<NetworkGetResponseBody>>(socket, {\n        id: message.id,\n        result: this.storage.get(message.params.requestId)!,\n      });\n    }\n\n    return false;\n  }\n}\n\n/** Custom message to transfer the response body data to the proxy */\nexport type NetworkReceivedResponseBody = CdpMessage<\n  'Expo(Network.receivedResponseBody)',\n  Protocol.Network.GetResponseBodyRequest & Protocol.Network.GetResponseBodyResponse,\n  never\n>;\n\n/** @see https://chromedevtools.github.io/devtools-protocol/1-2/Network/#method-getResponseBody */\nexport type NetworkGetResponseBody = CdpMessage<\n  'Network.getResponseBody',\n  Protocol.Network.GetResponseBodyRequest,\n  Protocol.Network.GetResponseBodyResponse\n>;\n"], "names": ["NetworkResponseHandler", "storage", "Map", "onDeviceMessage", "message", "method", "requestId", "requestInfo", "params", "set", "onDebuggerMessage", "socket", "has", "respond", "id", "result", "get"], "mappings": "AAAA;;;;AAWwB,IAAA,MAAS,WAAT,SAAS,CAAA;AAE1B,MAAMA,sBAAsB;IACjC,gDAAgD,CAChDC,OAAO,GAAG,IAAIC,GAAG,EAA8D,CAAC;IAEhFC,eAAe,CAACC,OAAmD,EAAE;QACnE,IAAIA,OAAO,CAACC,MAAM,KAAK,oCAAoC,EAAE;YAC3D,MAAM,EAAEC,SAAS,CAAA,EAAE,GAAGC,WAAW,EAAE,GAAGH,OAAO,CAACI,MAAM,AAAC;YACrD,IAAI,CAACP,OAAO,CAACQ,GAAG,CAACH,SAAS,EAAEC,WAAW,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;SACb;QAED,OAAO,KAAK,CAAC;KACd;IAEDG,iBAAiB,CACfN,OAAgD,EAChD,EAAEO,MAAM,CAAA,EAAoB,EAC5B;QACA,IACEP,OAAO,CAACC,MAAM,KAAK,yBAAyB,IAC5C,IAAI,CAACJ,OAAO,CAACW,GAAG,CAACR,OAAO,CAACI,MAAM,CAACF,SAAS,CAAC,EAC1C;YACA,OAAOO,CAAAA,GAAAA,MAAO,AAGZ,CAAA,QAHY,CAAyCF,MAAM,EAAE;gBAC7DG,EAAE,EAAEV,OAAO,CAACU,EAAE;gBACdC,MAAM,EAAE,IAAI,CAACd,OAAO,CAACe,GAAG,CAACZ,OAAO,CAACI,MAAM,CAACF,SAAS,CAAC;aACnD,CAAC,CAAC;SACJ;QAED,OAAO,KAAK,CAAC;KACd;CACF;QA9BYN,sBAAsB,GAAtBA,sBAAsB"}