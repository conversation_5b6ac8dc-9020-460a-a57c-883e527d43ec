{"version": 3, "sources": ["../../../../src/start/server/platformBundlers.ts"], "sourcesContent": ["import { ExpoConfig, Platform } from '@expo/config';\nimport resolveFrom from 'resolve-from';\n\n/** Which bundler each platform should use. */\nexport type PlatformBundlers = Record<Platform, 'metro' | 'webpack'>;\n\n/** Get the platform bundlers mapping. */\nexport function getPlatformBundlers(\n  projectRoot: string,\n  exp: Partial<ExpoConfig>\n): PlatformBundlers {\n  /**\n   * SDK 50+: The web bundler is dynamic based upon the presence of the `@expo/webpack-config` package.\n   */\n  let web = exp.web?.bundler;\n  if (!web) {\n    const resolved = resolveFrom.silent(projectRoot, '@expo/webpack-config/package.json');\n    web = resolved ? 'webpack' : 'metro';\n  }\n\n  return {\n    // @ts-expect-error: not on type yet\n    ios: exp.ios?.bundler ?? 'metro',\n    // @ts-expect-error: not on type yet\n    android: exp.android?.bundler ?? 'metro',\n    web,\n  };\n}\n"], "names": ["getPlatformBundlers", "projectRoot", "exp", "web", "bundler", "resolved", "resolveFrom", "silent", "ios", "android"], "mappings": "AAAA;;;;QAOgBA,mBAAmB,GAAnBA,mBAAmB;AANX,IAAA,YAAc,kCAAd,cAAc,EAAA;;;;;;AAM/B,SAASA,mBAAmB,CACjCC,WAAmB,EACnBC,GAAwB,EACN;QAIRA,GAAO,EAQVA,IAAO,EAEHA,IAAW;IAbtB;;KAEG,CACH,IAAIC,GAAG,GAAGD,CAAAA,GAAO,GAAPA,GAAG,CAACC,GAAG,SAAS,GAAhBD,KAAAA,CAAgB,GAAhBA,GAAO,CAAEE,OAAO,AAAC;IAC3B,IAAI,CAACD,GAAG,EAAE;QACR,MAAME,QAAQ,GAAGC,YAAW,QAAA,CAACC,MAAM,CAACN,WAAW,EAAE,mCAAmC,CAAC,AAAC;QACtFE,GAAG,GAAGE,QAAQ,GAAG,SAAS,GAAG,OAAO,CAAC;KACtC;QAIMH,IAAgB,EAEZA,IAAoB;IAJ/B,OAAO;QACL,oCAAoC;QACpCM,GAAG,EAAEN,CAAAA,IAAgB,GAAhBA,CAAAA,IAAO,GAAPA,GAAG,CAACM,GAAG,SAAS,GAAhBN,KAAAA,CAAgB,GAAhBA,IAAO,CAAEE,OAAO,YAAhBF,IAAgB,GAAI,OAAO;QAChC,oCAAoC;QACpCO,OAAO,EAAEP,CAAAA,IAAoB,GAApBA,CAAAA,IAAW,GAAXA,GAAG,CAACO,OAAO,SAAS,GAApBP,KAAAA,CAAoB,GAApBA,IAAW,CAAEE,OAAO,YAApBF,IAAoB,GAAI,OAAO;QACxCC,GAAG;KACJ,CAAC;CACH"}