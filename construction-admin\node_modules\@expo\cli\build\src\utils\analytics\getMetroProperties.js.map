{"version": 3, "sources": ["../../../../src/utils/analytics/getMetroProperties.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\n\n/**\n * Get the unstable / experimental properties used within the Metro config.\n * Note that this should match `metro-config`, but uses newer features that are not yet typed.\n *\n * @see https://github.com/facebook/metro/blob/1d51ffd33f54dba25c54b49ff059543dac519f21/packages/metro-config/src/configTypes.flow.js\n */\nexport function getMetroProperties(\n  projectRoot: string,\n  exp: ExpoConfig,\n  metroConfig: Record<string, any> = {}\n) {\n  return {\n    sdkVersion: exp.sdkVersion,\n    metroVersion: require('metro/package.json').version,\n\n    fileMapCacheManagerFactory:\n      Boolean(metroConfig.unstable_fileMapCacheManagerFactory) || undefined, // CacheManagerFactory\n    perfLogger: Boolean(metroConfig.unstable_perfLogger) || undefined, // PerfLoggerFactory\n\n    resolverEnableSymlinks: metroConfig.resolver?.unstable_enableSymlinks, // boolean\n    resolverConditionNames: metroConfig.resolver?.unstable_conditionNames, // string[]\n    resolverConditionsByPlatform: metroConfig.resolver?.unstable_conditionsByPlatform, // { [platform: string]: string[] }\n    resolverEnablePackageExports: metroConfig.resolver?.unstable_enablePackageExports, // boolean\n\n    serverImportBundleSupport: metroConfig.server?.experimentalImportBundleSupport, // boolean\n    serverServerRoot: Boolean(metroConfig.server?.unstable_serverRoot) || undefined, // string | null\n\n    transformerCollectDependenciesPath: metroConfig.transformer?.unstable_collectDependenciesPath, // string\n    transformerDependencyMapReservedName:\n      metroConfig.transformer?.unstable_dependencyMapReservedName, // string | null\n    transformerDisableModuleWrapping: metroConfig.transformer?.unstable_disableModuleWrapping, // boolean\n    transformerDisableNormalizePseudoGlobals:\n      metroConfig.transformer?.unstable_disableNormalizePseudoGlobals, // boolean\n    transformerCompactOutput: metroConfig.transformer?.unstable_compactOutput, // boolean\n    transformerAllowRequireContext: metroConfig.transformer?.unstable_allowRequireContext, // boolean\n  };\n}\n"], "names": ["getMetroProperties", "projectRoot", "exp", "metroConfig", "sdkVersion", "metroVersion", "require", "version", "fileMapCacheManagerFactory", "Boolean", "unstable_fileMapCacheManagerFactory", "undefined", "perfLogger", "unstable_perfLogger", "resolverEnableSymlinks", "resolver", "unstable_enableSymlinks", "resolverConditionNames", "unstable_conditionNames", "resolverConditionsByPlatform", "unstable_conditionsByPlatform", "resolverEnablePackageExports", "unstable_enablePackageExports", "serverImportBundleSupport", "server", "experimentalImportBundleSupport", "serverServerRoot", "unstable_serverRoot", "transformerCollectDependenciesPath", "transformer", "unstable_collectDependenciesPath", "transformerDependencyMapReservedName", "unstable_dependencyMapReservedName", "transformerDisableModuleWrapping", "unstable_disableModuleWrapping", "transformerDisableNormalizePseudoGlobals", "unstable_disableNormalizePseudoGlobals", "transformerCompactOutput", "unstable_compactOutput", "transformerAllowRequireContext", "unstable_allowRequireContext"], "mappings": "AAAA;;;;QAQgBA,kBAAkB,GAAlBA,kBAAkB;AAA3B,SAASA,kBAAkB,CAChCC,WAAmB,EACnBC,GAAe,EACfC,WAAgC,GAAG,EAAE,EACrC;QAS0BA,GAAoB,EACpBA,IAAoB,EACdA,IAAoB,EACpBA,IAAoB,EAEvBA,IAAkB,EACnBA,IAAkB,EAERA,IAAuB,EAEzDA,IAAuB,EACSA,IAAuB,EAEvDA,IAAuB,EACCA,KAAuB,EACjBA,KAAuB;IAvBzD,OAAO;QACLC,UAAU,EAAEF,GAAG,CAACE,UAAU;QAC1BC,YAAY,EAAEC,OAAO,CAAC,oBAAoB,CAAC,CAACC,OAAO;QAEnDC,0BAA0B,EACxBC,OAAO,CAACN,WAAW,CAACO,mCAAmC,CAAC,IAAIC,SAAS;QACvEC,UAAU,EAAEH,OAAO,CAACN,WAAW,CAACU,mBAAmB,CAAC,IAAIF,SAAS;QAEjEG,sBAAsB,EAAEX,CAAAA,GAAoB,GAApBA,WAAW,CAACY,QAAQ,SAAyB,GAA7CZ,KAAAA,CAA6C,GAA7CA,GAAoB,CAAEa,uBAAuB;QACrEC,sBAAsB,EAAEd,CAAAA,IAAoB,GAApBA,WAAW,CAACY,QAAQ,SAAyB,GAA7CZ,KAAAA,CAA6C,GAA7CA,IAAoB,CAAEe,uBAAuB;QACrEC,4BAA4B,EAAEhB,CAAAA,IAAoB,GAApBA,WAAW,CAACY,QAAQ,SAA+B,GAAnDZ,KAAAA,CAAmD,GAAnDA,IAAoB,CAAEiB,6BAA6B;QACjFC,4BAA4B,EAAElB,CAAAA,IAAoB,GAApBA,WAAW,CAACY,QAAQ,SAA+B,GAAnDZ,KAAAA,CAAmD,GAAnDA,IAAoB,CAAEmB,6BAA6B;QAEjFC,yBAAyB,EAAEpB,CAAAA,IAAkB,GAAlBA,WAAW,CAACqB,MAAM,SAAiC,GAAnDrB,KAAAA,CAAmD,GAAnDA,IAAkB,CAAEsB,+BAA+B;QAC9EC,gBAAgB,EAAEjB,OAAO,CAACN,CAAAA,IAAkB,GAAlBA,WAAW,CAACqB,MAAM,SAAqB,GAAvCrB,KAAAA,CAAuC,GAAvCA,IAAkB,CAAEwB,mBAAmB,CAAC,IAAIhB,SAAS;QAE/EiB,kCAAkC,EAAEzB,CAAAA,IAAuB,GAAvBA,WAAW,CAAC0B,WAAW,SAAkC,GAAzD1B,KAAAA,CAAyD,GAAzDA,IAAuB,CAAE2B,gCAAgC;QAC7FC,oCAAoC,EAClC5B,CAAAA,IAAuB,GAAvBA,WAAW,CAAC0B,WAAW,SAAoC,GAA3D1B,KAAAA,CAA2D,GAA3DA,IAAuB,CAAE6B,kCAAkC;QAC7DC,gCAAgC,EAAE9B,CAAAA,IAAuB,GAAvBA,WAAW,CAAC0B,WAAW,SAAgC,GAAvD1B,KAAAA,CAAuD,GAAvDA,IAAuB,CAAE+B,8BAA8B;QACzFC,wCAAwC,EACtChC,CAAAA,IAAuB,GAAvBA,WAAW,CAAC0B,WAAW,SAAwC,GAA/D1B,KAAAA,CAA+D,GAA/DA,IAAuB,CAAEiC,sCAAsC;QACjEC,wBAAwB,EAAElC,CAAAA,KAAuB,GAAvBA,WAAW,CAAC0B,WAAW,SAAwB,GAA/C1B,KAAAA,CAA+C,GAA/CA,KAAuB,CAAEmC,sBAAsB;QACzEC,8BAA8B,EAAEpC,CAAAA,KAAuB,GAAvBA,WAAW,CAAC0B,WAAW,SAA8B,GAArD1B,KAAAA,CAAqD,GAArDA,KAAuB,CAAEqC,4BAA4B;KACtF,CAAC;CACH"}