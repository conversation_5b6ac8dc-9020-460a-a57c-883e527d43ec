{"version": 3, "sources": ["../../../../src/start/server/DevServerManager.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\nimport assert from 'assert';\nimport chalk from 'chalk';\n\nimport { BundlerDevServer, BundlerStartOptions } from './BundlerDevServer';\nimport DevToolsPluginManager from './DevToolsPluginManager';\nimport { getPlatformBundlers } from './platformBundlers';\nimport { Log } from '../../log';\nimport { FileNotifier } from '../../utils/FileNotifier';\nimport { logEventAsync } from '../../utils/analytics/rudderstackClient';\nimport { env } from '../../utils/env';\nimport { ProjectPrerequisite } from '../doctor/Prerequisite';\nimport { TypeScriptProjectPrerequisite } from '../doctor/typescript/TypeScriptProjectPrerequisite';\nimport { printItem } from '../interface/commandsTable';\nimport * as AndroidDebugBridge from '../platforms/android/adb';\nimport { resolveSchemeAsync } from '../resolveOptions';\n\nconst debug = require('debug')('expo:start:server:devServerManager') as typeof console.log;\n\nexport type MultiBundlerStartOptions = {\n  type: keyof typeof BUNDLERS;\n  options?: BundlerStartOptions;\n}[];\n\nconst devServers: BundlerDevServer[] = [];\n\nconst BUNDLERS = {\n  webpack: () =>\n    require('./webpack/WebpackBundlerDevServer')\n      .WebpackBundlerDevServer as typeof import('./webpack/WebpackBundlerDevServer').WebpackBundlerDevServer,\n  metro: () =>\n    require('./metro/MetroBundlerDevServer')\n      .MetroBundlerDevServer as typeof import('./metro/MetroBundlerDevServer').MetroBundlerDevServer,\n};\n\n/** Manages interacting with multiple dev servers. */\nexport class DevServerManager {\n  private projectPrerequisites: ProjectPrerequisite<any, void>[] = [];\n  public readonly devtoolsPluginManager: DevToolsPluginManager;\n\n  private notifier: FileNotifier | null = null;\n\n  constructor(\n    public projectRoot: string,\n    /** Keep track of the original CLI options for bundlers that are started interactively. */\n    public options: BundlerStartOptions\n  ) {\n    this.notifier = this.watchBabelConfig();\n    this.devtoolsPluginManager = new DevToolsPluginManager(projectRoot);\n  }\n\n  private watchBabelConfig() {\n    const notifier = new FileNotifier(\n      this.projectRoot,\n      [\n        './babel.config.js',\n        './babel.config.json',\n        './.babelrc.json',\n        './.babelrc',\n        './.babelrc.js',\n      ],\n      {\n        additionalWarning: chalk` You may need to clear the bundler cache with the {bold --clear} flag for your changes to take effect.`,\n      }\n    );\n\n    notifier.startObserving();\n\n    return notifier;\n  }\n\n  /** Lazily load and assert a project-level prerequisite. */\n  async ensureProjectPrerequisiteAsync(PrerequisiteClass: typeof ProjectPrerequisite<any, any>) {\n    let prerequisite = this.projectPrerequisites.find(\n      (prerequisite) => prerequisite instanceof PrerequisiteClass\n    );\n    if (!prerequisite) {\n      prerequisite = new PrerequisiteClass(this.projectRoot);\n      this.projectPrerequisites.push(prerequisite);\n    }\n    return await prerequisite.assertAsync();\n  }\n\n  /**\n   * Sends a message over web sockets to all connected devices,\n   * does nothing when the dev server is not running.\n   *\n   * @param method name of the command. In RN projects `reload`, and `devMenu` are available. In Expo Go, `sendDevCommand` is available.\n   * @param params extra event info to send over the socket.\n   */\n  broadcastMessage(method: 'reload' | 'devMenu' | 'sendDevCommand', params?: Record<string, any>) {\n    devServers.forEach((server) => {\n      server.broadcastMessage(method, params);\n    });\n  }\n\n  /** Get the port for the dev server (either Webpack or Metro) that is hosting code for React Native runtimes. */\n  getNativeDevServerPort() {\n    const server = devServers.find((server) => server.isTargetingNative());\n    return server?.getInstance()?.location.port ?? null;\n  }\n\n  /** Get the first server that targets web. */\n  getWebDevServer() {\n    const server = devServers.find((server) => server.isTargetingWeb());\n    return server ?? null;\n  }\n\n  getDefaultDevServer(): BundlerDevServer {\n    // Return the first native dev server otherwise return the first dev server.\n    const server = devServers.find((server) => server.isTargetingNative());\n    const defaultServer = server ?? devServers[0];\n    assert(defaultServer, 'No dev servers are running');\n    return defaultServer;\n  }\n\n  async ensureWebDevServerRunningAsync() {\n    const [server] = devServers.filter((server) => server.isTargetingWeb());\n    if (server) {\n      return;\n    }\n    const { exp } = getConfig(this.projectRoot, {\n      skipPlugins: true,\n      skipSDKVersionRequirement: true,\n    });\n    const bundler = getPlatformBundlers(this.projectRoot, exp).web;\n    debug(`Starting ${bundler} dev server for web`);\n    return this.startAsync([\n      {\n        type: bundler,\n        options: this.options,\n      },\n    ]);\n  }\n\n  /** Switch between Expo Go and Expo Dev Clients. */\n  async toggleRuntimeMode(isUsingDevClient: boolean = !this.options.devClient): Promise<boolean> {\n    const nextMode = isUsingDevClient ? '--dev-client' : '--go';\n    Log.log(printItem(chalk`Switching to {bold ${nextMode}}`));\n\n    const nextScheme = await resolveSchemeAsync(this.projectRoot, {\n      devClient: isUsingDevClient,\n      // NOTE: The custom `--scheme` argument is lost from this point on.\n    });\n\n    this.options.location.scheme = nextScheme;\n    this.options.devClient = isUsingDevClient;\n    for (const devServer of devServers) {\n      devServer.isDevClient = isUsingDevClient;\n      const urlCreator = devServer.getUrlCreator();\n      urlCreator.defaults ??= {};\n      urlCreator.defaults.scheme = nextScheme;\n    }\n\n    debug(`New runtime options (runtime: ${nextMode}):`, this.options);\n    return true;\n  }\n\n  /** Start all dev servers. */\n  async startAsync(startOptions: MultiBundlerStartOptions): Promise<ExpoConfig> {\n    const { exp } = getConfig(this.projectRoot, { skipSDKVersionRequirement: true });\n\n    await logEventAsync('Start Project', {\n      sdkVersion: exp.sdkVersion ?? null,\n    });\n\n    const platformBundlers = getPlatformBundlers(this.projectRoot, exp);\n\n    // Start all dev servers...\n    for (const { type, options } of startOptions) {\n      const BundlerDevServerClass = await BUNDLERS[type]();\n      const server = new BundlerDevServerClass(this.projectRoot, platformBundlers, {\n        devToolsPluginManager: this.devtoolsPluginManager,\n        isDevClient: !!options?.devClient,\n      });\n      await server.startAsync(options ?? this.options);\n      devServers.push(server);\n    }\n\n    return exp;\n  }\n\n  async bootstrapTypeScriptAsync() {\n    const typescriptPrerequisite = await this.ensureProjectPrerequisiteAsync(\n      TypeScriptProjectPrerequisite\n    );\n\n    if (env.EXPO_NO_TYPESCRIPT_SETUP) {\n      return;\n    }\n\n    // Optionally, wait for the user to add TypeScript during the\n    // development cycle.\n    const server = devServers.find((server) => server.name === 'metro');\n    if (!server) {\n      return;\n    }\n\n    // The dev server shouldn't wait for the typescript services\n    if (!typescriptPrerequisite) {\n      server.waitForTypeScriptAsync().then(async (success) => {\n        if (success) {\n          server.startTypeScriptServices();\n        }\n      });\n    } else {\n      server.startTypeScriptServices();\n    }\n  }\n\n  async watchEnvironmentVariables() {\n    await devServers.find((server) => server.name === 'metro')?.watchEnvironmentVariables();\n  }\n\n  /** Stop all servers including ADB. */\n  async stopAsync(): Promise<void> {\n    await Promise.allSettled([\n      this.notifier?.stopObserving(),\n      // Stop all dev servers\n      ...devServers.map((server) => server.stopAsync()),\n      // Stop ADB\n      AndroidDebugBridge.getServer().stopAsync(),\n    ]);\n  }\n}\n"], "names": ["AndroidDebugBridge", "urlCreator", "debug", "require", "devServers", "BUNDLERS", "webpack", "WebpackBundlerDevServer", "metro", "MetroBundlerDevServer", "DevServerManager", "constructor", "projectRoot", "options", "projectPrerequisites", "notifier", "watchBabelConfig", "devtoolsPluginManager", "DevToolsPluginManager", "FileNotifier", "additionalWarning", "chalk", "startObserving", "ensureProjectPrerequisiteAsync", "PrerequisiteClass", "prerequisite", "find", "push", "assertAsync", "broadcastMessage", "method", "params", "for<PERSON>ach", "server", "getNativeDevServerPort", "isTargetingNative", "getInstance", "location", "port", "getWebDevServer", "isTargetingWeb", "getDefaultDevServer", "defaultServer", "assert", "ensureWebDevServerRunningAsync", "filter", "exp", "getConfig", "skip<PERSON>lug<PERSON>", "skipSDKVersionRequirement", "bundler", "getPlatformBundlers", "web", "startAsync", "type", "toggleRuntimeMode", "isUsingDevClient", "devClient", "nextMode", "Log", "log", "printItem", "nextScheme", "resolveSchemeAsync", "scheme", "devServer", "isDevClient", "getUrlCreator", "defaults", "startOptions", "logEventAsync", "sdkVersion", "platformBundlers", "BundlerDevServerClass", "devToolsPluginManager", "bootstrapTypeScriptAsync", "typescriptPrerequisite", "TypeScriptProjectPrerequisite", "env", "EXPO_NO_TYPESCRIPT_SETUP", "name", "waitForTypeScriptAsync", "then", "success", "startTypeScriptServices", "watchEnvironmentVariables", "stopAsync", "Promise", "allSettled", "stopObserving", "map", "getServer"], "mappings": "AAAA;;;;AAAsC,IAAA,OAAc,WAAd,cAAc,CAAA;AACjC,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACT,IAAA,MAAO,kCAAP,OAAO,EAAA;AAGS,IAAA,sBAAyB,kCAAzB,yBAAyB,EAAA;AACvB,IAAA,iBAAoB,WAApB,oBAAoB,CAAA;AACpC,IAAA,IAAW,WAAX,WAAW,CAAA;AACF,IAAA,aAA0B,WAA1B,0BAA0B,CAAA;AACzB,IAAA,kBAAyC,WAAzC,yCAAyC,CAAA;AACnD,IAAA,IAAiB,WAAjB,iBAAiB,CAAA;AAES,IAAA,8BAAoD,WAApD,oDAAoD,CAAA;AACxE,IAAA,cAA4B,WAA5B,4BAA4B,CAAA;AAC1CA,IAAAA,kBAAkB,mCAAM,0BAA0B,EAAhC;AACK,IAAA,eAAmB,WAAnB,mBAAmB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuIhDC,WAAU;AArIhB,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,oCAAoC,CAAC,AAAsB,AAAC;AAO3F,MAAMC,UAAU,GAAuB,EAAE,AAAC;AAE1C,MAAMC,QAAQ,GAAG;IACfC,OAAO,EAAE,IACPH,OAAO,CAAC,mCAAmC,CAAC,CACzCI,uBAAuB;IAA8E;IAC1GC,KAAK,EAAE,IACLL,OAAO,CAAC,+BAA+B,CAAC,CACrCM,qBAAqB;CAC3B,AAAC;AAGK,MAAMC,gBAAgB;IAM3BC,YACSC,WAAmB,EAEnBC,OAA4B,CACnC;aAHOD,WAAmB,GAAnBA,WAAmB;aAEnBC,OAA4B,GAA5BA,OAA4B;aAR7BC,oBAAoB,GAAqC,EAAE;aAG3DC,QAAQ,GAAwB,IAAI;QAO1C,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACC,gBAAgB,EAAE,CAAC;QACxC,IAAI,CAACC,qBAAqB,GAAG,IAAIC,sBAAqB,QAAA,CAACN,WAAW,CAAC,CAAC;KACrE;IAED,AAAQI,gBAAgB,GAAG;QACzB,MAAMD,QAAQ,GAAG,IAAII,aAAY,aAAA,CAC/B,IAAI,CAACP,WAAW,EAChB;YACE,mBAAmB;YACnB,qBAAqB;YACrB,iBAAiB;YACjB,YAAY;YACZ,eAAe;SAChB,EACD;YACEQ,iBAAiB,EAAEC,MAAK,QAAA,CAAC,sGAAsG,CAAC;SACjI,CACF,AAAC;QAEFN,QAAQ,CAACO,cAAc,EAAE,CAAC;QAE1B,OAAOP,QAAQ,CAAC;KACjB;IAED,2DAA2D,CAC3D,MAAMQ,8BAA8B,CAACC,iBAAuD,EAAE;QAC5F,IAAIC,aAAY,GAAG,IAAI,CAACX,oBAAoB,CAACY,IAAI,CAC/C,CAACD,YAAY,GAAKA,YAAY,YAAYD,iBAAiB;QAAA,CAC5D,AAAC;QACF,IAAI,CAACC,aAAY,EAAE;YACjBA,aAAY,GAAG,IAAID,iBAAiB,CAAC,IAAI,CAACZ,WAAW,CAAC,CAAC;YACvD,IAAI,CAACE,oBAAoB,CAACa,IAAI,CAACF,aAAY,CAAC,CAAC;SAC9C;QACD,OAAO,MAAMA,aAAY,CAACG,WAAW,EAAE,CAAC;KACzC;IAED;;;;;;KAMG,CACHC,gBAAgB,CAACC,MAA+C,EAAEC,MAA4B,EAAE;QAC9F3B,UAAU,CAAC4B,OAAO,CAAC,CAACC,MAAM,GAAK;YAC7BA,MAAM,CAACJ,gBAAgB,CAACC,MAAM,EAAEC,MAAM,CAAC,CAAC;SACzC,CAAC,CAAC;KACJ;IAED,gHAAgH,CAChHG,sBAAsB,GAAG;;QACvB,MAAMD,OAAM,GAAG7B,UAAU,CAACsB,IAAI,CAAC,CAACO,MAAM,GAAKA,MAAM,CAACE,iBAAiB,EAAE;QAAA,CAAC,AAAC;YAChEF,KAAoC;QAA3C,OAAOA,CAAAA,KAAoC,GAApCA,OAAAA,OAAM,QAAa,GAAnBA,KAAAA,CAAmB,GAAnBA,OAAM,CAAEG,WAAW,EAAE,SAAU,GAA/BH,KAAAA,CAA+B,GAA/BA,IAAuBI,QAAQ,CAACC,IAAI,YAApCL,KAAoC,GAAI,IAAI,CAAC;KACrD;IAED,6CAA6C,CAC7CM,eAAe,GAAG;QAChB,MAAMN,OAAM,GAAG7B,UAAU,CAACsB,IAAI,CAAC,CAACO,MAAM,GAAKA,MAAM,CAACO,cAAc,EAAE;QAAA,CAAC,AAAC;QACpE,OAAOP,OAAM,WAANA,OAAM,GAAI,IAAI,CAAC;KACvB;IAEDQ,mBAAmB,GAAqB;QACtC,4EAA4E;QAC5E,MAAMR,OAAM,GAAG7B,UAAU,CAACsB,IAAI,CAAC,CAACO,MAAM,GAAKA,MAAM,CAACE,iBAAiB,EAAE;QAAA,CAAC,AAAC;QACvE,MAAMO,aAAa,GAAGT,OAAM,WAANA,OAAM,GAAI7B,UAAU,CAAC,CAAC,CAAC,AAAC;QAC9CuC,CAAAA,GAAAA,OAAM,AAA6C,CAAA,QAA7C,CAACD,aAAa,EAAE,4BAA4B,CAAC,CAAC;QACpD,OAAOA,aAAa,CAAC;KACtB;IAED,MAAME,8BAA8B,GAAG;QACrC,MAAM,CAACX,OAAM,CAAC,GAAG7B,UAAU,CAACyC,MAAM,CAAC,CAACZ,MAAM,GAAKA,MAAM,CAACO,cAAc,EAAE;QAAA,CAAC,AAAC;QACxE,IAAIP,OAAM,EAAE;YACV,OAAO;SACR;QACD,MAAM,EAAEa,GAAG,CAAA,EAAE,GAAGC,CAAAA,GAAAA,OAAS,AAGvB,CAAA,UAHuB,CAAC,IAAI,CAACnC,WAAW,EAAE;YAC1CoC,WAAW,EAAE,IAAI;YACjBC,yBAAyB,EAAE,IAAI;SAChC,CAAC,AAAC;QACH,MAAMC,OAAO,GAAGC,CAAAA,GAAAA,iBAAmB,AAAuB,CAAA,oBAAvB,CAAC,IAAI,CAACvC,WAAW,EAAEkC,GAAG,CAAC,CAACM,GAAG,AAAC;QAC/DlD,KAAK,CAAC,CAAC,SAAS,EAAEgD,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAChD,OAAO,IAAI,CAACG,UAAU,CAAC;YACrB;gBACEC,IAAI,EAAEJ,OAAO;gBACbrC,OAAO,EAAE,IAAI,CAACA,OAAO;aACtB;SACF,CAAC,CAAC;KACJ;IAED,mDAAmD,CACnD,MAAM0C,iBAAiB,CAACC,gBAAyB,GAAG,CAAC,IAAI,CAAC3C,OAAO,CAAC4C,SAAS,EAAoB;QAC7F,MAAMC,QAAQ,GAAGF,gBAAgB,GAAG,cAAc,GAAG,MAAM,AAAC;QAC5DG,IAAG,IAAA,CAACC,GAAG,CAACC,CAAAA,GAAAA,cAAS,AAAwC,CAAA,UAAxC,CAACxC,MAAK,QAAA,CAAC,mBAAmB,EAAEqC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3D,MAAMI,UAAU,GAAG,MAAMC,CAAAA,GAAAA,eAAkB,AAGzC,CAAA,mBAHyC,CAAC,IAAI,CAACnD,WAAW,EAAE;YAC5D6C,SAAS,EAAED,gBAAgB;SAE5B,CAAC,AAAC;QAEH,IAAI,CAAC3C,OAAO,CAACwB,QAAQ,CAAC2B,MAAM,GAAGF,UAAU,CAAC;QAC1C,IAAI,CAACjD,OAAO,CAAC4C,SAAS,GAAGD,gBAAgB,CAAC;QAC1C,KAAK,MAAMS,SAAS,IAAI7D,UAAU,CAAE;YAClC6D,SAAS,CAACC,WAAW,GAAGV,gBAAgB,CAAC;YACzC,MAAMvD,UAAU,GAAGgE,SAAS,CAACE,aAAa,EAAE,AAAC;;YAC7ClE,cAAAA,WAAU,GAAVA,UAAU,EAACmE,QAAQ,wBAAnBnE,WAAU,CAACmE,QAAQ,GAAK,EAAE,CAAC;YAC3BnE,UAAU,CAACmE,QAAQ,CAACJ,MAAM,GAAGF,UAAU,CAAC;SACzC;QAED5D,KAAK,CAAC,CAAC,8BAA8B,EAAEwD,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC7C,OAAO,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC;KACb;IAED,6BAA6B,CAC7B,MAAMwC,UAAU,CAACgB,YAAsC,EAAuB;QAC5E,MAAM,EAAEvB,GAAG,CAAA,EAAE,GAAGC,CAAAA,GAAAA,OAAS,AAAuD,CAAA,UAAvD,CAAC,IAAI,CAACnC,WAAW,EAAE;YAAEqC,yBAAyB,EAAE,IAAI;SAAE,CAAC,AAAC;YAGnEH,WAAc;QAD5B,MAAMwB,CAAAA,GAAAA,kBAAa,AAEjB,CAAA,cAFiB,CAAC,eAAe,EAAE;YACnCC,UAAU,EAAEzB,CAAAA,WAAc,GAAdA,GAAG,CAACyB,UAAU,YAAdzB,WAAc,GAAI,IAAI;SACnC,CAAC,CAAC;QAEH,MAAM0B,gBAAgB,GAAGrB,CAAAA,GAAAA,iBAAmB,AAAuB,CAAA,oBAAvB,CAAC,IAAI,CAACvC,WAAW,EAAEkC,GAAG,CAAC,AAAC;QAEpE,2BAA2B;QAC3B,KAAK,MAAM,EAAEQ,IAAI,CAAA,EAAEzC,OAAO,CAAA,EAAE,IAAIwD,YAAY,CAAE;YAC5C,MAAMI,qBAAqB,GAAG,MAAMpE,QAAQ,CAACiD,IAAI,CAAC,EAAE,AAAC;YACrD,MAAMrB,MAAM,GAAG,IAAIwC,qBAAqB,CAAC,IAAI,CAAC7D,WAAW,EAAE4D,gBAAgB,EAAE;gBAC3EE,qBAAqB,EAAE,IAAI,CAACzD,qBAAqB;gBACjDiD,WAAW,EAAE,CAAC,CAACrD,CAAAA,OAAO,QAAW,GAAlBA,KAAAA,CAAkB,GAAlBA,OAAO,CAAE4C,SAAS,CAAA;aAClC,CAAC,AAAC;YACH,MAAMxB,MAAM,CAACoB,UAAU,CAACxC,OAAO,WAAPA,OAAO,GAAI,IAAI,CAACA,OAAO,CAAC,CAAC;YACjDT,UAAU,CAACuB,IAAI,CAACM,MAAM,CAAC,CAAC;SACzB;QAED,OAAOa,GAAG,CAAC;KACZ;IAED,MAAM6B,wBAAwB,GAAG;QAC/B,MAAMC,sBAAsB,GAAG,MAAM,IAAI,CAACrD,8BAA8B,CACtEsD,8BAA6B,8BAAA,CAC9B,AAAC;QAEF,IAAIC,IAAG,IAAA,CAACC,wBAAwB,EAAE;YAChC,OAAO;SACR;QAED,6DAA6D;QAC7D,qBAAqB;QACrB,MAAM9C,OAAM,GAAG7B,UAAU,CAACsB,IAAI,CAAC,CAACO,MAAM,GAAKA,MAAM,CAAC+C,IAAI,KAAK,OAAO;QAAA,CAAC,AAAC;QACpE,IAAI,CAAC/C,OAAM,EAAE;YACX,OAAO;SACR;QAED,4DAA4D;QAC5D,IAAI,CAAC2C,sBAAsB,EAAE;YAC3B3C,OAAM,CAACgD,sBAAsB,EAAE,CAACC,IAAI,CAAC,OAAOC,OAAO,GAAK;gBACtD,IAAIA,OAAO,EAAE;oBACXlD,OAAM,CAACmD,uBAAuB,EAAE,CAAC;iBAClC;aACF,CAAC,CAAC;SACJ,MAAM;YACLnD,OAAM,CAACmD,uBAAuB,EAAE,CAAC;SAClC;KACF;IAED,MAAMC,yBAAyB,GAAG;YAC1BjF,GAAoD;QAA1D,OAAMA,CAAAA,GAAoD,GAApDA,UAAU,CAACsB,IAAI,CAAC,CAACO,MAAM,GAAKA,MAAM,CAAC+C,IAAI,KAAK,OAAO;QAAA,CAAC,SAA2B,GAA/E5E,KAAAA,CAA+E,GAA/EA,GAAoD,CAAEiF,yBAAyB,EAAE,CAAA,CAAC;KACzF;IAED,sCAAsC,CACtC,MAAMC,SAAS,GAAkB;YAE7B,GAAa;QADf,MAAMC,OAAO,CAACC,UAAU,CAAC;YACvB,CAAA,GAAa,GAAb,IAAI,CAACzE,QAAQ,SAAe,GAA5B,KAAA,CAA4B,GAA5B,GAAa,CAAE0E,aAAa,EAAE;YAC9B,uBAAuB;eACpBrF,UAAU,CAACsF,GAAG,CAAC,CAACzD,MAAM,GAAKA,MAAM,CAACqD,SAAS,EAAE;YAAA,CAAC;YACjD,WAAW;YACXtF,kBAAkB,CAAC2F,SAAS,EAAE,CAACL,SAAS,EAAE;SAC3C,CAAC,CAAC;KACJ;CACF;QA5LY5E,gBAAgB,GAAhBA,gBAAgB"}