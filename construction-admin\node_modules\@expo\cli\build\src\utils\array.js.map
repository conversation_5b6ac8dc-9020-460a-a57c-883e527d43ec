{"version": 3, "sources": ["../../../src/utils/array.ts"], "sourcesContent": ["/** Returns the last index of an item based on a given criteria. */\nexport function findLastIndex<T>(array: T[], predicate: (item: T) => boolean) {\n  for (let i = array.length - 1; i >= 0; i--) {\n    if (predicate(array[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\n\n/** Returns a list of items that intersect between two given arrays. */\nexport function intersecting<T>(a: T[], b: T[]): T[] {\n  const [c, d] = a.length > b.length ? [a, b] : [b, a];\n  return c.filter((value) => d.includes(value));\n}\n\nexport function replaceValue<T>(values: T[], original: T, replacement: T): T[] {\n  const index = values.indexOf(original);\n  if (index > -1) {\n    values[index] = replacement;\n  }\n  return values;\n}\n\n/** lodash.uniqBy */\nexport function uniqBy<T>(array: T[], key: (item: T) => string): T[] {\n  const seen: { [key: string]: boolean } = {};\n  return array.filter((item) => {\n    const k = key(item);\n    if (seen[k]) {\n      return false;\n    }\n    seen[k] = true;\n    return true;\n  });\n}\n\n/** `lodash.chunk` */\nexport function chunk<T>(array: T[], size: number): T[][] {\n  const chunked = [];\n  let index = 0;\n  while (index < array.length) {\n    chunked.push(array.slice(index, (index += size)));\n  }\n  return chunked;\n}\n\n/** `lodash.groupBy` */\nexport function groupBy<T, K extends keyof any>(list: T[], getKey: (item: T) => K): Record<K, T[]> {\n  return list.reduce(\n    (previous, currentItem) => {\n      const group = getKey(currentItem);\n      if (!previous[group]) {\n        previous[group] = [];\n      }\n      previous[group].push(currentItem);\n      return previous;\n    },\n    {} as Record<K, T[]>\n  );\n}\n"], "names": ["findLastIndex", "intersecting", "replaceValue", "uniqBy", "chunk", "groupBy", "array", "predicate", "i", "length", "a", "b", "c", "d", "filter", "value", "includes", "values", "original", "replacement", "index", "indexOf", "key", "seen", "item", "k", "size", "chunked", "push", "slice", "list", "<PERSON><PERSON><PERSON>", "reduce", "previous", "currentItem", "group"], "mappings": "AACA;;;;QAAgBA,aAAa,GAAbA,aAAa;QAUbC,YAAY,GAAZA,YAAY;QAKZC,YAAY,GAAZA,YAAY;QASZC,MAAM,GAANA,MAAM;QAaNC,KAAK,GAALA,KAAK;QAULC,OAAO,GAAPA,OAAO;AA/ChB,SAASL,aAAa,CAAIM,KAAU,EAAEC,SAA+B,EAAE;IAC5E,IAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAE;QAC1C,IAAID,SAAS,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC,EAAE;YACvB,OAAOA,CAAC,CAAC;SACV;KACF;IACD,OAAO,CAAC,CAAC,CAAC;CACX;AAGM,SAASP,YAAY,CAAIS,CAAM,EAAEC,CAAM,EAAO;IACnD,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGH,CAAC,CAACD,MAAM,GAAGE,CAAC,CAACF,MAAM,GAAG;QAACC,CAAC;QAAEC,CAAC;KAAC,GAAG;QAACA,CAAC;QAAED,CAAC;KAAC,AAAC;IACrD,OAAOE,CAAC,CAACE,MAAM,CAAC,CAACC,KAAK,GAAKF,CAAC,CAACG,QAAQ,CAACD,KAAK,CAAC;IAAA,CAAC,CAAC;CAC/C;AAEM,SAASb,YAAY,CAAIe,MAAW,EAAEC,QAAW,EAAEC,WAAc,EAAO;IAC7E,MAAMC,KAAK,GAAGH,MAAM,CAACI,OAAO,CAACH,QAAQ,CAAC,AAAC;IACvC,IAAIE,KAAK,GAAG,CAAC,CAAC,EAAE;QACdH,MAAM,CAACG,KAAK,CAAC,GAAGD,WAAW,CAAC;KAC7B;IACD,OAAOF,MAAM,CAAC;CACf;AAGM,SAASd,MAAM,CAAIG,KAAU,EAAEgB,GAAwB,EAAO;IACnE,MAAMC,IAAI,GAA+B,EAAE,AAAC;IAC5C,OAAOjB,KAAK,CAACQ,MAAM,CAAC,CAACU,IAAI,GAAK;QAC5B,MAAMC,CAAC,GAAGH,GAAG,CAACE,IAAI,CAAC,AAAC;QACpB,IAAID,IAAI,CAACE,CAAC,CAAC,EAAE;YACX,OAAO,KAAK,CAAC;SACd;QACDF,IAAI,CAACE,CAAC,CAAC,GAAG,IAAI,CAAC;QACf,OAAO,IAAI,CAAC;KACb,CAAC,CAAC;CACJ;AAGM,SAASrB,KAAK,CAAIE,KAAU,EAAEoB,IAAY,EAAS;IACxD,MAAMC,OAAO,GAAG,EAAE,AAAC;IACnB,IAAIP,KAAK,GAAG,CAAC,AAAC;IACd,MAAOA,KAAK,GAAGd,KAAK,CAACG,MAAM,CAAE;QAC3BkB,OAAO,CAACC,IAAI,CAACtB,KAAK,CAACuB,KAAK,CAACT,KAAK,EAAGA,KAAK,IAAIM,IAAI,CAAE,CAAC,CAAC;KACnD;IACD,OAAOC,OAAO,CAAC;CAChB;AAGM,SAAStB,OAAO,CAAyByB,IAAS,EAAEC,MAAsB,EAAkB;IACjG,OAAOD,IAAI,CAACE,MAAM,CAChB,CAACC,QAAQ,EAAEC,WAAW,GAAK;QACzB,MAAMC,KAAK,GAAGJ,MAAM,CAACG,WAAW,CAAC,AAAC;QAClC,IAAI,CAACD,QAAQ,CAACE,KAAK,CAAC,EAAE;YACpBF,QAAQ,CAACE,KAAK,CAAC,GAAG,EAAE,CAAC;SACtB;QACDF,QAAQ,CAACE,KAAK,CAAC,CAACP,IAAI,CAACM,WAAW,CAAC,CAAC;QAClC,OAAOD,QAAQ,CAAC;KACjB,EACD,EAAE,CACH,CAAC;CACH"}