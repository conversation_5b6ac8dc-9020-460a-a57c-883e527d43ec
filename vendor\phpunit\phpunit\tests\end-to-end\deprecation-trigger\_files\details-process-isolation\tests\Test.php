<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture\DeprecationTrigger;

use const E_USER_DEPRECATED;
use function trigger_error;
use PHPUnit\Framework\TestCase;

final class Test extends TestCase
{
    public static function triggerDeprecation(): void
    {
        trigger_error('deprecation triggered by method', E_USER_DEPRECATED);
    }

    public function testDeprecation(): void
    {
        self::triggerDeprecation();
        triggerDeprecation();

        $this->assertTrue(true);
    }
}

function triggerDeprecation(): void
{
    trigger_error('deprecation triggered by function', E_USER_DEPRECATED);
}
