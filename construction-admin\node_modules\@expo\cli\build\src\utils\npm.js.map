{"version": 3, "sources": ["../../../src/utils/npm.ts"], "sourcesContent": ["import { J<PERSON>NValue } from '@expo/json-file';\nimport spawnAsync from '@expo/spawn-async';\nimport assert from 'assert';\nimport crypto from 'crypto';\nimport fs from 'fs';\nimport slugify from 'slugify';\nimport { PassThrough, Stream } from 'stream';\nimport tar from 'tar';\nimport { promisify } from 'util';\n\nimport { createEntryResolver, createFileTransform } from './createFileTransform';\nimport { ensureDirectoryAsync } from './dir';\nimport { CommandError } from './errors';\nimport { createCachedFetch } from '../api/rest/client';\n\nconst debug = require('debug')('expo:utils:npm') as typeof console.log;\n\nconst cachedFetch = createCachedFetch({\n  cacheDirectory: 'template-cache',\n  // Time to live. How long (in ms) responses remain cached before being automatically ejected. If undefined, responses are never automatically ejected from the cache.\n  // ttl: 1000,\n});\n\nexport function sanitizeNpmPackageName(name: string): string {\n  // https://github.com/npm/validate-npm-package-name/#naming-rules\n  return (\n    applyKnownNpmPackageNameRules(name) ||\n    applyKnownNpmPackageNameRules(slugify(name)) ||\n    // If nothing is left use 'app' like we do in Xcode projects.\n    'app'\n  );\n}\n\nfunction applyKnownNpmPackageNameRules(name: string): string | null {\n  // https://github.com/npm/validate-npm-package-name/#naming-rules\n\n  // package name cannot start with '.' or '_'.\n  while (/^(\\.|_)/.test(name)) {\n    name = name.substring(1);\n  }\n\n  name = name.toLowerCase().replace(/[^a-zA-Z._\\-/@]/g, '');\n\n  return (\n    name\n      // .replace(/![a-z0-9-._~]+/g, '')\n      // Remove special characters\n      .normalize('NFD')\n      .replace(/[\\u0300-\\u036f]/g, '') || null\n  );\n}\n\nexport async function npmViewAsync(...props: string[]): Promise<JSONValue> {\n  const cmd = ['view', ...props, '--json'];\n  const results = (await spawnAsync('npm', cmd)).stdout?.trim();\n  const cmdString = `npm ${cmd.join(' ')}`;\n  debug('Run:', cmdString);\n  if (!results) {\n    return null;\n  }\n  try {\n    return JSON.parse(results);\n  } catch (error: any) {\n    throw new Error(\n      `Could not parse JSON returned from \"${cmdString}\".\\n\\n${results}\\n\\nError: ${error.message}`\n    );\n  }\n}\n\n/** Given a package name like `expo` or `expo@beta`, return the registry URL if it exists. */\nexport async function getNpmUrlAsync(packageName: string): Promise<string> {\n  const results = await npmViewAsync(packageName, 'dist.tarball');\n\n  assert(results, `Could not get npm url for package \"${packageName}\"`);\n\n  // Fully qualified url returns a string.\n  // Example:\n  // 𝝠 npm view expo-template-bare-minimum@sdk-33 dist.tarball --json\n  if (typeof results === 'string') {\n    return results;\n  }\n\n  // When the tag is arbitrary, the tarball url is an array, return the last value as it's the most recent.\n  // Example:\n  // 𝝠 npm view expo-template-bare-minimum@33 dist.tarball --json\n  if (Array.isArray(results)) {\n    return results[results.length - 1] as string;\n  }\n\n  throw new CommandError(\n    'Expected results of `npm view ...` to be an array or string. Instead found: ' + results\n  );\n}\n\n// @ts-ignore\nconst pipeline = promisify(Stream.pipeline);\n\nexport async function downloadAndExtractNpmModuleAsync(\n  npmName: string,\n  props: ExtractProps\n): Promise<string> {\n  const url = await getNpmUrlAsync(npmName);\n\n  debug('Fetch from URL:', url);\n  return await extractNpmTarballFromUrlAsync(url, props);\n}\n\nexport async function extractLocalNpmTarballAsync(\n  tarFilePath: string,\n  props: ExtractProps\n): Promise<string> {\n  const readStream = fs.createReadStream(tarFilePath);\n  return await extractNpmTarballAsync(readStream, props);\n}\n\nexport type ExtractProps = {\n  name: string;\n  cwd: string;\n  strip?: number;\n  fileList?: string[];\n  /** The checksum algorithm to use when verifying the tarball. */\n  checksumAlgorithm?: string;\n  /** An optional filter to selectively extract specific paths */\n  filter?: tar.ExtractOptions['filter'];\n};\n\nasync function createUrlStreamAsync(url: string) {\n  const response = await cachedFetch(url);\n  if (!response.ok) {\n    throw new Error(`Unexpected response: ${response.statusText}. From url: ${url}`);\n  }\n\n  return response.body;\n}\n\nexport async function extractNpmTarballFromUrlAsync(\n  url: string,\n  props: ExtractProps\n): Promise<string> {\n  return await extractNpmTarballAsync(await createUrlStreamAsync(url), props);\n}\n\n/**\n * Extracts a tarball stream to a directory and returns the checksum of the tarball.\n */\nexport async function extractNpmTarballAsync(\n  stream: NodeJS.ReadableStream,\n  props: ExtractProps\n): Promise<string> {\n  const { cwd, strip, name, fileList = [], filter } = props;\n\n  await ensureDirectoryAsync(cwd);\n\n  const hash = crypto.createHash(props.checksumAlgorithm ?? 'md5');\n  const transformStream = new PassThrough();\n  transformStream.on('data', (chunk) => {\n    hash.update(chunk);\n  });\n\n  await pipeline(\n    stream,\n    transformStream,\n    tar.extract(\n      {\n        cwd,\n        filter,\n        transform: createFileTransform(name),\n        onentry: createEntryResolver(name),\n        strip: strip ?? 1,\n      },\n      fileList\n    )\n  );\n\n  return hash.digest('hex');\n}\n"], "names": ["sanitizeNpmPackageName", "npmViewAsync", "getNpmUrlAsync", "downloadAndExtractNpmModuleAsync", "extractLocalNpmTarballAsync", "extractNpmTarballFromUrlAsync", "extractNpmTarballAsync", "debug", "require", "cachedFetch", "createCachedFetch", "cacheDirectory", "name", "applyKnownNpmPackageNameRules", "slugify", "test", "substring", "toLowerCase", "replace", "normalize", "props", "cmd", "results", "spawnAsync", "stdout", "trim", "cmdString", "join", "JSON", "parse", "error", "Error", "message", "packageName", "assert", "Array", "isArray", "length", "CommandError", "pipeline", "promisify", "Stream", "npmName", "url", "tar<PERSON><PERSON><PERSON><PERSON>", "readStream", "fs", "createReadStream", "createUrlStreamAsync", "response", "ok", "statusText", "body", "stream", "cwd", "strip", "fileList", "filter", "ensureDirectoryAsync", "hash", "crypto", "createHash", "checksumAlgorithm", "transformStream", "PassThrough", "on", "chunk", "update", "tar", "extract", "transform", "createFileTransform", "onentry", "createEntryResolver", "digest"], "mappings": "AAAA;;;;QAuBgBA,sBAAsB,GAAtBA,sBAAsB;QA6BhBC,YAAY,GAAZA,YAAY;QAkBZC,cAAc,GAAdA,cAAc;QA2BdC,gCAAgC,GAAhCA,gCAAgC;QAUhCC,2BAA2B,GAA3BA,2BAA2B;QA4B3BC,6BAA6B,GAA7BA,6BAA6B;QAU7BC,sBAAsB,GAAtBA,sBAAsB;AAhJrB,IAAA,WAAmB,kCAAnB,mBAAmB,EAAA;AACvB,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACR,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACZ,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACC,IAAA,QAAS,kCAAT,SAAS,EAAA;AACO,IAAA,OAAQ,WAAR,QAAQ,CAAA;AAC5B,IAAA,IAAK,kCAAL,KAAK,EAAA;AACK,IAAA,KAAM,WAAN,MAAM,CAAA;AAEyB,IAAA,oBAAuB,WAAvB,uBAAuB,CAAA;AAC3C,IAAA,IAAO,WAAP,OAAO,CAAA;AACf,IAAA,OAAU,WAAV,UAAU,CAAA;AACL,IAAA,OAAoB,WAApB,oBAAoB,CAAA;;;;;;AAEtD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,AAAsB,AAAC;AAEvE,MAAMC,WAAW,GAAGC,CAAAA,GAAAA,OAAiB,AAInC,CAAA,kBAJmC,CAAC;IACpCC,cAAc,EAAE,gBAAgB;CAGjC,CAAC,AAAC;AAEI,SAASX,sBAAsB,CAACY,IAAY,EAAU;IAC3D,iEAAiE;IACjE,OACEC,6BAA6B,CAACD,IAAI,CAAC,IACnCC,6BAA6B,CAACC,CAAAA,GAAAA,QAAO,AAAM,CAAA,QAAN,CAACF,IAAI,CAAC,CAAC,IAC5C,6DAA6D;IAC7D,KAAK,CACL;CACH;AAED,SAASC,6BAA6B,CAACD,IAAY,EAAiB;IAClE,iEAAiE;IAEjE,6CAA6C;IAC7C,MAAO,UAAUG,IAAI,CAACH,IAAI,CAAC,CAAE;QAC3BA,IAAI,GAAGA,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,CAAC;KAC1B;IAEDJ,IAAI,GAAGA,IAAI,CAACK,WAAW,EAAE,CAACC,OAAO,qBAAqB,EAAE,CAAC,CAAC;IAE1D,OACEN,IAAI,AACF,kCAAkC;IAClC,4BAA4B;KAC3BO,SAAS,CAAC,KAAK,CAAC,CAChBD,OAAO,qBAAqB,EAAE,CAAC,IAAI,IAAI,CAC1C;CACH;AAEM,eAAejB,YAAY,CAAC,GAAGmB,KAAK,AAAU,EAAsB;QAEzD,GAAqC;IADrD,MAAMC,GAAG,GAAG;QAAC,MAAM;WAAKD,KAAK;QAAE,QAAQ;KAAC,AAAC;IACzC,MAAME,OAAO,GAAG,CAAA,GAAqC,GAArC,CAAC,MAAMC,CAAAA,GAAAA,WAAU,AAAY,CAAA,QAAZ,CAAC,KAAK,EAAEF,GAAG,CAAC,CAAC,CAACG,MAAM,SAAM,GAA3C,KAAA,CAA2C,GAA3C,GAAqC,CAAEC,IAAI,EAAE,AAAC;IAC9D,MAAMC,SAAS,GAAG,CAAC,IAAI,EAAEL,GAAG,CAACM,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,AAAC;IACzCpB,KAAK,CAAC,MAAM,EAAEmB,SAAS,CAAC,CAAC;IACzB,IAAI,CAACJ,OAAO,EAAE;QACZ,OAAO,IAAI,CAAC;KACb;IACD,IAAI;QACF,OAAOM,IAAI,CAACC,KAAK,CAACP,OAAO,CAAC,CAAC;KAC5B,CAAC,OAAOQ,KAAK,EAAO;QACnB,MAAM,IAAIC,KAAK,CACb,CAAC,oCAAoC,EAAEL,SAAS,CAAC,MAAM,EAAEJ,OAAO,CAAC,WAAW,EAAEQ,KAAK,CAACE,OAAO,CAAC,CAAC,CAC9F,CAAC;KACH;CACF;AAGM,eAAe9B,cAAc,CAAC+B,WAAmB,EAAmB;IACzE,MAAMX,OAAO,GAAG,MAAMrB,YAAY,CAACgC,WAAW,EAAE,cAAc,CAAC,AAAC;IAEhEC,CAAAA,GAAAA,OAAM,AAA+D,CAAA,QAA/D,CAACZ,OAAO,EAAE,CAAC,mCAAmC,EAAEW,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAEtE,wCAAwC;IACxC,WAAW;IACX,sEAAmE;IAChE,IAAC,OAAOX,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAOA,OAAO,CAAC;KAChB;IAED,yGAAyG;IACzG,WAAW;IACX,kEAA+D;IAC/D,IAAIa,KAAK,CAACC,OAAO,CAACd,OAAO,CAAC,EAAE;QAC1B,OAAOA,OAAO,CAACA,OAAO,CAACe,MAAM,GAAG,CAAC,CAAC,CAAW;KAC9C;IAED,MAAM,IAAIC,OAAY,aAAA,CACpB,8EAA8E,GAAGhB,OAAO,CACzF,CAAC;CACH;AAED,aAAa;AACb,MAAMiB,QAAQ,GAAGC,CAAAA,GAAAA,KAAS,AAAiB,CAAA,UAAjB,CAACC,OAAM,OAAA,CAACF,QAAQ,CAAC,AAAC;AAErC,eAAepC,gCAAgC,CACpDuC,OAAe,EACftB,KAAmB,EACF;IACjB,MAAMuB,GAAG,GAAG,MAAMzC,cAAc,CAACwC,OAAO,CAAC,AAAC;IAE1CnC,KAAK,CAAC,iBAAiB,EAAEoC,GAAG,CAAC,CAAC;IAC9B,OAAO,MAAMtC,6BAA6B,CAACsC,GAAG,EAAEvB,KAAK,CAAC,CAAC;CACxD;AAEM,eAAehB,2BAA2B,CAC/CwC,WAAmB,EACnBxB,KAAmB,EACF;IACjB,MAAMyB,UAAU,GAAGC,GAAE,QAAA,CAACC,gBAAgB,CAACH,WAAW,CAAC,AAAC;IACpD,OAAO,MAAMtC,sBAAsB,CAACuC,UAAU,EAAEzB,KAAK,CAAC,CAAC;CACxD;AAaD,eAAe4B,oBAAoB,CAACL,GAAW,EAAE;IAC/C,MAAMM,QAAQ,GAAG,MAAMxC,WAAW,CAACkC,GAAG,CAAC,AAAC;IACxC,IAAI,CAACM,QAAQ,CAACC,EAAE,EAAE;QAChB,MAAM,IAAInB,KAAK,CAAC,CAAC,qBAAqB,EAAEkB,QAAQ,CAACE,UAAU,CAAC,YAAY,EAAER,GAAG,CAAC,CAAC,CAAC,CAAC;KAClF;IAED,OAAOM,QAAQ,CAACG,IAAI,CAAC;CACtB;AAEM,eAAe/C,6BAA6B,CACjDsC,GAAW,EACXvB,KAAmB,EACF;IACjB,OAAO,MAAMd,sBAAsB,CAAC,MAAM0C,oBAAoB,CAACL,GAAG,CAAC,EAAEvB,KAAK,CAAC,CAAC;CAC7E;AAKM,eAAed,sBAAsB,CAC1C+C,MAA6B,EAC7BjC,KAAmB,EACF;IACjB,MAAM,EAAEkC,GAAG,CAAA,EAAEC,KAAK,CAAA,EAAE3C,IAAI,CAAA,EAAE4C,QAAQ,EAAG,EAAE,CAAA,EAAEC,MAAM,CAAA,EAAE,GAAGrC,KAAK,AAAC;IAE1D,MAAMsC,CAAAA,GAAAA,IAAoB,AAAK,CAAA,qBAAL,CAACJ,GAAG,CAAC,CAAC;QAEDlC,kBAAuB;IAAtD,MAAMuC,IAAI,GAAGC,OAAM,QAAA,CAACC,UAAU,CAACzC,CAAAA,kBAAuB,GAAvBA,KAAK,CAAC0C,iBAAiB,YAAvB1C,kBAAuB,GAAI,KAAK,CAAC,AAAC;IACjE,MAAM2C,eAAe,GAAG,IAAIC,OAAW,YAAA,EAAE,AAAC;IAC1CD,eAAe,CAACE,EAAE,CAAC,MAAM,EAAE,CAACC,KAAK,GAAK;QACpCP,IAAI,CAACQ,MAAM,CAACD,KAAK,CAAC,CAAC;KACpB,CAAC,CAAC;IAEH,MAAM3B,QAAQ,CACZc,MAAM,EACNU,eAAe,EACfK,IAAG,QAAA,CAACC,OAAO,CACT;QACEf,GAAG;QACHG,MAAM;QACNa,SAAS,EAAEC,CAAAA,GAAAA,oBAAmB,AAAM,CAAA,oBAAN,CAAC3D,IAAI,CAAC;QACpC4D,OAAO,EAAEC,CAAAA,GAAAA,oBAAmB,AAAM,CAAA,oBAAN,CAAC7D,IAAI,CAAC;QAClC2C,KAAK,EAAEA,KAAK,WAALA,KAAK,GAAI,CAAC;KAClB,EACDC,QAAQ,CACT,CACF,CAAC;IAEF,OAAOG,IAAI,CAACe,MAAM,CAAC,KAAK,CAAC,CAAC;CAC3B"}