{"version": 3, "sources": ["../../../src/utils/ora.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport oraReal, { Ora } from 'ora';\n\n// import * as Log from '../log';\nimport { env } from './env';\nimport { isInteractive } from './interactive';\n\nconst logReal = console.log;\nconst infoReal = console.info;\nconst warnReal = console.warn;\nconst errorReal = console.error;\n\nconst runningSpinners: oraReal.Ora[] = [];\n\nexport function getAllSpinners() {\n  return runningSpinners;\n}\n\n/**\n * A custom ora spinner that sends the stream to stdout in CI, non-TTY, or expo's non-interactive flag instead of stderr (the default).\n *\n * @param options\n * @returns\n */\nexport function ora(options?: oraReal.Options | string): oraReal.Ora {\n  const inputOptions = typeof options === 'string' ? { text: options } : options || {};\n  const disabled = !isInteractive() || env.EXPO_DEBUG;\n  const spinner = oraReal({\n    // Ensure our non-interactive mode emulates CI mode.\n    isEnabled: !disabled,\n    // In non-interactive mode, send the stream to stdout so it prevents looking like an error.\n    stream: disabled ? process.stdout : process.stderr,\n    ...inputOptions,\n  });\n\n  const oraStart = spinner.start.bind(spinner);\n  const oraStop = spinner.stop.bind(spinner);\n  const oraStopAndPersist = spinner.stopAndPersist.bind(spinner);\n\n  const logWrap = (method: any, args: any[]): void => {\n    oraStop();\n    method(...args);\n    spinner.start();\n  };\n\n  const wrapNativeLogs = (): void => {\n    console.log = (...args: any) => logWrap(logReal, args);\n    console.info = (...args: any) => logWrap(infoReal, args);\n    console.warn = (...args: any) => logWrap(warnReal, args);\n    console.error = (...args: any) => logWrap(errorReal, args);\n\n    runningSpinners.push(spinner);\n  };\n\n  const resetNativeLogs = (): void => {\n    console.log = logReal;\n    console.info = infoReal;\n    console.warn = warnReal;\n    console.error = errorReal;\n\n    const index = runningSpinners.indexOf(spinner);\n    if (index >= 0) {\n      runningSpinners.splice(index, 1);\n    }\n  };\n\n  spinner.start = (text): Ora => {\n    wrapNativeLogs();\n    return oraStart(text);\n  };\n\n  spinner.stopAndPersist = (options): Ora => {\n    const result = oraStopAndPersist(options);\n    resetNativeLogs();\n    return result;\n  };\n\n  spinner.stop = (): Ora => {\n    const result = oraStop();\n    resetNativeLogs();\n    return result;\n  };\n\n  // Always make the central logging module aware of the current spinner\n  // Log.setSpinner(spinner);\n\n  return spinner;\n}\n\n/**\n * Create a unified section spinner.\n *\n * @param title\n * @returns\n */\nexport function logNewSection(title: string) {\n  const spinner = ora(chalk.bold(title));\n  // Prevent the spinner from clashing with debug logs\n  spinner.start();\n  return spinner;\n}\n"], "names": ["getAllSpinners", "ora", "logNewSection", "logReal", "console", "log", "infoReal", "info", "warnReal", "warn", "errorReal", "error", "runningSpinners", "options", "inputOptions", "text", "disabled", "isInteractive", "env", "EXPO_DEBUG", "spinner", "oraReal", "isEnabled", "stream", "process", "stdout", "stderr", "oraStart", "start", "bind", "oraStop", "stop", "oraStopAndPersist", "stopAndPersist", "logWrap", "method", "args", "wrapNativeLogs", "push", "resetNativeLogs", "index", "indexOf", "splice", "result", "title", "chalk", "bold"], "mappings": "AAAA;;;;QAcgBA,cAAc,GAAdA,cAAc;QAUdC,GAAG,GAAHA,GAAG;QAuEHC,aAAa,GAAbA,aAAa;AA/FX,IAAA,MAAO,kCAAP,OAAO,EAAA;AACI,IAAA,IAAK,kCAAL,KAAK,EAAA;AAGd,IAAA,IAAO,WAAP,OAAO,CAAA;AACG,IAAA,YAAe,WAAf,eAAe,CAAA;;;;;;AAE7C,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,AAAC;AAC5B,MAAMC,QAAQ,GAAGF,OAAO,CAACG,IAAI,AAAC;AAC9B,MAAMC,QAAQ,GAAGJ,OAAO,CAACK,IAAI,AAAC;AAC9B,MAAMC,SAAS,GAAGN,OAAO,CAACO,KAAK,AAAC;AAEhC,MAAMC,eAAe,GAAkB,EAAE,AAAC;AAEnC,SAASZ,cAAc,GAAG;IAC/B,OAAOY,eAAe,CAAC;CACxB;AAQM,SAASX,GAAG,CAACY,QAAkC,EAAe;IACnE,MAAMC,YAAY,GAAG,OAAOD,QAAO,KAAK,QAAQ,GAAG;QAAEE,IAAI,EAAEF,QAAO;KAAE,GAAGA,QAAO,IAAI,EAAE,AAAC;IACrF,MAAMG,QAAQ,GAAG,CAACC,CAAAA,GAAAA,YAAa,AAAE,CAAA,cAAF,EAAE,IAAIC,IAAG,IAAA,CAACC,UAAU,AAAC;IACpD,MAAMC,OAAO,GAAGC,CAAAA,GAAAA,IAAO,AAMrB,CAAA,QANqB,CAAC;QACtB,oDAAoD;QACpDC,SAAS,EAAE,CAACN,QAAQ;QACpB,2FAA2F;QAC3FO,MAAM,EAAEP,QAAQ,GAAGQ,OAAO,CAACC,MAAM,GAAGD,OAAO,CAACE,MAAM;QAClD,GAAGZ,YAAY;KAChB,CAAC,AAAC;IAEH,MAAMa,QAAQ,GAAGP,OAAO,CAACQ,KAAK,CAACC,IAAI,CAACT,OAAO,CAAC,AAAC;IAC7C,MAAMU,OAAO,GAAGV,OAAO,CAACW,IAAI,CAACF,IAAI,CAACT,OAAO,CAAC,AAAC;IAC3C,MAAMY,iBAAiB,GAAGZ,OAAO,CAACa,cAAc,CAACJ,IAAI,CAACT,OAAO,CAAC,AAAC;IAE/D,MAAMc,OAAO,GAAG,CAACC,MAAW,EAAEC,IAAW,GAAW;QAClDN,OAAO,EAAE,CAAC;QACVK,MAAM,IAAIC,IAAI,CAAC,CAAC;QAChBhB,OAAO,CAACQ,KAAK,EAAE,CAAC;KACjB,AAAC;IAEF,MAAMS,cAAc,GAAG,IAAY;QACjCjC,OAAO,CAACC,GAAG,GAAG,CAAC,GAAG+B,IAAI,AAAK,GAAKF,OAAO,CAAC/B,OAAO,EAAEiC,IAAI,CAAC;QAAA,CAAC;QACvDhC,OAAO,CAACG,IAAI,GAAG,CAAC,GAAG6B,IAAI,AAAK,GAAKF,OAAO,CAAC5B,QAAQ,EAAE8B,IAAI,CAAC;QAAA,CAAC;QACzDhC,OAAO,CAACK,IAAI,GAAG,CAAC,GAAG2B,IAAI,AAAK,GAAKF,OAAO,CAAC1B,QAAQ,EAAE4B,IAAI,CAAC;QAAA,CAAC;QACzDhC,OAAO,CAACO,KAAK,GAAG,CAAC,GAAGyB,IAAI,AAAK,GAAKF,OAAO,CAACxB,SAAS,EAAE0B,IAAI,CAAC;QAAA,CAAC;QAE3DxB,eAAe,CAAC0B,IAAI,CAAClB,OAAO,CAAC,CAAC;KAC/B,AAAC;IAEF,MAAMmB,eAAe,GAAG,IAAY;QAClCnC,OAAO,CAACC,GAAG,GAAGF,OAAO,CAAC;QACtBC,OAAO,CAACG,IAAI,GAAGD,QAAQ,CAAC;QACxBF,OAAO,CAACK,IAAI,GAAGD,QAAQ,CAAC;QACxBJ,OAAO,CAACO,KAAK,GAAGD,SAAS,CAAC;QAE1B,MAAM8B,KAAK,GAAG5B,eAAe,CAAC6B,OAAO,CAACrB,OAAO,CAAC,AAAC;QAC/C,IAAIoB,KAAK,IAAI,CAAC,EAAE;YACd5B,eAAe,CAAC8B,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC;SAClC;KACF,AAAC;IAEFpB,OAAO,CAACQ,KAAK,GAAG,CAACb,IAAI,GAAU;QAC7BsB,cAAc,EAAE,CAAC;QACjB,OAAOV,QAAQ,CAACZ,IAAI,CAAC,CAAC;KACvB,CAAC;IAEFK,OAAO,CAACa,cAAc,GAAG,CAACpB,OAAO,GAAU;QACzC,MAAM8B,MAAM,GAAGX,iBAAiB,CAACnB,OAAO,CAAC,AAAC;QAC1C0B,eAAe,EAAE,CAAC;QAClB,OAAOI,MAAM,CAAC;KACf,CAAC;IAEFvB,OAAO,CAACW,IAAI,GAAG,IAAW;QACxB,MAAMY,MAAM,GAAGb,OAAO,EAAE,AAAC;QACzBS,eAAe,EAAE,CAAC;QAClB,OAAOI,MAAM,CAAC;KACf,CAAC;IAEF,sEAAsE;IACtE,2BAA2B;IAE3B,OAAOvB,OAAO,CAAC;CAChB;AAQM,SAASlB,aAAa,CAAC0C,KAAa,EAAE;IAC3C,MAAMxB,OAAO,GAAGnB,GAAG,CAAC4C,MAAK,QAAA,CAACC,IAAI,CAACF,KAAK,CAAC,CAAC,AAAC;IACvC,oDAAoD;IACpDxB,OAAO,CAACQ,KAAK,EAAE,CAAC;IAChB,OAAOR,OAAO,CAAC;CAChB"}