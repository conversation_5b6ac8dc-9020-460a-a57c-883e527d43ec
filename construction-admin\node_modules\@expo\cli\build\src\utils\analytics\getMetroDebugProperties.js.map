{"version": 3, "sources": ["../../../../src/utils/analytics/getMetroDebugProperties.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\n\nexport type DebugTool = {\n  name: string;\n  version?: string;\n};\n\nexport function getMetroDebugProperties(\n  projectRoot: string,\n  exp: ExpoConfig,\n  debugTool: DebugTool\n) {\n  return {\n    sdkVersion: exp.sdkVersion,\n    metroVersion: require('metro/package.json').version,\n    toolName: debugTool.name,\n    toolVersion: debugTool.version,\n  };\n}\n"], "names": ["getMetroDebugProperties", "projectRoot", "exp", "debugTool", "sdkVersion", "metroVersion", "require", "version", "toolName", "name", "toolVersion"], "mappings": "AAAA;;;;QAOgBA,uBAAuB,GAAvBA,uBAAuB;AAAhC,SAASA,uBAAuB,CACrCC,WAAmB,EACnBC,GAAe,EACfC,SAAoB,EACpB;IACA,OAAO;QACLC,UAAU,EAAEF,GAAG,CAACE,UAAU;QAC1BC,YAAY,EAAEC,OAAO,CAAC,oBAAoB,CAAC,CAACC,OAAO;QACnDC,QAAQ,EAAEL,SAAS,CAACM,IAAI;QACxBC,WAAW,EAAEP,SAAS,CAACI,OAAO;KAC/B,CAAC;CACH"}