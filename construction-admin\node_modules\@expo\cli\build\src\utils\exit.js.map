{"version": 3, "sources": ["../../../src/utils/exit.ts"], "sourcesContent": ["import { guardAsync } from './fn';\n\nconst debug = require('debug')('expo:utils:exit') as typeof console.log;\n\ntype AsyncExitHook = (signal: NodeJS.Signals) => void | Promise<void>;\n\nconst PRE_EXIT_SIGNALS: NodeJS.Signals[] = ['SIGHUP', 'SIGINT', 'SIGTERM', 'SIGBREAK'];\n\n// We create a queue since Node.js throws an error if we try to append too many listeners:\n// (node:4405) MaxListenersExceededWarning: Possible EventEmitter memory leak detected. 11 SIGINT listeners added to [process]. Use emitter.setMaxListeners() to increase limit\nconst queue: AsyncExitHook[] = [];\n\nlet unsubscribe: (() => void) | null = null;\n\n/** Add functions that run before the process exits. Returns a function for removing the listeners. */\nexport function installExitHooks(asyncExitHook: AsyncExitHook): () => void {\n  // We need to instantiate the master listener the first time the queue is used.\n  if (!queue.length) {\n    // Track the master listener so we can remove it later.\n    unsubscribe = attachMasterListener();\n  }\n\n  queue.push(asyncExitHook);\n\n  return () => {\n    const index = queue.indexOf(asyncExitHook);\n    if (index >= 0) {\n      queue.splice(index, 1);\n    }\n    // Clean up the master listener if we don't need it anymore.\n    if (!queue.length) {\n      unsubscribe?.();\n    }\n  };\n}\n\n// Create a function that runs before the process exits and guards against running multiple times.\nfunction createExitHook(signal: NodeJS.Signals) {\n  return guardAsync(async () => {\n    debug(`pre-exit (signal: ${signal}, queue length: ${queue.length})`);\n\n    for (const [index, hookAsync] of Object.entries(queue)) {\n      try {\n        await hookAsync(signal);\n      } catch (error: any) {\n        debug(`Error in exit hook: %O (queue: ${index})`, error);\n      }\n    }\n\n    debug(`post-exit (code: ${process.exitCode ?? 0})`);\n\n    process.exit();\n  });\n}\n\nfunction attachMasterListener() {\n  const hooks: [NodeJS.Signals, () => any][] = [];\n  for (const signal of PRE_EXIT_SIGNALS) {\n    const hook = createExitHook(signal);\n    hooks.push([signal, hook]);\n    process.on(signal, hook);\n  }\n  return () => {\n    for (const [signal, hook] of hooks) {\n      process.removeListener(signal, hook);\n    }\n  };\n}\n"], "names": ["installExitHooks", "debug", "require", "PRE_EXIT_SIGNALS", "queue", "unsubscribe", "asyncExitHook", "length", "attachMasterListener", "push", "index", "indexOf", "splice", "createExitHook", "signal", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Object", "entries", "error", "process", "exitCode", "exit", "hooks", "hook", "on", "removeListener"], "mappings": "AAAA;;;;QAegBA,gBAAgB,GAAhBA,gBAAgB;AAfL,IAAA,GAAM,WAAN,MAAM,CAAA;AAEjC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,AAAsB,AAAC;AAIxE,MAAMC,gBAAgB,GAAqB;IAAC,QAAQ;IAAE,QAAQ;IAAE,SAAS;IAAE,UAAU;CAAC,AAAC;AAEvF,0FAA0F;AAC1F,+KAA+K;AAC/K,MAAMC,KAAK,GAAoB,EAAE,AAAC;AAElC,IAAIC,WAAW,GAAwB,IAAI,AAAC;AAGrC,SAASL,gBAAgB,CAACM,aAA4B,EAAc;IACzE,+EAA+E;IAC/E,IAAI,CAACF,KAAK,CAACG,MAAM,EAAE;QACjB,uDAAuD;QACvDF,WAAW,GAAGG,oBAAoB,EAAE,CAAC;KACtC;IAEDJ,KAAK,CAACK,IAAI,CAACH,aAAa,CAAC,CAAC;IAE1B,OAAO,IAAM;QACX,MAAMI,KAAK,GAAGN,KAAK,CAACO,OAAO,CAACL,aAAa,CAAC,AAAC;QAC3C,IAAII,KAAK,IAAI,CAAC,EAAE;YACdN,KAAK,CAACQ,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC;SACxB;QACD,4DAA4D;QAC5D,IAAI,CAACN,KAAK,CAACG,MAAM,EAAE;YACjBF,WAAW,QAAI,GAAfA,KAAAA,CAAe,GAAfA,WAAW,EAAI,AA/BrB,CA+BsB;SACjB;KACF,CAAC;CACH;AAED,kGAAkG;AAClG,SAASQ,cAAc,CAACC,MAAsB,EAAE;IAC9C,OAAOC,CAAAA,GAAAA,GAAU,AAcf,CAAA,WAde,CAAC,UAAY;QAC5Bd,KAAK,CAAC,CAAC,kBAAkB,EAAEa,MAAM,CAAC,gBAAgB,EAAEV,KAAK,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAErE,KAAK,MAAM,CAACG,KAAK,EAAEM,SAAS,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACd,KAAK,CAAC,CAAE;YACtD,IAAI;gBACF,MAAMY,SAAS,CAACF,MAAM,CAAC,CAAC;aACzB,CAAC,OAAOK,KAAK,EAAO;gBACnBlB,KAAK,CAAC,CAAC,+BAA+B,EAAES,KAAK,CAAC,CAAC,CAAC,EAAES,KAAK,CAAC,CAAC;aAC1D;SACF;YAEyBC,SAAgB;QAA1CnB,KAAK,CAAC,CAAC,iBAAiB,EAAEmB,CAAAA,SAAgB,GAAhBA,OAAO,CAACC,QAAQ,YAAhBD,SAAgB,GAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpDA,OAAO,CAACE,IAAI,EAAE,CAAC;KAChB,CAAC,CAAC;CACJ;AAED,SAASd,oBAAoB,GAAG;IAC9B,MAAMe,KAAK,GAAkC,EAAE,AAAC;IAChD,KAAK,MAAMT,OAAM,IAAIX,gBAAgB,CAAE;QACrC,MAAMqB,IAAI,GAAGX,cAAc,CAACC,OAAM,CAAC,AAAC;QACpCS,KAAK,CAACd,IAAI,CAAC;YAACK,OAAM;YAAEU,IAAI;SAAC,CAAC,CAAC;QAC3BJ,OAAO,CAACK,EAAE,CAACX,OAAM,EAAEU,IAAI,CAAC,CAAC;KAC1B;IACD,OAAO,IAAM;QACX,KAAK,MAAM,CAACV,MAAM,EAAEU,IAAI,CAAC,IAAID,KAAK,CAAE;YAClCH,OAAO,CAACM,cAAc,CAACZ,MAAM,EAAEU,IAAI,CAAC,CAAC;SACtC;KACF,CAAC;CACH"}