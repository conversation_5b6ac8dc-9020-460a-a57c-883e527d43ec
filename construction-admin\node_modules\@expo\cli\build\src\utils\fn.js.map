{"version": 3, "sources": ["../../../src/utils/fn.ts"], "sourcesContent": ["/** `lodash.memoize` */\nexport function memoize<T extends (...args: any[]) => any>(fn: T): T {\n  const cache: { [key: string]: any } = {};\n  return ((...args: any[]) => {\n    const key = JSON.stringify(args);\n    if (cache[key]) {\n      return cache[key];\n    }\n    const result = fn(...args);\n    cache[key] = result;\n    return result;\n  }) as any;\n}\n\n/** memoizes an async function to prevent subsequent calls that might be invoked before the function has finished resolving. */\nexport function guardAsync<V, T extends (...args: any[]) => Promise<V>>(fn: T): T {\n  let invoked = false;\n  let returnValue: V;\n\n  const guard: any = async (...args: any[]): Promise<V> => {\n    if (!invoked) {\n      invoked = true;\n      returnValue = await fn(...args);\n    }\n\n    return returnValue;\n  };\n\n  return guard;\n}\n"], "names": ["memoize", "<PERSON><PERSON><PERSON>", "fn", "cache", "args", "key", "JSON", "stringify", "result", "invoked", "returnValue", "guard"], "mappings": "AACA;;;;QAAgBA,OAAO,GAAPA,OAAO;QAcPC,UAAU,GAAVA,UAAU;AAdnB,SAASD,OAAO,CAAoCE,EAAK,EAAK;IACnE,MAAMC,KAAK,GAA2B,EAAE,AAAC;IACzC,OAAQ,CAAC,GAAGC,IAAI,AAAO,GAAK;QAC1B,MAAMC,GAAG,GAAGC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,AAAC;QACjC,IAAID,KAAK,CAACE,GAAG,CAAC,EAAE;YACd,OAAOF,KAAK,CAACE,GAAG,CAAC,CAAC;SACnB;QACD,MAAMG,MAAM,GAAGN,EAAE,IAAIE,IAAI,CAAC,AAAC;QAC3BD,KAAK,CAACE,GAAG,CAAC,GAAGG,MAAM,CAAC;QACpB,OAAOA,MAAM,CAAC;KACf,CAAS;CACX;AAGM,SAASP,UAAU,CAA8CC,EAAK,EAAK;IAChF,IAAIO,OAAO,GAAG,KAAK,AAAC;IACpB,IAAIC,WAAW,AAAG,AAAC;IAEnB,MAAMC,KAAK,GAAQ,OAAO,GAAGP,IAAI,AAAO,GAAiB;QACvD,IAAI,CAACK,OAAO,EAAE;YACZA,OAAO,GAAG,IAAI,CAAC;YACfC,WAAW,GAAG,MAAMR,EAAE,IAAIE,IAAI,CAAC,CAAC;SACjC;QAED,OAAOM,WAAW,CAAC;KACpB,AAAC;IAEF,OAAOC,KAAK,CAAC;CACd"}