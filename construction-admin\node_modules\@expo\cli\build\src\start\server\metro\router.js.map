{"version": 3, "sources": ["../../../../../src/start/server/metro/router.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport chalk from 'chalk';\nimport { sync as globSync } from 'glob';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { Log } from '../../../log';\nimport { directoryExistsSync } from '../../../utils/dir';\nimport { memoize } from '../../../utils/fn';\nimport { learnMore } from '../../../utils/link';\n\nconst debug = require('debug')('expo:start:server:metro:router') as typeof console.log;\n\n/**\n * Get the relative path for requiring the `/app` folder relative to the `expo-router/entry` file.\n * This mechanism does require the server to restart after the `expo-router` package is installed.\n */\nexport function getAppRouterRelativeEntryPath(\n  projectRoot: string,\n  routerDirectory: string = getRouterDirectory(projectRoot)\n): string | undefined {\n  // Auto pick App entry\n  const routerEntry =\n    resolveFrom.silent(projectRoot, 'expo-router/entry') ?? getFallbackEntryRoot(projectRoot);\n  if (!routerEntry) {\n    return undefined;\n  }\n  // It doesn't matter if the app folder exists.\n  const appFolder = path.join(projectRoot, routerDirectory);\n  const appRoot = path.relative(path.dirname(routerEntry), appFolder);\n  debug('expo-router entry', routerEntry, appFolder, appRoot);\n  return appRoot;\n}\n\n/** If the `expo-router` package is not installed, then use the `expo` package to determine where the node modules are relative to the project. */\nfunction getFallbackEntryRoot(projectRoot: string): string {\n  const expoRoot = resolveFrom.silent(projectRoot, 'expo/package.json');\n  if (expoRoot) {\n    return path.join(path.dirname(path.dirname(expoRoot)), 'expo-router/entry');\n  }\n  return path.join(projectRoot, 'node_modules/expo-router/entry');\n}\n\nexport function getRouterDirectoryModuleIdWithManifest(\n  projectRoot: string,\n  exp: ExpoConfig\n): string {\n  return exp.extra?.router?.root ?? getRouterDirectory(projectRoot);\n}\n\nconst logSrcDir = memoize(() =>\n  Log.log(chalk.gray('Using src/app as the root directory for Expo Router.'))\n);\n\nexport function getRouterDirectory(projectRoot: string): string {\n  // more specific directories first\n  if (directoryExistsSync(path.join(projectRoot, 'src/app'))) {\n    logSrcDir();\n    return 'src/app';\n  }\n\n  debug('Using app as the root directory for Expo Router.');\n  return 'app';\n}\n\nexport function isApiRouteConvention(name: string): boolean {\n  return /\\+api\\.[tj]sx?$/.test(name);\n}\n\nexport function getApiRoutesForDirectory(cwd: string) {\n  return globSync('**/*+api.@(ts|tsx|js|jsx)', {\n    cwd,\n    absolute: true,\n  });\n}\n\n// Used to emulate a context module, but way faster. TODO: May need to adjust the extensions to stay in sync with Metro.\nexport function getRoutePaths(cwd: string) {\n  return globSync('**/*.@(ts|tsx|js|jsx)', {\n    cwd,\n  }).map((p) => './' + normalizePaths(p));\n}\n\nfunction normalizePaths(p: string) {\n  return p.replace(/\\\\/g, '/');\n}\n\nlet hasWarnedAboutApiRouteOutput = false;\n\nexport function hasWarnedAboutApiRoutes() {\n  return hasWarnedAboutApiRouteOutput;\n}\n\nexport function warnInvalidWebOutput() {\n  if (!hasWarnedAboutApiRouteOutput) {\n    Log.warn(\n      chalk.yellow`Using API routes requires the {bold web.output} to be set to {bold \"server\"} in the project {bold app.json}. ${learnMore(\n        'https://docs.expo.dev/router/reference/api-routes/'\n      )}`\n    );\n  }\n\n  hasWarnedAboutApiRouteOutput = true;\n}\n"], "names": ["getAppRouterRelativeEntryPath", "getRouterDirectoryModuleIdWithManifest", "getRouterDirectory", "isApiRouteConvention", "getApiRoutesForDirectory", "getRoutePaths", "hasWarnedAboutApiRoutes", "warnInvalidWebOutput", "debug", "require", "projectRoot", "routerDirectory", "resolveFrom", "routerEntry", "silent", "getFallbackEntryRoot", "undefined", "appFolder", "path", "join", "appRoot", "relative", "dirname", "expoRoot", "exp", "extra", "router", "root", "logSrcDir", "memoize", "Log", "log", "chalk", "gray", "directoryExistsSync", "name", "test", "cwd", "globSync", "absolute", "map", "p", "normalizePaths", "replace", "hasWarnedAboutApiRouteOutput", "warn", "yellow", "learnMore"], "mappings": "AAAA;;;;QAiBgBA,6BAA6B,GAA7BA,6BAA6B;QA0B7BC,sCAAsC,GAAtCA,sCAAsC;QAWtCC,kBAAkB,GAAlBA,kBAAkB;QAWlBC,oBAAoB,GAApBA,oBAAoB;QAIpBC,wBAAwB,GAAxBA,wBAAwB;QAQxBC,aAAa,GAAbA,aAAa;QAYbC,uBAAuB,GAAvBA,uBAAuB;QAIvBC,oBAAoB,GAApBA,oBAAoB;AA5FlB,IAAA,MAAO,kCAAP,OAAO,EAAA;AACQ,IAAA,KAAM,WAAN,MAAM,CAAA;AACtB,IAAA,KAAM,kCAAN,MAAM,EAAA;AACC,IAAA,YAAc,kCAAd,cAAc,EAAA;AAElB,IAAA,IAAc,WAAd,cAAc,CAAA;AACE,IAAA,IAAoB,WAApB,oBAAoB,CAAA;AAChC,IAAA,GAAmB,WAAnB,mBAAmB,CAAA;AACjB,IAAA,KAAqB,WAArB,qBAAqB,CAAA;;;;;;AAE/C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,gCAAgC,CAAC,AAAsB,AAAC;AAMhF,SAAST,6BAA6B,CAC3CU,WAAmB,EACnBC,eAAuB,GAAGT,kBAAkB,CAACQ,WAAW,CAAC,EACrC;QAGlBE,GAAoD;IAFtD,sBAAsB;IACtB,MAAMC,WAAW,GACfD,CAAAA,GAAoD,GAApDA,YAAW,QAAA,CAACE,MAAM,CAACJ,WAAW,EAAE,mBAAmB,CAAC,YAApDE,GAAoD,GAAIG,oBAAoB,CAACL,WAAW,CAAC,AAAC;IAC5F,IAAI,CAACG,WAAW,EAAE;QAChB,OAAOG,SAAS,CAAC;KAClB;IACD,8CAA8C;IAC9C,MAAMC,SAAS,GAAGC,KAAI,QAAA,CAACC,IAAI,CAACT,WAAW,EAAEC,eAAe,CAAC,AAAC;IAC1D,MAAMS,OAAO,GAAGF,KAAI,QAAA,CAACG,QAAQ,CAACH,KAAI,QAAA,CAACI,OAAO,CAACT,WAAW,CAAC,EAAEI,SAAS,CAAC,AAAC;IACpET,KAAK,CAAC,mBAAmB,EAAEK,WAAW,EAAEI,SAAS,EAAEG,OAAO,CAAC,CAAC;IAC5D,OAAOA,OAAO,CAAC;CAChB;AAED,kJAAkJ,CAClJ,SAASL,oBAAoB,CAACL,WAAmB,EAAU;IACzD,MAAMa,QAAQ,GAAGX,YAAW,QAAA,CAACE,MAAM,CAACJ,WAAW,EAAE,mBAAmB,CAAC,AAAC;IACtE,IAAIa,QAAQ,EAAE;QACZ,OAAOL,KAAI,QAAA,CAACC,IAAI,CAACD,KAAI,QAAA,CAACI,OAAO,CAACJ,KAAI,QAAA,CAACI,OAAO,CAACC,QAAQ,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;KAC7E;IACD,OAAOL,KAAI,QAAA,CAACC,IAAI,CAACT,WAAW,EAAE,gCAAgC,CAAC,CAAC;CACjE;AAEM,SAAST,sCAAsC,CACpDS,WAAmB,EACnBc,GAAe,EACP;QACDA,GAAS;QAATA,IAAuB;IAA9B,OAAOA,CAAAA,IAAuB,GAAvBA,CAAAA,GAAS,GAATA,GAAG,CAACC,KAAK,SAAQ,GAAjBD,KAAAA,CAAiB,GAAjBA,QAAAA,GAAS,CAAEE,MAAM,SAAA,GAAjBF,KAAAA,CAAiB,QAAEG,IAAI,AAAN,YAAjBH,IAAuB,GAAItB,kBAAkB,CAACQ,WAAW,CAAC,CAAC;CACnE;AAED,MAAMkB,SAAS,GAAGC,CAAAA,GAAAA,GAAO,AAExB,CAAA,QAFwB,CAAC,IACxBC,IAAG,IAAA,CAACC,GAAG,CAACC,MAAK,QAAA,CAACC,IAAI,CAAC,sDAAsD,CAAC,CAAC;AAAA,CAC5E,AAAC;AAEK,SAAS/B,kBAAkB,CAACQ,WAAmB,EAAU;IAC9D,kCAAkC;IAClC,IAAIwB,CAAAA,GAAAA,IAAmB,AAAmC,CAAA,oBAAnC,CAAChB,KAAI,QAAA,CAACC,IAAI,CAACT,WAAW,EAAE,SAAS,CAAC,CAAC,EAAE;QAC1DkB,SAAS,EAAE,CAAC;QACZ,OAAO,SAAS,CAAC;KAClB;IAEDpB,KAAK,CAAC,kDAAkD,CAAC,CAAC;IAC1D,OAAO,KAAK,CAAC;CACd;AAEM,SAASL,oBAAoB,CAACgC,IAAY,EAAW;IAC1D,OAAO,kBAAkBC,IAAI,CAACD,IAAI,CAAC,CAAC;CACrC;AAEM,SAAS/B,wBAAwB,CAACiC,GAAW,EAAE;IACpD,OAAOC,CAAAA,GAAAA,KAAQ,AAGb,CAAA,KAHa,CAAC,2BAA2B,EAAE;QAC3CD,GAAG;QACHE,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;CACJ;AAGM,SAASlC,aAAa,CAACgC,GAAW,EAAE;IACzC,OAAOC,CAAAA,GAAAA,KAAQ,AAEb,CAAA,KAFa,CAAC,uBAAuB,EAAE;QACvCD,GAAG;KACJ,CAAC,CAACG,GAAG,CAAC,CAACC,CAAC,GAAK,IAAI,GAAGC,cAAc,CAACD,CAAC,CAAC;IAAA,CAAC,CAAC;CACzC;AAED,SAASC,cAAc,CAACD,CAAS,EAAE;IACjC,OAAOA,CAAC,CAACE,OAAO,QAAQ,GAAG,CAAC,CAAC;CAC9B;AAED,IAAIC,4BAA4B,GAAG,KAAK,AAAC;AAElC,SAAStC,uBAAuB,GAAG;IACxC,OAAOsC,4BAA4B,CAAC;CACrC;AAEM,SAASrC,oBAAoB,GAAG;IACrC,IAAI,CAACqC,4BAA4B,EAAE;QACjCd,IAAG,IAAA,CAACe,IAAI,CACNb,MAAK,QAAA,CAACc,MAAM,CAAC,6GAA6G,EAAEC,CAAAA,GAAAA,KAAS,AAEpI,CAAA,UAFoI,CACnI,oDAAoD,CACrD,CAAC,CAAC,CACJ,CAAC;KACH;IAEDH,4BAA4B,GAAG,IAAI,CAAC;CACrC"}