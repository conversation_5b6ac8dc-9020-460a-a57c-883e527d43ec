{"version": 3, "sources": ["../../../../../src/start/server/metro/bundleApiRoutes.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport { logMetroErrorAsync } from './metroErrorInterface';\nimport { requireFileContentsWithMetro } from '../getStaticRenderFunctions';\n\nconst debug = require('debug')('expo:api-routes') as typeof console.log;\n\nconst pendingRouteOperations = new Map<string, Promise<{ src: string; filename: string } | null>>();\n\nexport type ApiRouteOptions = {\n  mode?: string;\n  routerRoot: string;\n  port?: number;\n  shouldThrow?: boolean;\n  baseUrl: string;\n};\n\n// Bundle the API Route with Metro and return the string contents to be evaluated in the server.\nexport async function bundleApiRoute(\n  projectRoot: string,\n  filepath: string,\n  options: ApiRouteOptions\n): Promise<{ src: string; filename: string } | null | undefined> {\n  if (pendingRouteOperations.has(filepath)) {\n    return pendingRouteOperations.get(filepath);\n  }\n\n  const devServerUrl = `http://localhost:${options.port}`;\n\n  async function bundleAsync() {\n    try {\n      debug('Bundle API route:', options.routerRoot, filepath);\n\n      const middleware = await requireFileContentsWithMetro(projectRoot, devServerUrl, filepath, {\n        minify: options.mode === 'production',\n        dev: options.mode !== 'production',\n        // Ensure Node.js\n        environment: 'node',\n        baseUrl: options.baseUrl,\n        routerRoot: options.routerRoot,\n      });\n\n      return middleware;\n    } catch (error: any) {\n      if (error instanceof Error) {\n        await logMetroErrorAsync({ error, projectRoot });\n      }\n      if (options.shouldThrow) {\n        throw error;\n      }\n      // TODO: improve error handling, maybe have this be a mock function which returns the static error html\n      return null;\n    } finally {\n      // pendingRouteOperations.delete(filepath);\n    }\n  }\n  const route = bundleAsync();\n\n  pendingRouteOperations.set(filepath, route);\n  return route;\n}\n\nexport async function invalidateApiRouteCache() {\n  pendingRouteOperations.clear();\n}\n"], "names": ["bundleApiRoute", "invalidateApiRouteCache", "debug", "require", "pendingRouteOperations", "Map", "projectRoot", "filepath", "options", "has", "get", "devServerUrl", "port", "bundleAsync", "routerRoot", "middleware", "requireFileContentsWithMetro", "minify", "mode", "dev", "environment", "baseUrl", "error", "Error", "logMetroErrorAsync", "shouldThrow", "route", "set", "clear"], "mappings": "AAOA;;;;QAgBsBA,cAAc,GAAdA,cAAc;QA4CdC,uBAAuB,GAAvBA,uBAAuB;AA5DV,IAAA,oBAAuB,WAAvB,uBAAuB,CAAA;AACb,IAAA,yBAA6B,WAA7B,6BAA6B,CAAA;AAE1E,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,AAAsB,AAAC;AAExE,MAAMC,sBAAsB,GAAG,IAAIC,GAAG,EAA6D,AAAC;AAW7F,eAAeL,cAAc,CAClCM,WAAmB,EACnBC,QAAgB,EAChBC,OAAwB,EACuC;IAC/D,IAAIJ,sBAAsB,CAACK,GAAG,CAACF,QAAQ,CAAC,EAAE;QACxC,OAAOH,sBAAsB,CAACM,GAAG,CAACH,QAAQ,CAAC,CAAC;KAC7C;IAED,MAAMI,YAAY,GAAG,CAAC,iBAAiB,EAAEH,OAAO,CAACI,IAAI,CAAC,CAAC,AAAC;IAExD,eAAeC,WAAW,GAAG;QAC3B,IAAI;YACFX,KAAK,CAAC,mBAAmB,EAAEM,OAAO,CAACM,UAAU,EAAEP,QAAQ,CAAC,CAAC;YAEzD,MAAMQ,UAAU,GAAG,MAAMC,CAAAA,GAAAA,yBAA4B,AAOnD,CAAA,6BAPmD,CAACV,WAAW,EAAEK,YAAY,EAAEJ,QAAQ,EAAE;gBACzFU,MAAM,EAAET,OAAO,CAACU,IAAI,KAAK,YAAY;gBACrCC,GAAG,EAAEX,OAAO,CAACU,IAAI,KAAK,YAAY;gBAClC,iBAAiB;gBACjBE,WAAW,EAAE,MAAM;gBACnBC,OAAO,EAAEb,OAAO,CAACa,OAAO;gBACxBP,UAAU,EAAEN,OAAO,CAACM,UAAU;aAC/B,CAAC,AAAC;YAEH,OAAOC,UAAU,CAAC;SACnB,CAAC,OAAOO,KAAK,EAAO;YACnB,IAAIA,KAAK,YAAYC,KAAK,EAAE;gBAC1B,MAAMC,CAAAA,GAAAA,oBAAkB,AAAwB,CAAA,mBAAxB,CAAC;oBAAEF,KAAK;oBAAEhB,WAAW;iBAAE,CAAC,CAAC;aAClD;YACD,IAAIE,OAAO,CAACiB,WAAW,EAAE;gBACvB,MAAMH,KAAK,CAAC;aACb;YACD,uGAAuG;YACvG,OAAO,IAAI,CAAC;SACb,QAAS;QACR,2CAA2C;SAC5C;KACF;IACD,MAAMI,KAAK,GAAGb,WAAW,EAAE,AAAC;IAE5BT,sBAAsB,CAACuB,GAAG,CAACpB,QAAQ,EAAEmB,KAAK,CAAC,CAAC;IAC5C,OAAOA,KAAK,CAAC;CACd;AAEM,eAAezB,uBAAuB,GAAG;IAC9CG,sBAAsB,CAACwB,KAAK,EAAE,CAAC;CAChC"}