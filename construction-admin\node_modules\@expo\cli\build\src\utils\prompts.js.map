{"version": 3, "sources": ["../../../src/utils/prompts.ts"], "sourcesContent": ["import assert from 'assert';\nimport prompts, { Choice, Options, PromptObject } from 'prompts';\n\nimport { AbortCommandError, CommandError } from './errors';\nimport { isInteractive } from './interactive';\n\nconst debug = require('debug')('expo:utils:prompts') as typeof console.log;\n\nexport type Question<V extends string = string> = PromptObject<V> & {\n  optionsPerPage?: number;\n};\n\nexport interface ExpoChoice<T> extends Choice {\n  value: T;\n}\n\ntype PromptOptions = { nonInteractiveHelp?: string } & Options;\n\nexport type NamelessQuestion = Omit<Question<'value'>, 'name' | 'type'>;\n\ntype InteractionOptions = { pause: boolean; canEscape?: boolean };\n\ntype InteractionCallback = (options: InteractionOptions) => void;\n\n/** Interaction observers for detecting when keystroke tracking should pause/resume. */\nconst listeners: InteractionCallback[] = [];\n\nexport default async function prompt(\n  questions: Question | Question[],\n  { nonInteractiveHelp, ...options }: PromptOptions = {}\n) {\n  questions = Array.isArray(questions) ? questions : [questions];\n  if (!isInteractive() && questions.length !== 0) {\n    let message = `Input is required, but 'npx expo' is in non-interactive mode.\\n`;\n    if (nonInteractiveHelp) {\n      message += nonInteractiveHelp;\n    } else {\n      const question = questions[0];\n      const questionMessage =\n        typeof question.message === 'function'\n          ? question.message(undefined, {}, question)\n          : question.message;\n\n      message += `Required input:\\n${(questionMessage || '').trim().replace(/^/gm, '> ')}`;\n    }\n    throw new CommandError('NON_INTERACTIVE', message);\n  }\n\n  pauseInteractions();\n  try {\n    const results = await prompts(questions, {\n      onCancel() {\n        throw new AbortCommandError();\n      },\n      ...options,\n    });\n\n    return results;\n  } finally {\n    resumeInteractions();\n  }\n}\n\n/**\n * Create a standard yes/no confirmation that can be cancelled.\n *\n * @param questions\n * @param options\n */\nexport async function confirmAsync(\n  questions: NamelessQuestion,\n  options?: PromptOptions\n): Promise<boolean> {\n  const { value } = await prompt(\n    {\n      initial: true,\n      ...questions,\n      name: 'value',\n      type: 'confirm',\n    },\n    options\n  );\n  return value ?? null;\n}\n\n/** Select an option from a list of options. */\nexport async function selectAsync<T>(\n  message: string,\n  choices: ExpoChoice<T>[],\n  options?: PromptOptions\n): Promise<T> {\n  const { value } = await prompt(\n    {\n      message,\n      choices,\n      name: 'value',\n      type: 'select',\n    },\n    options\n  );\n  return value ?? null;\n}\n\nexport const promptAsync = prompt;\n\n/** Used to pause/resume interaction observers while prompting (made for TerminalUI). */\nexport function addInteractionListener(callback: InteractionCallback) {\n  listeners.push(callback);\n}\n\nexport function removeInteractionListener(callback: InteractionCallback) {\n  const listenerIndex = listeners.findIndex((_callback) => _callback === callback);\n  assert(\n    listenerIndex >= 0,\n    'removeInteractionListener(): cannot remove an unregistered event listener.'\n  );\n  listeners.splice(listenerIndex, 1);\n}\n\n/** Notify all listeners that keypress observations must pause. */\nexport function pauseInteractions(options: Omit<InteractionOptions, 'pause'> = {}) {\n  debug('Interaction observers paused');\n  for (const listener of listeners) {\n    listener({ pause: true, ...options });\n  }\n}\n\n/** Notify all listeners that keypress observations can start.. */\nexport function resumeInteractions(options: Omit<InteractionOptions, 'pause'> = {}) {\n  debug('Interaction observers resumed');\n  for (const listener of listeners) {\n    listener({ pause: false, ...options });\n  }\n}\n\nexport function createSelectionFilter(): (input: any, choices: Choice[]) => Promise<any> {\n  function escapeRegex(string: string) {\n    return string.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n  }\n\n  return async (input: any, choices: Choice[]) => {\n    try {\n      const regex = new RegExp(escapeRegex(input), 'i');\n      return choices.filter((choice: any) => regex.test(choice.title));\n    } catch (error: any) {\n      debug('Error filtering choices', error);\n      return [];\n    }\n  };\n}\n"], "names": ["prompt", "<PERSON><PERSON><PERSON>", "selectAsync", "addInteractionListener", "removeInteractionListener", "pauseInteractions", "resumeInteractions", "createSelectionFilter", "questions", "nonInteractiveHelp", "options", "Array", "isArray", "isInteractive", "length", "message", "question", "questionMessage", "undefined", "trim", "replace", "CommandError", "results", "prompts", "onCancel", "AbortCommandError", "debug", "require", "listeners", "value", "initial", "name", "type", "choices", "promptAsync", "callback", "push", "listenerIndex", "findIndex", "_callback", "assert", "splice", "listener", "pause", "escapeRegex", "string", "input", "regex", "RegExp", "filter", "choice", "test", "title", "error"], "mappings": "AAAA;;;;kBA2B8BA,MAAM;QA0CdC,YAAY,GAAZA,YAAY;QAiBZC,WAAW,GAAXA,WAAW;QAoBjBC,sBAAsB,GAAtBA,sBAAsB;QAItBC,yBAAyB,GAAzBA,yBAAyB;QAUzBC,iBAAiB,GAAjBA,iBAAiB;QAQjBC,kBAAkB,GAAlBA,kBAAkB;QAOlBC,qBAAqB,GAArBA,qBAAqB;;AAvIlB,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AAC4B,IAAA,QAAS,kCAAT,SAAS,EAAA;AAEhB,IAAA,OAAU,WAAV,UAAU,CAAA;AAC5B,IAAA,YAAe,WAAf,eAAe,CAAA;AAuB9B,eAAeP,MAAM,CAClCQ,SAAgC,EAChC,EAAEC,kBAAkB,CAAA,EAAE,GAAGC,OAAO,EAAiB,GAAG,EAAE,EACtD;IACAF,SAAS,GAAGG,KAAK,CAACC,OAAO,CAACJ,SAAS,CAAC,GAAGA,SAAS,GAAG;QAACA,SAAS;KAAC,CAAC;IAC/D,IAAI,CAACK,CAAAA,GAAAA,YAAa,AAAE,CAAA,cAAF,EAAE,IAAIL,SAAS,CAACM,MAAM,KAAK,CAAC,EAAE;QAC9C,IAAIC,OAAO,GAAG,CAAC,+DAA+D,CAAC,AAAC;QAChF,IAAIN,kBAAkB,EAAE;YACtBM,OAAO,IAAIN,kBAAkB,CAAC;SAC/B,MAAM;YACL,MAAMO,QAAQ,GAAGR,SAAS,CAAC,CAAC,CAAC,AAAC;YAC9B,MAAMS,eAAe,GACnB,OAAOD,QAAQ,CAACD,OAAO,KAAK,UAAU,GAClCC,QAAQ,CAACD,OAAO,CAACG,SAAS,EAAE,EAAE,EAAEF,QAAQ,CAAC,GACzCA,QAAQ,CAACD,OAAO,AAAC;YAEvBA,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAACE,eAAe,IAAI,EAAE,CAAC,CAACE,IAAI,EAAE,CAACC,OAAO,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC;SACtF;QACD,MAAM,IAAIC,OAAY,aAAA,CAAC,iBAAiB,EAAEN,OAAO,CAAC,CAAC;KACpD;IAEDV,iBAAiB,EAAE,CAAC;IACpB,IAAI;QACF,MAAMiB,OAAO,GAAG,MAAMC,CAAAA,GAAAA,QAAO,AAK3B,CAAA,QAL2B,CAACf,SAAS,EAAE;YACvCgB,QAAQ,IAAG;gBACT,MAAM,IAAIC,OAAiB,kBAAA,EAAE,CAAC;aAC/B;YACD,GAAGf,OAAO;SACX,CAAC,AAAC;QAEH,OAAOY,OAAO,CAAC;KAChB,QAAS;QACRhB,kBAAkB,EAAE,CAAC;KACtB;CACF;;;;;;AAvDD,MAAMoB,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,AAAsB,AAAC;AAkB3E,uFAAuF,CACvF,MAAMC,SAAS,GAA0B,EAAE,AAAC;AA4CrC,eAAe3B,YAAY,CAChCO,SAA2B,EAC3BE,OAAuB,EACL;IAClB,MAAM,EAAEmB,KAAK,CAAA,EAAE,GAAG,MAAM7B,MAAM,CAC5B;QACE8B,OAAO,EAAE,IAAI;QACb,GAAGtB,SAAS;QACZuB,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,SAAS;KAChB,EACDtB,OAAO,CACR,AAAC;IACF,OAAOmB,KAAK,WAALA,KAAK,GAAI,IAAI,CAAC;CACtB;AAGM,eAAe3B,WAAW,CAC/Ba,OAAe,EACfkB,OAAwB,EACxBvB,OAAuB,EACX;IACZ,MAAM,EAAEmB,KAAK,CAAA,EAAE,GAAG,MAAM7B,MAAM,CAC5B;QACEe,OAAO;QACPkB,OAAO;QACPF,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,QAAQ;KACf,EACDtB,OAAO,CACR,AAAC;IACF,OAAOmB,KAAK,WAALA,KAAK,GAAI,IAAI,CAAC;CACtB;AAEM,MAAMK,WAAW,GAAGlC,MAAM,AAAC;QAArBkC,WAAW,GAAXA,WAAW;AAGjB,SAAS/B,sBAAsB,CAACgC,QAA6B,EAAE;IACpEP,SAAS,CAACQ,IAAI,CAACD,QAAQ,CAAC,CAAC;CAC1B;AAEM,SAAS/B,yBAAyB,CAAC+B,QAA6B,EAAE;IACvE,MAAME,aAAa,GAAGT,SAAS,CAACU,SAAS,CAAC,CAACC,SAAS,GAAKA,SAAS,KAAKJ,QAAQ;IAAA,CAAC,AAAC;IACjFK,CAAAA,GAAAA,OAAM,AAGL,CAAA,QAHK,CACJH,aAAa,IAAI,CAAC,EAClB,4EAA4E,CAC7E,CAAC;IACFT,SAAS,CAACa,MAAM,CAACJ,aAAa,EAAE,CAAC,CAAC,CAAC;CACpC;AAGM,SAAShC,iBAAiB,CAACK,OAA0C,GAAG,EAAE,EAAE;IACjFgB,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACtC,KAAK,MAAMgB,QAAQ,IAAId,SAAS,CAAE;QAChCc,QAAQ,CAAC;YAAEC,KAAK,EAAE,IAAI;YAAE,GAAGjC,OAAO;SAAE,CAAC,CAAC;KACvC;CACF;AAGM,SAASJ,kBAAkB,CAACI,OAA0C,GAAG,EAAE,EAAE;IAClFgB,KAAK,CAAC,+BAA+B,CAAC,CAAC;IACvC,KAAK,MAAMgB,QAAQ,IAAId,SAAS,CAAE;QAChCc,QAAQ,CAAC;YAAEC,KAAK,EAAE,KAAK;YAAE,GAAGjC,OAAO;SAAE,CAAC,CAAC;KACxC;CACF;AAEM,SAASH,qBAAqB,GAAoD;IACvF,SAASqC,WAAW,CAACC,MAAc,EAAE;QACnC,OAAOA,MAAM,CAACzB,OAAO,0BAA0B,MAAM,CAAC,CAAC;KACxD;IAED,OAAO,OAAO0B,KAAU,EAAEb,OAAiB,GAAK;QAC9C,IAAI;YACF,MAAMc,KAAK,GAAG,IAAIC,MAAM,CAACJ,WAAW,CAACE,KAAK,CAAC,EAAE,GAAG,CAAC,AAAC;YAClD,OAAOb,OAAO,CAACgB,MAAM,CAAC,CAACC,MAAW,GAAKH,KAAK,CAACI,IAAI,CAACD,MAAM,CAACE,KAAK,CAAC;YAAA,CAAC,CAAC;SAClE,CAAC,OAAOC,KAAK,EAAO;YACnB3B,KAAK,CAAC,yBAAyB,EAAE2B,KAAK,CAAC,CAAC;YACxC,OAAO,EAAE,CAAC;SACX;KACF,CAAC;CACH"}