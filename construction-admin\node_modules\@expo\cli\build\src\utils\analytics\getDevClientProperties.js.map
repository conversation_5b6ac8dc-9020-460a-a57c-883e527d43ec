{"version": 3, "sources": ["../../../../src/utils/analytics/getDevClientProperties.ts"], "sourcesContent": ["import { ExpoConfig, getAccountUsername, getDefaultTarget, getPackageJson } from '@expo/config';\nimport JsonFile, { JSONValue } from '@expo/json-file';\nimport resolveFrom from 'resolve-from';\n\nimport { memoize } from '../fn';\n\nconst getAccountName = memoize((exp: Pick<ExpoConfig, 'owner'>) => {\n  return getAccountUsername(exp);\n});\n\n/** @returns true if the expo-dev-client package is found in the project `package.json` file. */\nexport function hasDirectDevClientDependency(projectRoot: string): boolean {\n  const pkg = getPackageJson(projectRoot);\n  return !!pkg.dependencies?.['expo-dev-client'] || !!pkg.devDependencies?.['expo-dev-client'];\n}\n\nconst getDevClientVersion = memoize((projectRoot: string): JSONValue | undefined => {\n  try {\n    const devClientPackage = resolveFrom.silent(projectRoot, 'expo-dev-client/package.json');\n    if (devClientPackage) {\n      return JsonFile.read(devClientPackage).version;\n    }\n  } catch {}\n  return undefined;\n});\n\nconst getProjectType = memoize((projectRoot: string): 'managed' | 'generic' => {\n  return getDefaultTarget(projectRoot) === 'managed' ? 'managed' : 'generic';\n});\n\nexport default function getDevClientProperties(projectRoot: string, exp: ExpoConfig) {\n  return {\n    account_name: getAccountName({ owner: exp.owner }),\n    dev_client_version: getDevClientVersion(projectRoot),\n    project_type: getProjectType(projectRoot),\n    uptime_ms: process.uptime() * 1000,\n  };\n}\n"], "names": ["getDevClientProperties", "hasDirectDevClientDependency", "projectRoot", "exp", "account_name", "getAccountName", "owner", "dev_client_version", "getDevClientVersion", "project_type", "getProjectType", "uptime_ms", "process", "uptime", "memoize", "getAccountUsername", "pkg", "getPackageJson", "dependencies", "devDependencies", "devClientPackage", "resolveFrom", "silent", "JsonFile", "read", "version", "undefined", "getDefaultTarget"], "mappings": "AAAA;;;;kBA8BwBA,sBAAsB;QAnB9BC,4BAA4B,GAA5BA,4BAA4B;AAXqC,IAAA,OAAc,WAAd,cAAc,CAAA;AAC3D,IAAA,SAAiB,kCAAjB,iBAAiB,EAAA;AAC7B,IAAA,YAAc,kCAAd,cAAc,EAAA;AAEd,IAAA,GAAO,WAAP,OAAO,CAAA;AA0BhB,SAASD,sBAAsB,CAACE,WAAmB,EAAEC,GAAe,EAAE;IACnF,OAAO;QACLC,YAAY,EAAEC,cAAc,CAAC;YAAEC,KAAK,EAAEH,GAAG,CAACG,KAAK;SAAE,CAAC;QAClDC,kBAAkB,EAAEC,mBAAmB,CAACN,WAAW,CAAC;QACpDO,YAAY,EAAEC,cAAc,CAACR,WAAW,CAAC;QACzCS,SAAS,EAAEC,OAAO,CAACC,MAAM,EAAE,GAAG,IAAI;KACnC,CAAC;CACH;;;;;;AA/BD,MAAMR,cAAc,GAAGS,CAAAA,GAAAA,GAAO,AAE5B,CAAA,QAF4B,CAAC,CAACX,GAA8B,GAAK;IACjE,OAAOY,CAAAA,GAAAA,OAAkB,AAAK,CAAA,mBAAL,CAACZ,GAAG,CAAC,CAAC;CAChC,CAAC,AAAC;AAGI,SAASF,4BAA4B,CAACC,WAAmB,EAAW;QAEhEc,GAAgB,EAA2BA,IAAmB;IADvE,MAAMA,GAAG,GAAGC,CAAAA,GAAAA,OAAc,AAAa,CAAA,eAAb,CAACf,WAAW,CAAC,AAAC;IACxC,OAAO,CAAC,CAACc,CAAAA,CAAAA,GAAgB,GAAhBA,GAAG,CAACE,YAAY,SAAqB,GAArCF,KAAAA,CAAqC,GAArCA,GAAgB,AAAE,CAAC,iBAAiB,CAAC,CAAA,IAAI,CAAC,CAACA,CAAAA,CAAAA,IAAmB,GAAnBA,GAAG,CAACG,eAAe,SAAqB,GAAxCH,KAAAA,CAAwC,GAAxCA,IAAmB,AAAE,CAAC,iBAAiB,CAAC,CAAA,CAAC;CAC9F;AAED,MAAMR,mBAAmB,GAAGM,CAAAA,GAAAA,GAAO,AAQjC,CAAA,QARiC,CAAC,CAACZ,WAAmB,GAA4B;IAClF,IAAI;QACF,MAAMkB,gBAAgB,GAAGC,YAAW,QAAA,CAACC,MAAM,CAACpB,WAAW,EAAE,8BAA8B,CAAC,AAAC;QACzF,IAAIkB,gBAAgB,EAAE;YACpB,OAAOG,SAAQ,QAAA,CAACC,IAAI,CAACJ,gBAAgB,CAAC,CAACK,OAAO,CAAC;SAChD;KACF,CAAC,OAAM,EAAE;IACV,OAAOC,SAAS,CAAC;CAClB,CAAC,AAAC;AAEH,MAAMhB,cAAc,GAAGI,CAAAA,GAAAA,GAAO,AAE5B,CAAA,QAF4B,CAAC,CAACZ,WAAmB,GAA4B;IAC7E,OAAOyB,CAAAA,GAAAA,OAAgB,AAAa,CAAA,iBAAb,CAACzB,WAAW,CAAC,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;CAC5E,CAAC,AAAC"}