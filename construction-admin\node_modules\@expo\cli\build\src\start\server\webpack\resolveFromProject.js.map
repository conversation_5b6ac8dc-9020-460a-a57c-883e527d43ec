{"version": 3, "sources": ["../../../../../src/start/server/webpack/resolveFromProject.ts"], "sourcesContent": ["import resolveFrom from 'resolve-from';\n\nimport { CommandError } from '../../../utils/errors';\n\n// These resolvers enable us to test the CLI in older projects.\n// We may be able to get rid of this in the future.\n// TODO: Maybe combine with AsyncResolver?\nclass WebpackImportError extends CommandError {\n  constructor(projectRoot: string, moduleId: string) {\n    super(\n      'WEBPACK_IMPORT',\n      `Missing package \"${moduleId}\" in the project. Try running the command again. (cwd: ${projectRoot})`\n    );\n  }\n}\n\nfunction resolveFromProject(projectRoot: string, moduleId: string) {\n  const resolvedPath = resolveFrom.silent(projectRoot, moduleId);\n  if (!resolvedPath) {\n    throw new WebpackImportError(projectRoot, moduleId);\n  }\n  return resolvedPath;\n}\n\nfunction importFromProject(projectRoot: string, moduleId: string) {\n  return require(resolveFromProject(projectRoot, moduleId));\n}\n\n/** Import `webpack` from the project. */\nexport function importWebpackFromProject(projectRoot: string): typeof import('webpack') {\n  return importFromProject(projectRoot, 'webpack');\n}\n\n/** Import `@expo/webpack-config` from the project. */\nexport function importExpoWebpackConfigFromProject(\n  projectRoot: string\n): typeof import('@expo/webpack-config') {\n  return importFromProject(projectRoot, '@expo/webpack-config');\n}\n\n/** Import `webpack-dev-server` from the project. */\nexport function importWebpackDevServerFromProject(\n  projectRoot: string\n): typeof import('webpack-dev-server') {\n  return importFromProject(projectRoot, 'webpack-dev-server');\n}\n"], "names": ["importWebpackFromProject", "importExpoWebpackConfigFromProject", "importWebpackDevServerFromProject", "WebpackImportError", "CommandError", "constructor", "projectRoot", "moduleId", "resolveFromProject", "<PERSON><PERSON><PERSON>", "resolveFrom", "silent", "importFromProject", "require"], "mappings": "AAAA;;;;QA6BgBA,wBAAwB,GAAxBA,wBAAwB;QAKxBC,kCAAkC,GAAlCA,kCAAkC;QAOlCC,iCAAiC,GAAjCA,iCAAiC;AAzCzB,IAAA,YAAc,kCAAd,cAAc,EAAA;AAET,IAAA,OAAuB,WAAvB,uBAAuB,CAAA;;;;;;AAEpD,+DAA+D;AAC/D,mDAAmD;AACnD,0CAA0C;AAC1C,MAAMC,kBAAkB,SAASC,OAAY,aAAA;IAC3CC,YAAYC,WAAmB,EAAEC,QAAgB,CAAE;QACjD,KAAK,CACH,gBAAgB,EAChB,CAAC,iBAAiB,EAAEA,QAAQ,CAAC,uDAAuD,EAAED,WAAW,CAAC,CAAC,CAAC,CACrG,CAAC;KACH;CACF;AAED,SAASE,kBAAkB,CAACF,WAAmB,EAAEC,QAAgB,EAAE;IACjE,MAAME,YAAY,GAAGC,YAAW,QAAA,CAACC,MAAM,CAACL,WAAW,EAAEC,QAAQ,CAAC,AAAC;IAC/D,IAAI,CAACE,YAAY,EAAE;QACjB,MAAM,IAAIN,kBAAkB,CAACG,WAAW,EAAEC,QAAQ,CAAC,CAAC;KACrD;IACD,OAAOE,YAAY,CAAC;CACrB;AAED,SAASG,iBAAiB,CAACN,WAAmB,EAAEC,QAAgB,EAAE;IAChE,OAAOM,OAAO,CAACL,kBAAkB,CAACF,WAAW,EAAEC,QAAQ,CAAC,CAAC,CAAC;CAC3D;AAGM,SAASP,wBAAwB,CAACM,WAAmB,EAA4B;IACtF,OAAOM,iBAAiB,CAACN,WAAW,EAAE,SAAS,CAAC,CAAC;CAClD;AAGM,SAASL,kCAAkC,CAChDK,WAAmB,EACoB;IACvC,OAAOM,iBAAiB,CAACN,WAAW,EAAE,sBAAsB,CAAC,CAAC;CAC/D;AAGM,SAASJ,iCAAiC,CAC/CI,WAAmB,EACkB;IACrC,OAAOM,iBAAiB,CAACN,WAAW,EAAE,oBAAoB,CAAC,CAAC;CAC7D"}