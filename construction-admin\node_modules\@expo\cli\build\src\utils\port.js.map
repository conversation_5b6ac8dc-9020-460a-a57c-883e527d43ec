{"version": 3, "sources": ["../../../src/utils/port.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport freeportAsync from 'freeport-async';\n\nimport { env } from './env';\nimport { CommandError } from './errors';\nimport * as Log from '../log';\n\n/** Get a free port or assert a CLI command error. */\nexport async function getFreePortAsync(rangeStart: number): Promise<number> {\n  const port = await freeportAsync(rangeStart, { hostnames: [null, 'localhost'] });\n  if (!port) {\n    throw new CommandError('NO_PORT_FOUND', 'No available port found');\n  }\n\n  return port;\n}\n\n/** @return `true` if the port can still be used to start the dev server, `false` if the dev server should be skipped, and asserts if the port is now taken. */\nexport async function ensurePortAvailabilityAsync(\n  projectRoot: string,\n  { port }: { port: number }\n): Promise<boolean> {\n  const freePort = await freeportAsync(port, { hostnames: [null] });\n  // Check if port has become busy during the build.\n  if (freePort === port) {\n    return true;\n  }\n\n  const isBusy = await isBusyPortRunningSameProcessAsync(projectRoot, { port });\n  if (!isBusy) {\n    throw new CommandError(\n      `Port \"${port}\" became busy running another process while the app was compiling. Re-run command to use a new port.`\n    );\n  }\n\n  // Log that the dev server will not be started and that the logs will appear in another window.\n  Log.log(\n    '› The dev server for this app is already running in another window. Logs will appear there.'\n  );\n  return false;\n}\n\nfunction isRestrictedPort(port: number) {\n  if (process.platform !== 'win32' && port < 1024) {\n    const isRoot = process.getuid && process.getuid() === 0;\n    return !isRoot;\n  }\n  return false;\n}\n\nasync function isBusyPortRunningSameProcessAsync(projectRoot: string, { port }: { port: number }) {\n  const { getRunningProcess } =\n    require('./getRunningProcess') as typeof import('./getRunningProcess');\n\n  const runningProcess = isRestrictedPort(port) ? null : getRunningProcess(port);\n  if (runningProcess) {\n    if (runningProcess.directory === projectRoot) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  return null;\n}\n\n// TODO(Bacon): Revisit after all start and run code is merged.\nexport async function choosePortAsync(\n  projectRoot: string,\n  {\n    defaultPort,\n    host,\n    reuseExistingPort,\n  }: {\n    defaultPort: number;\n    host?: string;\n    reuseExistingPort?: boolean;\n  }\n): Promise<number | null> {\n  try {\n    const port = await freeportAsync(defaultPort, { hostnames: [host ?? null] });\n    if (port === defaultPort) {\n      return port;\n    }\n\n    const isRestricted = isRestrictedPort(port);\n\n    let message = isRestricted\n      ? `Admin permissions are required to run a server on a port below 1024`\n      : `Port ${chalk.bold(defaultPort)} is`;\n\n    const { getRunningProcess } =\n      require('./getRunningProcess') as typeof import('./getRunningProcess');\n    const runningProcess = isRestricted ? null : getRunningProcess(defaultPort);\n\n    if (runningProcess) {\n      const pidTag = chalk.gray(`(pid ${runningProcess.pid})`);\n      if (runningProcess.directory === projectRoot) {\n        message += ` running this app in another window`;\n        if (reuseExistingPort) {\n          return null;\n        }\n      } else {\n        message += ` running ${chalk.cyan(runningProcess.command)} in another window`;\n      }\n      message += '\\n' + chalk.gray(`  ${runningProcess.directory} ${pidTag}`);\n    } else {\n      message += ' being used by another process';\n    }\n\n    Log.log(`\\u203A ${message}`);\n    const { confirmAsync } = require('./prompts') as typeof import('./prompts');\n    const change = await confirmAsync({\n      message: `Use port ${port} instead?`,\n      initial: true,\n    });\n    return change ? port : null;\n  } catch (error: any) {\n    if (error.code === 'ABORTED') {\n      throw error;\n    } else if (error.code === 'NON_INTERACTIVE') {\n      Log.warn(chalk.yellow(error.message));\n      return null;\n    }\n    throw error;\n  }\n}\n\n// TODO(Bacon): Revisit after all start and run code is merged.\nexport async function resolvePortAsync(\n  projectRoot: string,\n  {\n    /** Should opt to reuse a port that is running the same project in another window. */\n    reuseExistingPort,\n    /** Preferred port. */\n    defaultPort,\n    /** Backup port for when the default isn't available. */\n    fallbackPort,\n  }: {\n    reuseExistingPort?: boolean;\n    defaultPort?: string | number;\n    fallbackPort?: number;\n  } = {}\n): Promise<number | null> {\n  let port: number;\n  if (typeof defaultPort === 'string') {\n    port = parseInt(defaultPort, 10);\n  } else if (typeof defaultPort === 'number') {\n    port = defaultPort;\n  } else {\n    port = env.RCT_METRO_PORT || fallbackPort || 8081;\n  }\n\n  // Only check the port when the bundler is running.\n  const resolvedPort = await choosePortAsync(projectRoot, {\n    defaultPort: port,\n    reuseExistingPort,\n  });\n  if (resolvedPort == null) {\n    Log.log('\\u203A Skipping dev server');\n    // Skip bundling if the port is null\n  } else {\n    // Use the new or resolved port\n    process.env.RCT_METRO_PORT = String(resolvedPort);\n  }\n\n  return resolvedPort;\n}\n"], "names": ["getFreePortAsync", "ensurePortAvailabilityAsync", "choosePortAsync", "resolvePortAsync", "Log", "rangeStart", "port", "freeportAsync", "hostnames", "CommandError", "projectRoot", "freePort", "isBusy", "isBusyPortRunningSameProcessAsync", "log", "isRestrictedPort", "process", "platform", "isRoot", "getuid", "getRunningProcess", "require", "runningProcess", "directory", "defaultPort", "host", "reuseExistingPort", "isRestricted", "message", "chalk", "bold", "pidTag", "gray", "pid", "cyan", "command", "<PERSON><PERSON><PERSON>", "change", "initial", "error", "code", "warn", "yellow", "fallback<PERSON>ort", "parseInt", "env", "RCT_METRO_PORT", "resolvedPort", "String"], "mappings": "AAAA;;;;QAQsBA,gBAAgB,GAAhBA,gBAAgB;QAUhBC,2BAA2B,GAA3BA,2BAA2B;QAiD3BC,eAAe,GAAfA,eAAe;QA8DfC,gBAAgB,GAAhBA,gBAAgB;AAjIpB,IAAA,MAAO,kCAAP,OAAO,EAAA;AACC,IAAA,cAAgB,kCAAhB,gBAAgB,EAAA;AAEtB,IAAA,IAAO,WAAP,OAAO,CAAA;AACE,IAAA,OAAU,WAAV,UAAU,CAAA;AAC3BC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGR,eAAeJ,gBAAgB,CAACK,UAAkB,EAAmB;IAC1E,MAAMC,IAAI,GAAG,MAAMC,CAAAA,GAAAA,cAAa,AAAgD,CAAA,QAAhD,CAACF,UAAU,EAAE;QAAEG,SAAS,EAAE;YAAC,IAAI;YAAE,WAAW;SAAC;KAAE,CAAC,AAAC;IACjF,IAAI,CAACF,IAAI,EAAE;QACT,MAAM,IAAIG,OAAY,aAAA,CAAC,eAAe,EAAE,yBAAyB,CAAC,CAAC;KACpE;IAED,OAAOH,IAAI,CAAC;CACb;AAGM,eAAeL,2BAA2B,CAC/CS,WAAmB,EACnB,EAAEJ,IAAI,CAAA,EAAoB,EACR;IAClB,MAAMK,QAAQ,GAAG,MAAMJ,CAAAA,GAAAA,cAAa,AAA6B,CAAA,QAA7B,CAACD,IAAI,EAAE;QAAEE,SAAS,EAAE;YAAC,IAAI;SAAC;KAAE,CAAC,AAAC;IAClE,kDAAkD;IAClD,IAAIG,QAAQ,KAAKL,IAAI,EAAE;QACrB,OAAO,IAAI,CAAC;KACb;IAED,MAAMM,MAAM,GAAG,MAAMC,iCAAiC,CAACH,WAAW,EAAE;QAAEJ,IAAI;KAAE,CAAC,AAAC;IAC9E,IAAI,CAACM,MAAM,EAAE;QACX,MAAM,IAAIH,OAAY,aAAA,CACpB,CAAC,MAAM,EAAEH,IAAI,CAAC,oGAAoG,CAAC,CACpH,CAAC;KACH;IAED,+FAA+F;IAC/FF,GAAG,CAACU,GAAG,CACL,kGAA6F,CAC9F,CAAC;IACF,OAAO,KAAK,CAAC;CACd;AAED,SAASC,gBAAgB,CAACT,IAAY,EAAE;IACtC,IAAIU,OAAO,CAACC,QAAQ,KAAK,OAAO,IAAIX,IAAI,GAAG,IAAI,EAAE;QAC/C,MAAMY,MAAM,GAAGF,OAAO,CAACG,MAAM,IAAIH,OAAO,CAACG,MAAM,EAAE,KAAK,CAAC,AAAC;QACxD,OAAO,CAACD,MAAM,CAAC;KAChB;IACD,OAAO,KAAK,CAAC;CACd;AAED,eAAeL,iCAAiC,CAACH,WAAmB,EAAE,EAAEJ,IAAI,CAAA,EAAoB,EAAE;IAChG,MAAM,EAAEc,iBAAiB,CAAA,EAAE,GACzBC,OAAO,CAAC,qBAAqB,CAAC,AAAwC,AAAC;IAEzE,MAAMC,cAAc,GAAGP,gBAAgB,CAACT,IAAI,CAAC,GAAG,IAAI,GAAGc,iBAAiB,CAACd,IAAI,CAAC,AAAC;IAC/E,IAAIgB,cAAc,EAAE;QAClB,IAAIA,cAAc,CAACC,SAAS,KAAKb,WAAW,EAAE;YAC5C,OAAO,IAAI,CAAC;SACb,MAAM;YACL,OAAO,KAAK,CAAC;SACd;KACF;IAED,OAAO,IAAI,CAAC;CACb;AAGM,eAAeR,eAAe,CACnCQ,WAAmB,EACnB,EACEc,WAAW,CAAA,EACXC,IAAI,CAAA,EACJC,iBAAiB,CAAA,EAKlB,EACuB;IACxB,IAAI;QACF,MAAMpB,IAAI,GAAG,MAAMC,CAAAA,GAAAA,cAAa,AAA4C,CAAA,QAA5C,CAACiB,WAAW,EAAE;YAAEhB,SAAS,EAAE;gBAACiB,IAAI,WAAJA,IAAI,GAAI,IAAI;aAAC;SAAE,CAAC,AAAC;QAC7E,IAAInB,IAAI,KAAKkB,WAAW,EAAE;YACxB,OAAOlB,IAAI,CAAC;SACb;QAED,MAAMqB,YAAY,GAAGZ,gBAAgB,CAACT,IAAI,CAAC,AAAC;QAE5C,IAAIsB,OAAO,GAAGD,YAAY,GACtB,CAAC,mEAAmE,CAAC,GACrE,CAAC,KAAK,EAAEE,MAAK,QAAA,CAACC,IAAI,CAACN,WAAW,CAAC,CAAC,GAAG,CAAC,AAAC;QAEzC,MAAM,EAAEJ,iBAAiB,CAAA,EAAE,GACzBC,OAAO,CAAC,qBAAqB,CAAC,AAAwC,AAAC;QACzE,MAAMC,cAAc,GAAGK,YAAY,GAAG,IAAI,GAAGP,iBAAiB,CAACI,WAAW,CAAC,AAAC;QAE5E,IAAIF,cAAc,EAAE;YAClB,MAAMS,MAAM,GAAGF,MAAK,QAAA,CAACG,IAAI,CAAC,CAAC,KAAK,EAAEV,cAAc,CAACW,GAAG,CAAC,CAAC,CAAC,CAAC,AAAC;YACzD,IAAIX,cAAc,CAACC,SAAS,KAAKb,WAAW,EAAE;gBAC5CkB,OAAO,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBACjD,IAAIF,iBAAiB,EAAE;oBACrB,OAAO,IAAI,CAAC;iBACb;aACF,MAAM;gBACLE,OAAO,IAAI,CAAC,SAAS,EAAEC,MAAK,QAAA,CAACK,IAAI,CAACZ,cAAc,CAACa,OAAO,CAAC,CAAC,kBAAkB,CAAC,CAAC;aAC/E;YACDP,OAAO,IAAI,IAAI,GAAGC,MAAK,QAAA,CAACG,IAAI,CAAC,CAAC,EAAE,EAAEV,cAAc,CAACC,SAAS,CAAC,CAAC,EAAEQ,MAAM,CAAC,CAAC,CAAC,CAAC;SACzE,MAAM;YACLH,OAAO,IAAI,gCAAgC,CAAC;SAC7C;QAEDxB,GAAG,CAACU,GAAG,CAAC,CAAC,OAAO,EAAEc,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7B,MAAM,EAAEQ,YAAY,CAAA,EAAE,GAAGf,OAAO,CAAC,WAAW,CAAC,AAA8B,AAAC;QAC5E,MAAMgB,MAAM,GAAG,MAAMD,YAAY,CAAC;YAChCR,OAAO,EAAE,CAAC,SAAS,EAAEtB,IAAI,CAAC,SAAS,CAAC;YACpCgC,OAAO,EAAE,IAAI;SACd,CAAC,AAAC;QACH,OAAOD,MAAM,GAAG/B,IAAI,GAAG,IAAI,CAAC;KAC7B,CAAC,OAAOiC,KAAK,EAAO;QACnB,IAAIA,KAAK,CAACC,IAAI,KAAK,SAAS,EAAE;YAC5B,MAAMD,KAAK,CAAC;SACb,MAAM,IAAIA,KAAK,CAACC,IAAI,KAAK,iBAAiB,EAAE;YAC3CpC,GAAG,CAACqC,IAAI,CAACZ,MAAK,QAAA,CAACa,MAAM,CAACH,KAAK,CAACX,OAAO,CAAC,CAAC,CAAC;YACtC,OAAO,IAAI,CAAC;SACb;QACD,MAAMW,KAAK,CAAC;KACb;CACF;AAGM,eAAepC,gBAAgB,CACpCO,WAAmB,EACnB,EACE,qFAAqF,CACrFgB,iBAAiB,CAAA,EACjB,sBAAsB,CACtBF,WAAW,CAAA,EACX,wDAAwD,CACxDmB,YAAY,CAAA,EAKb,GAAG,EAAE,EACkB;IACxB,IAAIrC,IAAI,AAAQ,AAAC;IACjB,IAAI,OAAOkB,WAAW,KAAK,QAAQ,EAAE;QACnClB,IAAI,GAAGsC,QAAQ,CAACpB,WAAW,EAAE,EAAE,CAAC,CAAC;KAClC,MAAM,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;QAC1ClB,IAAI,GAAGkB,WAAW,CAAC;KACpB,MAAM;QACLlB,IAAI,GAAGuC,IAAG,IAAA,CAACC,cAAc,IAAIH,YAAY,IAAI,IAAI,CAAC;KACnD;IAED,mDAAmD;IACnD,MAAMI,YAAY,GAAG,MAAM7C,eAAe,CAACQ,WAAW,EAAE;QACtDc,WAAW,EAAElB,IAAI;QACjBoB,iBAAiB;KAClB,CAAC,AAAC;IACH,IAAIqB,YAAY,IAAI,IAAI,EAAE;QACxB3C,GAAG,CAACU,GAAG,CAAC,4BAA4B,CAAC,CAAC;IACtC,oCAAoC;KACrC,MAAM;QACL,+BAA+B;QAC/BE,OAAO,CAAC6B,GAAG,CAACC,cAAc,GAAGE,MAAM,CAACD,YAAY,CAAC,CAAC;KACnD;IAED,OAAOA,YAAY,CAAC;CACrB"}