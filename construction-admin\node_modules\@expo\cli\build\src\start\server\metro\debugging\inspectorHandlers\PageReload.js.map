{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/inspectorHandlers/PageReload.ts"], "sourcesContent": ["import type { Protocol } from 'devtools-protocol';\n\nimport { CdpMessage, DebuggerMetadata, DebuggerRequest, InspectorHandler } from './types';\nimport { MetroBundlerDevServer } from '../../MetroBundlerDevServer';\n\nexport class PageReloadHandler implements InspectorHandler {\n  constructor(private readonly metroBundler: MetroBundlerDevServer) {}\n\n  onDebuggerMessage(message: DebuggerRequest<PageReload>, { socket }: DebuggerMetadata) {\n    if (message.method === 'Page.reload') {\n      this.metroBundler.broadcastMessage('reload');\n      socket.send(JSON.stringify({ id: message.id }));\n      return true;\n    }\n\n    return false;\n  }\n}\n\n/** @see https://chromedevtools.github.io/devtools-protocol/1-2/Page/#method-reload */\nexport type PageReload = CdpMessage<'Page.reload', Protocol.Page.ReloadRequest, never>;\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "metroBundler", "onDebuggerMessage", "message", "socket", "method", "broadcastMessage", "send", "JSON", "stringify", "id"], "mappings": "AAAA;;;;AAKO,MAAMA,iBAAiB;IAC5BC,YAA6BC,YAAmC,CAAE;aAArCA,YAAmC,GAAnCA,YAAmC;KAAI;IAEpEC,iBAAiB,CAACC,OAAoC,EAAE,EAAEC,MAAM,CAAA,EAAoB,EAAE;QACpF,IAAID,OAAO,CAACE,MAAM,KAAK,aAAa,EAAE;YACpC,IAAI,CAACJ,YAAY,CAACK,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAC7CF,MAAM,CAACG,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;gBAAEC,EAAE,EAAEP,OAAO,CAACO,EAAE;aAAE,CAAC,CAAC,CAAC;YAChD,OAAO,IAAI,CAAC;SACb;QAED,OAAO,KAAK,CAAC;KACd;CACF;QAZYX,iBAAiB,GAAjBA,iBAAiB"}