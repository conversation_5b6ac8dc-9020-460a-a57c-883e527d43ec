{"version": 3, "sources": ["../../../src/utils/errors.ts"], "sourcesContent": ["import { AssertionError } from 'assert';\nimport chalk from 'chalk';\n\nimport { exit } from '../log';\n\nconst ERROR_PREFIX = 'Error: ';\n\n/**\n * General error, formatted as a message in red text when caught by expo-cli (no stack trace is printed). Should be used in favor of `log.error()` in most cases.\n */\nexport class CommandError extends Error {\n  name = 'CommandError';\n  readonly isCommandError = true;\n\n  constructor(\n    public code: string,\n    message: string = ''\n  ) {\n    super('');\n    // If e.toString() was called to get `message` we don't want it to look\n    // like \"Error: Error:\".\n    if (message.startsWith(ERROR_PREFIX)) {\n      message = message.substring(ERROR_PREFIX.length);\n    }\n\n    this.message = message || code;\n  }\n}\n\nexport class AbortCommandError extends CommandError {\n  constructor() {\n    super('ABORTED', 'Interactive prompt was cancelled.');\n  }\n}\n\n/**\n * Used to end a CLI process without printing a stack trace in the Expo CLI. Should be used in favor of `process.exit`.\n */\nexport class SilentError extends CommandError {\n  constructor(messageOrError?: string | Error) {\n    const message =\n      (typeof messageOrError === 'string' ? messageOrError : messageOrError?.message) ??\n      'This error should fail silently in the CLI';\n    super('SILENT', message);\n    if (typeof messageOrError !== 'string') {\n      // forward the props of the incoming error for tests or processes outside of expo-cli that use expo cli internals.\n      this.stack = messageOrError?.stack ?? this.stack;\n      this.name = messageOrError?.name ?? this.name;\n    }\n  }\n}\n\nexport function logCmdError(error: Error): never {\n  if (error instanceof AbortCommandError || error instanceof SilentError) {\n    // Do nothing, this is used for prompts or other cases that were custom logged.\n    process.exit(0);\n  } else if (\n    error instanceof CommandError ||\n    error instanceof AssertionError ||\n    error.name === 'ApiV2Error' ||\n    error.name === 'ConfigError'\n  ) {\n    // Print the stack trace in debug mode only.\n    exit(error);\n  }\n\n  const errorDetails = error.stack ? '\\n' + chalk.gray(error.stack) : '';\n\n  exit(chalk.red(error.toString()) + errorDetails);\n}\n\n/** This should never be thrown in production. */\nexport class UnimplementedError extends Error {\n  constructor() {\n    super('Unimplemented');\n    this.name = 'UnimplementedError';\n  }\n}\n"], "names": ["logCmdError", "ERROR_PREFIX", "CommandError", "Error", "constructor", "code", "message", "name", "isCommandError", "startsWith", "substring", "length", "AbortCommandError", "SilentError", "messageOrError", "stack", "error", "process", "exit", "AssertionError", "errorDetails", "chalk", "gray", "red", "toString", "UnimplementedError"], "mappings": "AAAA;;;;QAoDgBA,WAAW,GAAXA,WAAW;AApDI,IAAA,OAAQ,WAAR,QAAQ,CAAA;AACrB,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEJ,IAAA,IAAQ,WAAR,QAAQ,CAAA;;;;;;AAE7B,MAAMC,YAAY,GAAG,SAAS,AAAC;AAKxB,MAAMC,YAAY,SAASC,KAAK;IAIrCC,YACSC,IAAY,EACnBC,OAAe,GAAG,EAAE,CACpB;QACA,KAAK,CAAC,EAAE,CAAC,CAAC;aAHHD,IAAY,GAAZA,IAAY;aAJrBE,IAAI,GAAG,cAAc;aACZC,cAAc,GAAG,IAAI;QAO5B,uEAAuE;QACvE,wBAAwB;QACxB,IAAIF,OAAO,CAACG,UAAU,CAACR,YAAY,CAAC,EAAE;YACpCK,OAAO,GAAGA,OAAO,CAACI,SAAS,CAACT,YAAY,CAACU,MAAM,CAAC,CAAC;SAClD;QAED,IAAI,CAACL,OAAO,GAAGA,OAAO,IAAID,IAAI,CAAC;KAChC;CACF;QAjBYH,YAAY,GAAZA,YAAY;AAmBlB,MAAMU,iBAAiB,SAASV,YAAY;IACjDE,aAAc;QACZ,KAAK,CAAC,SAAS,EAAE,mCAAmC,CAAC,CAAC;KACvD;CACF;QAJYQ,iBAAiB,GAAjBA,iBAAiB;AASvB,MAAMC,WAAW,SAASX,YAAY;IAC3CE,YAAYU,cAA+B,CAAE;YAEzC,GAA+E;QADjF,MAAMR,OAAO,GACX,CAAA,GAA+E,GAA9E,OAAOQ,cAAc,KAAK,QAAQ,GAAGA,cAAc,GAAGA,cAAc,QAAS,GAAvBA,KAAAA,CAAuB,GAAvBA,cAAc,CAAER,OAAO,YAA9E,GAA+E,GAC/E,4CAA4C,AAAC;QAC/C,KAAK,CAAC,QAAQ,EAAEA,OAAO,CAAC,CAAC;QACzB,IAAI,OAAOQ,cAAc,KAAK,QAAQ,EAAE;gBAEzBA,IAAqB;YADlC,kHAAkH;YAClH,IAAI,CAACC,KAAK,GAAGD,CAAAA,IAAqB,GAArBA,cAAc,QAAO,GAArBA,KAAAA,CAAqB,GAArBA,cAAc,CAAEC,KAAK,YAArBD,IAAqB,GAAI,IAAI,CAACC,KAAK,CAAC;gBACrCD,IAAoB;YAAhC,IAAI,CAACP,IAAI,GAAGO,CAAAA,IAAoB,GAApBA,cAAc,QAAM,GAApBA,KAAAA,CAAoB,GAApBA,cAAc,CAAEP,IAAI,YAApBO,IAAoB,GAAI,IAAI,CAACP,IAAI,CAAC;SAC/C;KACF;CACF;QAZYM,WAAW,GAAXA,WAAW;AAcjB,SAASb,WAAW,CAACgB,KAAY,EAAS;IAC/C,IAAIA,KAAK,YAAYJ,iBAAiB,IAAII,KAAK,YAAYH,WAAW,EAAE;QACtE,+EAA+E;QAC/EI,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB,MAAM,IACLF,KAAK,YAAYd,YAAY,IAC7Bc,KAAK,YAAYG,OAAc,eAAA,IAC/BH,KAAK,CAACT,IAAI,KAAK,YAAY,IAC3BS,KAAK,CAACT,IAAI,KAAK,aAAa,EAC5B;QACA,4CAA4C;QAC5CW,CAAAA,GAAAA,IAAI,AAAO,CAAA,KAAP,CAACF,KAAK,CAAC,CAAC;KACb;IAED,MAAMI,YAAY,GAAGJ,KAAK,CAACD,KAAK,GAAG,IAAI,GAAGM,MAAK,QAAA,CAACC,IAAI,CAACN,KAAK,CAACD,KAAK,CAAC,GAAG,EAAE,AAAC;IAEvEG,CAAAA,GAAAA,IAAI,AAA4C,CAAA,KAA5C,CAACG,MAAK,QAAA,CAACE,GAAG,CAACP,KAAK,CAACQ,QAAQ,EAAE,CAAC,GAAGJ,YAAY,CAAC,CAAC;CAClD;AAGM,MAAMK,kBAAkB,SAAStB,KAAK;IAC3CC,aAAc;QACZ,KAAK,CAAC,eAAe,CAAC,CAAC;QACvB,IAAI,CAACG,IAAI,GAAG,oBAAoB,CAAC;KAClC;CACF;QALYkB,kBAAkB,GAAlBA,kBAAkB"}