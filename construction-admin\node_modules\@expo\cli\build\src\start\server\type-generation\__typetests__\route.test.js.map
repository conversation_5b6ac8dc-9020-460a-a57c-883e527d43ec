{"version": 3, "sources": ["../../../../../../src/start/server/type-generation/__typetests__/route.test.ts"], "sourcesContent": ["import { expectType, expectError } from 'tsd-lite';\n\nimport {\n  useGlobalSearchParams,\n  useSegments,\n  useRouter,\n  useSearchParams,\n  useLocalSearchParams,\n} from './fixtures/basic';\n\n// eslint-disable-next-line react-hooks/rules-of-hooks\nconst router = useRouter();\n\ndescribe('router.push()', () => {\n  // router.push will return void when the type matches, otherwise it should error\n\n  describe('href', () => {\n    it('will error on non-urls', () => {\n      expectError(router.push('should-error'));\n    });\n\n    it('can accept an absolute url', () => {\n      expectType<void>(router.push('/apple'));\n      expectType<void>(router.push('/banana'));\n    });\n\n    it('can accept a ANY relative url', () => {\n      // We only type-check absolute urls\n      expectType<void>(router.push('./this/work/but/is/not/valid'));\n    });\n\n    it('works for dynamic urls', () => {\n      expectType<void>(router.push('/colors/blue'));\n    });\n\n    it('works for CatchAll routes', () => {\n      expectType<void>(router.push('/animals/bear'));\n      expectType<void>(router.push('/animals/bear/cat/dog'));\n      expectType<void>(router.push('/mix/apple/blue/cat/dog'));\n    });\n\n    it.skip('works for optional CatchAll routes', () => {\n      // CatchAll routes are not currently optional\n      // expectType<void>(router.push('/animals/'));\n    });\n\n    it('will error when providing extra parameters', () => {\n      expectError(router.push('/colors/blue/test'));\n    });\n\n    it('will error when providing too few parameters', () => {\n      expectError(router.push('/mix/apple'));\n      expectError(router.push('/mix/apple/cat'));\n    });\n  });\n\n  describe('HrefObject', () => {\n    it('will error on non-urls', () => {\n      expectError(router.push({ pathname: 'should-error' }));\n    });\n\n    it('can accept an absolute url', () => {\n      expectType<void>(router.push({ pathname: '/apple' }));\n      expectType<void>(router.push({ pathname: '/banana' }));\n    });\n\n    it('can accept a ANY relative url', () => {\n      // We only type-check absolute urls\n      expectType<void>(router.push({ pathname: './this/work/but/is/not/valid' }));\n    });\n\n    it('works for dynamic urls', () => {\n      expectType<void>(\n        router.push({\n          pathname: '/colors/[color]',\n          params: { color: 'blue' },\n        })\n      );\n    });\n\n    it('requires a valid pathname', () => {\n      expectError(\n        router.push({\n          pathname: '/colors/[invalid]',\n          params: { color: 'blue' },\n        })\n      );\n    });\n\n    it('requires a valid param', () => {\n      expectError(\n        router.push({\n          pathname: '/colors/[color]',\n          params: { invalid: 'blue' },\n        })\n      );\n    });\n\n    it('works for catch all routes', () => {\n      expectType<void>(\n        router.push({\n          pathname: '/animals/[...animal]',\n          params: { animal: ['cat', 'dog'] },\n        })\n      );\n    });\n\n    it('allows numeric inputs', () => {\n      expectType<void>(\n        router.push({\n          pathname: '/mix/[fruit]/[color]/[...animals]',\n          params: { color: 1, fruit: 'apple', animals: [2, 'cat'] },\n        })\n      );\n    });\n\n    it('requires an array for catch all routes', () => {\n      expectError(\n        router.push({\n          pathname: '/animals/[...animal]',\n          params: { animal: 'cat' },\n        })\n      );\n    });\n\n    it('works for mixed routes', () => {\n      expectType<void>(\n        router.push({\n          pathname: '/mix/[fruit]/[color]/[...animals]',\n          params: { color: 'red', fruit: 'apple', animals: [] },\n        })\n      );\n    });\n\n    it('requires all params in mixed routes', () => {\n      expectError(\n        router.push({\n          pathname: '/mix/[fruit]/[color]/[...animals]',\n          params: { color: 'red', animals: ['cat', 'dog'] },\n        })\n      );\n    });\n  });\n});\n\ndescribe('useSearchParams', () => {\n  expectType<Record<'color', string>>(useSearchParams<Record<'color', string>>());\n  expectType<Record<'color', string> & Record<string, string | string[]>>(\n    useSearchParams<'/colors/[color]'>()\n  );\n\n  expectError(useSearchParams<'/invalid'>());\n  expectError(useSearchParams<Record<'custom', Function>>());\n});\n\ndescribe('useLocalSearchParams', () => {\n  expectType<Record<'color', string>>(useLocalSearchParams<Record<'color', string>>());\n  expectType<Record<'color', string> & Record<string, string | string[]>>(\n    useLocalSearchParams<'/colors/[color]'>()\n  );\n\n  expectError(useSearchParams<'/invalid'>());\n  expectError(useSearchParams<Record<'custom', Function>>());\n});\n\ndescribe('useGlobalSearchParams', () => {\n  expectType<Record<'color', string>>(useGlobalSearchParams<Record<'color', string>>());\n  expectType<Record<'color', string> & Record<string, string | string[]>>(\n    useGlobalSearchParams<'/colors/[color]'>()\n  );\n\n  expectError(useGlobalSearchParams<'/invalid'>());\n  expectError(useGlobalSearchParams<Record<'custom', Function>>());\n});\n\ndescribe('useSegments', () => {\n  it('can accept an absolute url', () => {\n    expectType<['apple']>(useSegments<'/apple'>());\n  });\n\n  it('only accepts valid possible urls', () => {\n    expectError(useSegments<'/invalid'>());\n  });\n\n  it('can accept an array of segments', () => {\n    expectType<['apple']>(useSegments<['apple']>());\n  });\n\n  it('only accepts valid possible segments', () => {\n    expectError(useSegments<['invalid segment']>());\n  });\n});\n\ndescribe('external routes', () => {\n  it('can accept any external url', () => {\n    expectType<void>(router.push('http://expo.dev'));\n  });\n\n  it('can accept any schema url', () => {\n    expectType<void>(router.push('custom-schema://expo.dev'));\n  });\n\n  it('can accept mailto url', () => {\n    expectType<void>(router.push('mailto:<EMAIL>'));\n  });\n});\n"], "names": ["router", "useRouter", "describe", "it", "expectError", "push", "expectType", "skip", "pathname", "params", "color", "invalid", "animal", "fruit", "animals", "useSearchParams", "useLocalSearchParams", "useGlobalSearchParams", "useSegments"], "mappings": "AAAA;AAAwC,IAAA,QAAU,WAAV,UAAU,CAAA;AAQ3C,IAAA,MAAkB,WAAlB,kBAAkB,CAAA;AAEzB,sDAAsD;AACtD,MAAMA,MAAM,GAAGC,CAAAA,GAAAA,MAAS,AAAE,CAAA,UAAF,EAAE,AAAC;AAE3BC,QAAQ,CAAC,eAAe,EAAE,IAAM;IAC9B,gFAAgF;IAEhFA,QAAQ,CAAC,MAAM,EAAE,IAAM;QACrBC,EAAE,CAAC,wBAAwB,EAAE,IAAM;YACjCC,CAAAA,GAAAA,QAAW,AAA6B,CAAA,YAA7B,CAACJ,MAAM,CAACK,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;SAC1C,CAAC,CAAC;QAEHF,EAAE,CAAC,4BAA4B,EAAE,IAAM;YACrCG,CAAAA,GAAAA,QAAU,AAA6B,CAAA,WAA7B,CAAON,MAAM,CAACK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YACxCC,CAAAA,GAAAA,QAAU,AAA8B,CAAA,WAA9B,CAAON,MAAM,CAACK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;SAC1C,CAAC,CAAC;QAEHF,EAAE,CAAC,+BAA+B,EAAE,IAAM;YACxC,mCAAmC;YACnCG,CAAAA,GAAAA,QAAU,AAAmD,CAAA,WAAnD,CAAON,MAAM,CAACK,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC;SAC/D,CAAC,CAAC;QAEHF,EAAE,CAAC,wBAAwB,EAAE,IAAM;YACjCG,CAAAA,GAAAA,QAAU,AAAmC,CAAA,WAAnC,CAAON,MAAM,CAACK,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;SAC/C,CAAC,CAAC;QAEHF,EAAE,CAAC,2BAA2B,EAAE,IAAM;YACpCG,CAAAA,GAAAA,QAAU,AAAoC,CAAA,WAApC,CAAON,MAAM,CAACK,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAC/CC,CAAAA,GAAAA,QAAU,AAA4C,CAAA,WAA5C,CAAON,MAAM,CAACK,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC;YACvDC,CAAAA,GAAAA,QAAU,AAA8C,CAAA,WAA9C,CAAON,MAAM,CAACK,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC;SAC1D,CAAC,CAAC;QAEHF,EAAE,CAACI,IAAI,CAAC,oCAAoC,EAAE,IAAM;QAClD,6CAA6C;QAC7C,8CAA8C;SAC/C,CAAC,CAAC;QAEHJ,EAAE,CAAC,4CAA4C,EAAE,IAAM;YACrDC,CAAAA,GAAAA,QAAW,AAAkC,CAAA,YAAlC,CAACJ,MAAM,CAACK,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;SAC/C,CAAC,CAAC;QAEHF,EAAE,CAAC,8CAA8C,EAAE,IAAM;YACvDC,CAAAA,GAAAA,QAAW,AAA2B,CAAA,YAA3B,CAACJ,MAAM,CAACK,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YACvCD,CAAAA,GAAAA,QAAW,AAA+B,CAAA,YAA/B,CAACJ,MAAM,CAACK,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;SAC5C,CAAC,CAAC;KACJ,CAAC,CAAC;IAEHH,QAAQ,CAAC,YAAY,EAAE,IAAM;QAC3BC,EAAE,CAAC,wBAAwB,EAAE,IAAM;YACjCC,CAAAA,GAAAA,QAAW,AAA2C,CAAA,YAA3C,CAACJ,MAAM,CAACK,IAAI,CAAC;gBAAEG,QAAQ,EAAE,cAAc;aAAE,CAAC,CAAC,CAAC;SACxD,CAAC,CAAC;QAEHL,EAAE,CAAC,4BAA4B,EAAE,IAAM;YACrCG,CAAAA,GAAAA,QAAU,AAA2C,CAAA,WAA3C,CAAON,MAAM,CAACK,IAAI,CAAC;gBAAEG,QAAQ,EAAE,QAAQ;aAAE,CAAC,CAAC,CAAC;YACtDF,CAAAA,GAAAA,QAAU,AAA4C,CAAA,WAA5C,CAAON,MAAM,CAACK,IAAI,CAAC;gBAAEG,QAAQ,EAAE,SAAS;aAAE,CAAC,CAAC,CAAC;SACxD,CAAC,CAAC;QAEHL,EAAE,CAAC,+BAA+B,EAAE,IAAM;YACxC,mCAAmC;YACnCG,CAAAA,GAAAA,QAAU,AAAiE,CAAA,WAAjE,CAAON,MAAM,CAACK,IAAI,CAAC;gBAAEG,QAAQ,EAAE,8BAA8B;aAAE,CAAC,CAAC,CAAC;SAC7E,CAAC,CAAC;QAEHL,EAAE,CAAC,wBAAwB,EAAE,IAAM;YACjCG,CAAAA,GAAAA,QAAU,AAKT,CAAA,WALS,CACRN,MAAM,CAACK,IAAI,CAAC;gBACVG,QAAQ,EAAE,iBAAiB;gBAC3BC,MAAM,EAAE;oBAAEC,KAAK,EAAE,MAAM;iBAAE;aAC1B,CAAC,CACH,CAAC;SACH,CAAC,CAAC;QAEHP,EAAE,CAAC,2BAA2B,EAAE,IAAM;YACpCC,CAAAA,GAAAA,QAAW,AAKV,CAAA,YALU,CACTJ,MAAM,CAACK,IAAI,CAAC;gBACVG,QAAQ,EAAE,mBAAmB;gBAC7BC,MAAM,EAAE;oBAAEC,KAAK,EAAE,MAAM;iBAAE;aAC1B,CAAC,CACH,CAAC;SACH,CAAC,CAAC;QAEHP,EAAE,CAAC,wBAAwB,EAAE,IAAM;YACjCC,CAAAA,GAAAA,QAAW,AAKV,CAAA,YALU,CACTJ,MAAM,CAACK,IAAI,CAAC;gBACVG,QAAQ,EAAE,iBAAiB;gBAC3BC,MAAM,EAAE;oBAAEE,OAAO,EAAE,MAAM;iBAAE;aAC5B,CAAC,CACH,CAAC;SACH,CAAC,CAAC;QAEHR,EAAE,CAAC,4BAA4B,EAAE,IAAM;YACrCG,CAAAA,GAAAA,QAAU,AAKT,CAAA,WALS,CACRN,MAAM,CAACK,IAAI,CAAC;gBACVG,QAAQ,EAAE,sBAAsB;gBAChCC,MAAM,EAAE;oBAAEG,MAAM,EAAE;wBAAC,KAAK;wBAAE,KAAK;qBAAC;iBAAE;aACnC,CAAC,CACH,CAAC;SACH,CAAC,CAAC;QAEHT,EAAE,CAAC,uBAAuB,EAAE,IAAM;YAChCG,CAAAA,GAAAA,QAAU,AAKT,CAAA,WALS,CACRN,MAAM,CAACK,IAAI,CAAC;gBACVG,QAAQ,EAAE,mCAAmC;gBAC7CC,MAAM,EAAE;oBAAEC,KAAK,EAAE,CAAC;oBAAEG,KAAK,EAAE,OAAO;oBAAEC,OAAO,EAAE;AAAC,yBAAC;wBAAE,KAAK;qBAAC;iBAAE;aAC1D,CAAC,CACH,CAAC;SACH,CAAC,CAAC;QAEHX,EAAE,CAAC,wCAAwC,EAAE,IAAM;YACjDC,CAAAA,GAAAA,QAAW,AAKV,CAAA,YALU,CACTJ,MAAM,CAACK,IAAI,CAAC;gBACVG,QAAQ,EAAE,sBAAsB;gBAChCC,MAAM,EAAE;oBAAEG,MAAM,EAAE,KAAK;iBAAE;aAC1B,CAAC,CACH,CAAC;SACH,CAAC,CAAC;QAEHT,EAAE,CAAC,wBAAwB,EAAE,IAAM;YACjCG,CAAAA,GAAAA,QAAU,AAKT,CAAA,WALS,CACRN,MAAM,CAACK,IAAI,CAAC;gBACVG,QAAQ,EAAE,mCAAmC;gBAC7CC,MAAM,EAAE;oBAAEC,KAAK,EAAE,KAAK;oBAAEG,KAAK,EAAE,OAAO;oBAAEC,OAAO,EAAE,EAAE;iBAAE;aACtD,CAAC,CACH,CAAC;SACH,CAAC,CAAC;QAEHX,EAAE,CAAC,qCAAqC,EAAE,IAAM;YAC9CC,CAAAA,GAAAA,QAAW,AAKV,CAAA,YALU,CACTJ,MAAM,CAACK,IAAI,CAAC;gBACVG,QAAQ,EAAE,mCAAmC;gBAC7CC,MAAM,EAAE;oBAAEC,KAAK,EAAE,KAAK;oBAAEI,OAAO,EAAE;wBAAC,KAAK;wBAAE,KAAK;qBAAC;iBAAE;aAClD,CAAC,CACH,CAAC;SACH,CAAC,CAAC;KACJ,CAAC,CAAC;CACJ,CAAC,CAAC;AAEHZ,QAAQ,CAAC,iBAAiB,EAAE,IAAM;IAChCI,CAAAA,GAAAA,QAAU,AAAqE,CAAA,WAArE,CAA0BS,CAAAA,GAAAA,MAAe,AAA2B,CAAA,gBAA3B,EAA2B,CAAC,CAAC;IAChFT,CAAAA,GAAAA,QAAU,AAET,CAAA,WAFS,CACRS,CAAAA,GAAAA,MAAe,AAAqB,CAAA,gBAArB,EAAqB,CACrC,CAAC;IAEFX,CAAAA,GAAAA,QAAW,AAA+B,CAAA,YAA/B,CAACW,CAAAA,GAAAA,MAAe,AAAc,CAAA,gBAAd,EAAc,CAAC,CAAC;IAC3CX,CAAAA,GAAAA,QAAW,AAA+C,CAAA,YAA/C,CAACW,CAAAA,GAAAA,MAAe,AAA8B,CAAA,gBAA9B,EAA8B,CAAC,CAAC;CAC5D,CAAC,CAAC;AAEHb,QAAQ,CAAC,sBAAsB,EAAE,IAAM;IACrCI,CAAAA,GAAAA,QAAU,AAA0E,CAAA,WAA1E,CAA0BU,CAAAA,GAAAA,MAAoB,AAA2B,CAAA,qBAA3B,EAA2B,CAAC,CAAC;IACrFV,CAAAA,GAAAA,QAAU,AAET,CAAA,WAFS,CACRU,CAAAA,GAAAA,MAAoB,AAAqB,CAAA,qBAArB,EAAqB,CAC1C,CAAC;IAEFZ,CAAAA,GAAAA,QAAW,AAA+B,CAAA,YAA/B,CAACW,CAAAA,GAAAA,MAAe,AAAc,CAAA,gBAAd,EAAc,CAAC,CAAC;IAC3CX,CAAAA,GAAAA,QAAW,AAA+C,CAAA,YAA/C,CAACW,CAAAA,GAAAA,MAAe,AAA8B,CAAA,gBAA9B,EAA8B,CAAC,CAAC;CAC5D,CAAC,CAAC;AAEHb,QAAQ,CAAC,uBAAuB,EAAE,IAAM;IACtCI,CAAAA,GAAAA,QAAU,AAA2E,CAAA,WAA3E,CAA0BW,CAAAA,GAAAA,MAAqB,AAA2B,CAAA,sBAA3B,EAA2B,CAAC,CAAC;IACtFX,CAAAA,GAAAA,QAAU,AAET,CAAA,WAFS,CACRW,CAAAA,GAAAA,MAAqB,AAAqB,CAAA,sBAArB,EAAqB,CAC3C,CAAC;IAEFb,CAAAA,GAAAA,QAAW,AAAqC,CAAA,YAArC,CAACa,CAAAA,GAAAA,MAAqB,AAAc,CAAA,sBAAd,EAAc,CAAC,CAAC;IACjDb,CAAAA,GAAAA,QAAW,AAAqD,CAAA,YAArD,CAACa,CAAAA,GAAAA,MAAqB,AAA8B,CAAA,sBAA9B,EAA8B,CAAC,CAAC;CAClE,CAAC,CAAC;AAEHf,QAAQ,CAAC,aAAa,EAAE,IAAM;IAC5BC,EAAE,CAAC,4BAA4B,EAAE,IAAM;QACrCG,CAAAA,GAAAA,QAAU,AAAoC,CAAA,WAApC,CAAYY,CAAAA,GAAAA,MAAW,AAAY,CAAA,YAAZ,EAAY,CAAC,CAAC;KAChD,CAAC,CAAC;IAEHf,EAAE,CAAC,kCAAkC,EAAE,IAAM;QAC3CC,CAAAA,GAAAA,QAAW,AAA2B,CAAA,YAA3B,CAACc,CAAAA,GAAAA,MAAW,AAAc,CAAA,YAAd,EAAc,CAAC,CAAC;KACxC,CAAC,CAAC;IAEHf,EAAE,CAAC,iCAAiC,EAAE,IAAM;QAC1CG,CAAAA,GAAAA,QAAU,AAAqC,CAAA,WAArC,CAAYY,CAAAA,GAAAA,MAAW,AAAa,CAAA,YAAb,EAAa,CAAC,CAAC;KACjD,CAAC,CAAC;IAEHf,EAAE,CAAC,sCAAsC,EAAE,IAAM;QAC/CC,CAAAA,GAAAA,QAAW,AAAoC,CAAA,YAApC,CAACc,CAAAA,GAAAA,MAAW,AAAuB,CAAA,YAAvB,EAAuB,CAAC,CAAC;KACjD,CAAC,CAAC;CACJ,CAAC,CAAC;AAEHhB,QAAQ,CAAC,iBAAiB,EAAE,IAAM;IAChCC,EAAE,CAAC,6BAA6B,EAAE,IAAM;QACtCG,CAAAA,GAAAA,QAAU,AAAsC,CAAA,WAAtC,CAAON,MAAM,CAACK,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;KAClD,CAAC,CAAC;IAEHF,EAAE,CAAC,2BAA2B,EAAE,IAAM;QACpCG,CAAAA,GAAAA,QAAU,AAA+C,CAAA,WAA/C,CAAON,MAAM,CAACK,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC;KAC3D,CAAC,CAAC;IAEHF,EAAE,CAAC,uBAAuB,EAAE,IAAM;QAChCG,CAAAA,GAAAA,QAAU,AAA2C,CAAA,WAA3C,CAAON,MAAM,CAACK,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;KACvD,CAAC,CAAC;CACJ,CAAC,CAAC"}