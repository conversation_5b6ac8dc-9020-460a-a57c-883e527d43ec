{"version": 3, "sources": ["../../../src/whoami/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport { Command } from '../../bin/cli';\nimport { assertArgs, printHelp } from '../utils/args';\nimport { logCmdError } from '../utils/errors';\n\nexport const expoWhoami: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': <PERSON><PERSON><PERSON>,\n      // Aliases\n      '-h': '--help',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Show the currently authenticated username`,\n      `npx expo whoami`,\n      `-h, --help    Usage info`\n    );\n  }\n\n  const { whoamiAsync } = await import('./whoamiAsync.js');\n  return whoamiAsync().catch(logCmdError);\n};\n"], "names": ["expoWhoami", "argv", "args", "assertArgs", "Boolean", "printHelp", "<PERSON>ami<PERSON><PERSON>", "catch", "logCmdError"], "mappings": "AAAA;;;;;;AAEsC,IAAA,KAAe,WAAf,eAAe,CAAA;AACzB,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;;;;;;;;;;;;;;;;;;;;;;AAEtC,MAAMA,UAAU,GAAY,OAAOC,IAAI,GAAK;IACjD,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,KAAU,AAQtB,CAAA,WARsB,CACrB;QACE,QAAQ;QACR,QAAQ,EAAEC,OAAO;QACjB,UAAU;QACV,IAAI,EAAE,QAAQ;KACf,EACDH,IAAI,CACL,AAAC;IAEF,IAAIC,IAAI,CAAC,QAAQ,CAAC,EAAE;QAClBG,CAAAA,GAAAA,KAAS,AAIR,CAAA,UAJQ,CACP,CAAC,yCAAyC,CAAC,EAC3C,CAAC,eAAe,CAAC,EACjB,CAAC,wBAAwB,CAAC,CAC3B,CAAC;KACH;IAED,MAAM,EAAEC,WAAW,CAAA,EAAE,GAAG,MAAM;+CAAO,kBAAkB;MAAC,AAAC;IACzD,OAAOA,WAAW,EAAE,CAACC,KAAK,CAACC,OAAW,YAAA,CAAC,CAAC;CACzC,AAAC;QArBWR,UAAU,GAAVA,UAAU"}