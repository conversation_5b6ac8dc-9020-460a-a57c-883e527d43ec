{"version": 3, "sources": ["../../../src/utils/downloadAppAsync.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { Stream } from 'stream';\nimport temporary from 'tempy';\nimport { promisify } from 'util';\n\nimport { ensureDirectoryAsync } from './dir';\nimport { CommandError } from './errors';\nimport { extractAsync } from './tar';\nimport { createCachedFetch, fetchAsync } from '../api/rest/client';\nimport { FetchLike, ProgressCallback } from '../api/rest/client.types';\n\nconst debug = require('debug')('expo:utils:downloadAppAsync') as typeof console.log;\n\nconst TIMER_DURATION = 30000;\n\nconst pipeline = promisify(Stream.pipeline);\n\nasync function downloadAsync({\n  url,\n  outputPath,\n  cacheDirectory,\n  onProgress,\n}: {\n  url: string;\n  outputPath: string;\n  cacheDirectory?: string;\n  onProgress?: ProgressCallback;\n}) {\n  let fetchInstance: FetchLike = fetchAsync;\n  if (cacheDirectory) {\n    // Reconstruct the cached fetch since caching could be disabled.\n    fetchInstance = createCachedFetch({\n      // We'll use a 1 week cache for versions so older values get flushed out eventually.\n      ttl: 1000 * 60 * 60 * 24 * 7,\n      // Users can also nuke their `~/.expo` directory to clear the cache.\n      cacheDirectory,\n    });\n  }\n\n  debug(`Downloading ${url} to ${outputPath}`);\n  const res = await fetchInstance(url, {\n    timeout: TIMER_DURATION,\n    onProgress,\n  });\n  if (!res.ok) {\n    throw new CommandError(\n      'FILE_DOWNLOAD',\n      `Unexpected response: ${res.statusText}. From url: ${url}`\n    );\n  }\n  return pipeline(res.body, fs.createWriteStream(outputPath));\n}\n\nexport async function downloadAppAsync({\n  url,\n  outputPath,\n  extract = false,\n  cacheDirectory,\n  onProgress,\n}: {\n  url: string;\n  outputPath: string;\n  extract?: boolean;\n  cacheDirectory?: string;\n  onProgress?: ProgressCallback;\n}): Promise<void> {\n  if (extract) {\n    // For iOS we download the ipa to a file then pass that file into the extractor.\n    // In the future we should just pipe the `res.body -> tar.extract` directly.\n    // I tried this and it created some weird errors where observing the data stream\n    // would corrupt the file causing tar to fail with `TAR_BAD_ARCHIVE`.\n    const tmpPath = temporary.file({ name: path.basename(outputPath) });\n    await downloadAsync({ url, outputPath: tmpPath, cacheDirectory, onProgress });\n    debug(`Extracting ${tmpPath} to ${outputPath}`);\n    await ensureDirectoryAsync(outputPath);\n    await extractAsync(tmpPath, outputPath);\n  } else {\n    await ensureDirectoryAsync(path.dirname(outputPath));\n    await downloadAsync({ url, outputPath, cacheDirectory, onProgress });\n  }\n}\n"], "names": ["downloadAppAsync", "debug", "require", "TIMER_DURATION", "pipeline", "promisify", "Stream", "downloadAsync", "url", "outputPath", "cacheDirectory", "onProgress", "fetchInstance", "fetchAsync", "createCachedFetch", "ttl", "res", "timeout", "ok", "CommandError", "statusText", "body", "fs", "createWriteStream", "extract", "tmpPath", "temporary", "file", "name", "path", "basename", "ensureDirectoryAsync", "extractAsync", "dirname"], "mappings": "AAAA;;;;QAsDsBA,gBAAgB,GAAhBA,gBAAgB;AAtDvB,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AACA,IAAA,OAAQ,WAAR,QAAQ,CAAA;AACT,IAAA,MAAO,kCAAP,OAAO,EAAA;AACH,IAAA,KAAM,WAAN,MAAM,CAAA;AAEK,IAAA,IAAO,WAAP,OAAO,CAAA;AACf,IAAA,OAAU,WAAV,UAAU,CAAA;AACV,IAAA,IAAO,WAAP,OAAO,CAAA;AACU,IAAA,OAAoB,WAApB,oBAAoB,CAAA;;;;;;AAGlE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,6BAA6B,CAAC,AAAsB,AAAC;AAEpF,MAAMC,cAAc,GAAG,KAAK,AAAC;AAE7B,MAAMC,QAAQ,GAAGC,CAAAA,GAAAA,KAAS,AAAiB,CAAA,UAAjB,CAACC,OAAM,OAAA,CAACF,QAAQ,CAAC,AAAC;AAE5C,eAAeG,aAAa,CAAC,EAC3BC,GAAG,CAAA,EACHC,UAAU,CAAA,EACVC,cAAc,CAAA,EACdC,UAAU,CAAA,EAMX,EAAE;IACD,IAAIC,aAAa,GAAcC,OAAU,WAAA,AAAC;IAC1C,IAAIH,cAAc,EAAE;QAClB,gEAAgE;QAChEE,aAAa,GAAGE,CAAAA,GAAAA,OAAiB,AAK/B,CAAA,kBAL+B,CAAC;YAChC,oFAAoF;YACpFC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YAC5B,oEAAoE;YACpEL,cAAc;SACf,CAAC,CAAC;KACJ;IAEDT,KAAK,CAAC,CAAC,YAAY,EAAEO,GAAG,CAAC,IAAI,EAAEC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC7C,MAAMO,GAAG,GAAG,MAAMJ,aAAa,CAACJ,GAAG,EAAE;QACnCS,OAAO,EAAEd,cAAc;QACvBQ,UAAU;KACX,CAAC,AAAC;IACH,IAAI,CAACK,GAAG,CAACE,EAAE,EAAE;QACX,MAAM,IAAIC,OAAY,aAAA,CACpB,eAAe,EACf,CAAC,qBAAqB,EAAEH,GAAG,CAACI,UAAU,CAAC,YAAY,EAAEZ,GAAG,CAAC,CAAC,CAC3D,CAAC;KACH;IACD,OAAOJ,QAAQ,CAACY,GAAG,CAACK,IAAI,EAAEC,GAAE,QAAA,CAACC,iBAAiB,CAACd,UAAU,CAAC,CAAC,CAAC;CAC7D;AAEM,eAAeT,gBAAgB,CAAC,EACrCQ,GAAG,CAAA,EACHC,UAAU,CAAA,EACVe,OAAO,EAAG,KAAK,CAAA,EACfd,cAAc,CAAA,EACdC,UAAU,CAAA,EAOX,EAAiB;IAChB,IAAIa,OAAO,EAAE;QACX,gFAAgF;QAChF,4EAA4E;QAC5E,gFAAgF;QAChF,qEAAqE;QACrE,MAAMC,OAAO,GAAGC,MAAS,QAAA,CAACC,IAAI,CAAC;YAAEC,IAAI,EAAEC,KAAI,QAAA,CAACC,QAAQ,CAACrB,UAAU,CAAC;SAAE,CAAC,AAAC;QACpE,MAAMF,aAAa,CAAC;YAAEC,GAAG;YAAEC,UAAU,EAAEgB,OAAO;YAAEf,cAAc;YAAEC,UAAU;SAAE,CAAC,CAAC;QAC9EV,KAAK,CAAC,CAAC,WAAW,EAAEwB,OAAO,CAAC,IAAI,EAAEhB,UAAU,CAAC,CAAC,CAAC,CAAC;QAChD,MAAMsB,CAAAA,GAAAA,IAAoB,AAAY,CAAA,qBAAZ,CAACtB,UAAU,CAAC,CAAC;QACvC,MAAMuB,CAAAA,GAAAA,IAAY,AAAqB,CAAA,aAArB,CAACP,OAAO,EAAEhB,UAAU,CAAC,CAAC;KACzC,MAAM;QACL,MAAMsB,CAAAA,GAAAA,IAAoB,AAA0B,CAAA,qBAA1B,CAACF,KAAI,QAAA,CAACI,OAAO,CAACxB,UAAU,CAAC,CAAC,CAAC;QACrD,MAAMF,aAAa,CAAC;YAAEC,GAAG;YAAEC,UAAU;YAAEC,cAAc;YAAEC,UAAU;SAAE,CAAC,CAAC;KACtE;CACF"}