{"version": 3, "sources": ["../../../../src/utils/analytics/rudderstackClient.ts"], "sourcesContent": ["import RudderAnalytics from '@expo/rudder-sdk-node';\nimport * as ciInfo from 'ci-info';\nimport os from 'os';\n\nimport UserSettings from '../../api/user/UserSettings';\nimport { getUserAsync } from '../../api/user/user';\nimport { env } from '../env';\n\nconst PLATFORM_TO_ANALYTICS_PLATFORM: { [platform: string]: string } = {\n  darwin: 'Mac',\n  win32: 'Windows',\n  linux: 'Linux',\n};\n\nlet client: RudderAnalytics | null = null;\nlet identified = false;\nlet identifyData: {\n  userId: string;\n  deviceId: string;\n  traits: Record<string, any>;\n} | null = null;\n\nexport function resetInternalStateForTesting() {\n  identified = false;\n  identifyData = null;\n  client = null;\n}\n\nexport function getRudderAnalyticsClient(): RudderAnalytics {\n  if (client) {\n    return client;\n  }\n\n  client = new RudderAnalytics(\n    env.EXPO_STAGING || env.EXPO_LOCAL\n      ? '24TKICqYKilXM480mA7ktgVDdea'\n      : '24TKR7CQAaGgIrLTgu3Fp4OdOkI', // expo unified\n    'https://cdp.expo.dev/v1/batch',\n    {\n      flushInterval: 300,\n    }\n  );\n\n  // Install flush on exit...\n  process.on('SIGINT', () => client?.flush?.());\n  process.on('SIGTERM', () => client?.flush?.());\n\n  return client;\n}\n\nexport async function setUserDataAsync(userId: string, traits: Record<string, any>): Promise<void> {\n  if (env.EXPO_NO_TELEMETRY) {\n    return;\n  }\n\n  const deviceId = await UserSettings.getAnonymousIdentifierAsync();\n\n  identifyData = {\n    userId,\n    deviceId,\n    traits,\n  };\n\n  identifyIfNotYetIdentified();\n}\n\ntype Event =\n  | 'action'\n  | 'Open Url on Device'\n  | 'Start Project'\n  | 'Serve Manifest'\n  | 'Serve Expo Updates Manifest'\n  | 'dev client start command'\n  | 'dev client run command'\n  | 'metro config'\n  | 'metro debug';\n\n/**\n * Log an event, ensuring the user is identified before logging the event.\n **/\nexport async function logEventAsync(\n  event: Event,\n  properties: Record<string, any> = {}\n): Promise<void> {\n  if (env.EXPO_NO_TELEMETRY) {\n    return;\n  }\n\n  // this has the side effect of calling `setUserData` which fetches the user and populates identifyData\n  try {\n    await getUserAsync();\n  } catch {}\n\n  identifyIfNotYetIdentified();\n\n  if (!identifyData) {\n    return;\n  }\n  const { userId, deviceId } = identifyData;\n  const commonEventProperties = { source_version: process.env.__EXPO_VERSION, source: 'expo' };\n\n  const identity = { userId, anonymousId: deviceId };\n  getRudderAnalyticsClient().track({\n    event,\n    properties: { ...properties, ...commonEventProperties },\n    ...identity,\n    context: getContext(),\n  });\n}\n\nfunction identifyIfNotYetIdentified(): void {\n  if (env.EXPO_NO_TELEMETRY || identified || !identifyData) {\n    return;\n  }\n\n  getRudderAnalyticsClient().identify({\n    userId: identifyData.userId,\n    anonymousId: identifyData.deviceId,\n    traits: identifyData.traits,\n  });\n  identified = true;\n}\n\n/** Exposed for testing only */\nexport function getContext(): Record<string, any> {\n  const platform = PLATFORM_TO_ANALYTICS_PLATFORM[os.platform()] || os.platform();\n  return {\n    os: { name: platform, version: os.release() },\n    device: { type: platform, model: platform },\n    app: { name: 'expo', version: process.env.__EXPO_VERSION },\n    ci: ciInfo.isCI ? { name: ciInfo.name, isPr: ciInfo.isPR } : undefined,\n  };\n}\n"], "names": ["resetInternalStateForTesting", "getRudderAnalyticsClient", "setUserDataAsync", "logEventAsync", "getContext", "ciInfo", "PLATFORM_TO_ANALYTICS_PLATFORM", "darwin", "win32", "linux", "client", "identified", "identifyData", "RudderAnalytics", "env", "EXPO_STAGING", "EXPO_LOCAL", "flushInterval", "process", "on", "flush", "userId", "traits", "EXPO_NO_TELEMETRY", "deviceId", "UserSettings", "getAnonymousIdentifierAsync", "identifyIfNotYetIdentified", "event", "properties", "getUserAsync", "commonEventProperties", "source_version", "__EXPO_VERSION", "source", "identity", "anonymousId", "track", "context", "identify", "platform", "os", "name", "version", "release", "device", "type", "model", "app", "ci", "isCI", "isPr", "isPR", "undefined"], "mappings": "AAAA;;;;QAsBgBA,4BAA4B,GAA5BA,4BAA4B;QAM5BC,wBAAwB,GAAxBA,wBAAwB;QAsBlBC,gBAAgB,GAAhBA,gBAAgB;QA8BhBC,aAAa,GAAbA,aAAa;QA4CnBC,UAAU,GAAVA,UAAU;AA5HE,IAAA,cAAuB,kCAAvB,uBAAuB,EAAA;AACvCC,IAAAA,MAAM,mCAAM,SAAS,EAAf;AACH,IAAA,GAAI,kCAAJ,IAAI,EAAA;AAEM,IAAA,aAA6B,kCAA7B,6BAA6B,EAAA;AACzB,IAAA,KAAqB,WAArB,qBAAqB,CAAA;AAC9B,IAAA,IAAQ,WAAR,QAAQ,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5B,MAAMC,8BAA8B,GAAmC;IACrEC,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,OAAO;CACf,AAAC;AAEF,IAAIC,MAAM,GAA2B,IAAI,AAAC;AAC1C,IAAIC,UAAU,GAAG,KAAK,AAAC;AACvB,IAAIC,YAAY,GAIL,IAAI,AAAC;AAET,SAASZ,4BAA4B,GAAG;IAC7CW,UAAU,GAAG,KAAK,CAAC;IACnBC,YAAY,GAAG,IAAI,CAAC;IACpBF,MAAM,GAAG,IAAI,CAAC;CACf;AAEM,SAAST,wBAAwB,GAAoB;IAC1D,IAAIS,MAAM,EAAE;QACV,OAAOA,MAAM,CAAC;KACf;IAEDA,MAAM,GAAG,IAAIG,cAAe,QAAA,CAC1BC,IAAG,IAAA,CAACC,YAAY,IAAID,IAAG,IAAA,CAACE,UAAU,GAC9B,6BAA6B,GAC7B,6BAA6B,EACjC,+BAA+B,EAC/B;QACEC,aAAa,EAAE,GAAG;KACnB,CACF,CAAC;IAEF,2BAA2B;IAC3BC,OAAO,CAACC,EAAE,CAAC,QAAQ,EAAE;QAAMT,OAAAA,MAAM,QAAO,GAAbA,KAAAA,CAAa,GAAbA,MAAM,CAAEU,KAAK,QAAI,GAAjBV,KAAAA,CAAiB,GAAjBA,MAAM,CAAEU,KAAK,EAAI,CAAA;KAAA,CAAC,CAAC;IAC9CF,OAAO,CAACC,EAAE,CAAC,SAAS,EAAE;QAAMT,OAAAA,MAAM,QAAO,GAAbA,KAAAA,CAAa,GAAbA,MAAM,CAAEU,KAAK,QAAI,GAAjBV,KAAAA,CAAiB,GAAjBA,MAAM,CAAEU,KAAK,EAAI,CAAA;KAAA,CAAC,CAAC;IAE/C,OAAOV,MAAM,CAAC;CACf;AAEM,eAAeR,gBAAgB,CAACmB,MAAc,EAAEC,MAA2B,EAAiB;IACjG,IAAIR,IAAG,IAAA,CAACS,iBAAiB,EAAE;QACzB,OAAO;KACR;IAED,MAAMC,QAAQ,GAAG,MAAMC,aAAY,QAAA,CAACC,2BAA2B,EAAE,AAAC;IAElEd,YAAY,GAAG;QACbS,MAAM;QACNG,QAAQ;QACRF,MAAM;KACP,CAAC;IAEFK,0BAA0B,EAAE,CAAC;CAC9B;AAgBM,eAAexB,aAAa,CACjCyB,KAAY,EACZC,UAA+B,GAAG,EAAE,EACrB;IACf,IAAIf,IAAG,IAAA,CAACS,iBAAiB,EAAE;QACzB,OAAO;KACR;IAED,sGAAsG;IACtG,IAAI;QACF,MAAMO,CAAAA,GAAAA,KAAY,AAAE,CAAA,aAAF,EAAE,CAAC;KACtB,CAAC,OAAM,EAAE;IAEVH,0BAA0B,EAAE,CAAC;IAE7B,IAAI,CAACf,YAAY,EAAE;QACjB,OAAO;KACR;IACD,MAAM,EAAES,MAAM,CAAA,EAAEG,QAAQ,CAAA,EAAE,GAAGZ,YAAY,AAAC;IAC1C,MAAMmB,qBAAqB,GAAG;QAAEC,cAAc,EAAEd,OAAO,CAACJ,GAAG,CAACmB,cAAc;QAAEC,MAAM,EAAE,MAAM;KAAE,AAAC;IAE7F,MAAMC,QAAQ,GAAG;QAAEd,MAAM;QAAEe,WAAW,EAAEZ,QAAQ;KAAE,AAAC;IACnDvB,wBAAwB,EAAE,CAACoC,KAAK,CAAC;QAC/BT,KAAK;QACLC,UAAU,EAAE;YAAE,GAAGA,UAAU;YAAE,GAAGE,qBAAqB;SAAE;QACvD,GAAGI,QAAQ;QACXG,OAAO,EAAElC,UAAU,EAAE;KACtB,CAAC,CAAC;CACJ;AAED,SAASuB,0BAA0B,GAAS;IAC1C,IAAIb,IAAG,IAAA,CAACS,iBAAiB,IAAIZ,UAAU,IAAI,CAACC,YAAY,EAAE;QACxD,OAAO;KACR;IAEDX,wBAAwB,EAAE,CAACsC,QAAQ,CAAC;QAClClB,MAAM,EAAET,YAAY,CAACS,MAAM;QAC3Be,WAAW,EAAExB,YAAY,CAACY,QAAQ;QAClCF,MAAM,EAAEV,YAAY,CAACU,MAAM;KAC5B,CAAC,CAAC;IACHX,UAAU,GAAG,IAAI,CAAC;CACnB;AAGM,SAASP,UAAU,GAAwB;IAChD,MAAMoC,QAAQ,GAAGlC,8BAA8B,CAACmC,GAAE,QAAA,CAACD,QAAQ,EAAE,CAAC,IAAIC,GAAE,QAAA,CAACD,QAAQ,EAAE,AAAC;IAChF,OAAO;QACLC,EAAE,EAAE;YAAEC,IAAI,EAAEF,QAAQ;YAAEG,OAAO,EAAEF,GAAE,QAAA,CAACG,OAAO,EAAE;SAAE;QAC7CC,MAAM,EAAE;YAAEC,IAAI,EAAEN,QAAQ;YAAEO,KAAK,EAAEP,QAAQ;SAAE;QAC3CQ,GAAG,EAAE;YAAEN,IAAI,EAAE,MAAM;YAAEC,OAAO,EAAEzB,OAAO,CAACJ,GAAG,CAACmB,cAAc;SAAE;QAC1DgB,EAAE,EAAE5C,MAAM,CAAC6C,IAAI,GAAG;YAAER,IAAI,EAAErC,MAAM,CAACqC,IAAI;YAAES,IAAI,EAAE9C,MAAM,CAAC+C,IAAI;SAAE,GAAGC,SAAS;KACvE,CAAC;CACH"}