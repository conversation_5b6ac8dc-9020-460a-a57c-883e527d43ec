"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.createDebugMiddleware = createDebugMiddleware;
var _chalk = _interopRequireDefault(require("chalk"));
var _inspectorDevice = require("./InspectorDevice");
var _inspectorProxy = require("./InspectorProxy");
var _log = require("../../../../log");
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function createDebugMiddleware(metroBundler) {
    // Load the React Native debugging tools from project
    // TODO: check if this works with isolated modules
    const { createDevMiddleware , unstable_Device , unstable_InspectorProxy  } = require("@react-native/dev-middleware");
    // Create the extended inspector proxy, using our own device class
    const ExpoInspectorProxy = (0, _inspectorProxy).createInspectorProxyClass(unstable_InspectorProxy, (0, _inspectorDevice).createInspectorDeviceClass(metroBundler, unstable_Device));
    const { middleware , websocketEndpoints  } = createDevMiddleware({
        projectRoot: metroBundler.projectRoot,
        serverBaseUrl: metroBundler.getUrlCreator().constructUrl({
            scheme: "http",
            hostType: "lan"
        }),
        logger: createLogger(_chalk.default.bold("Debug:")),
        unstable_InspectorProxy: ExpoInspectorProxy,
        unstable_experiments: {
            enableNewDebugger: true
        }
    });
    return {
        debugMiddleware: middleware,
        debugWebsocketEndpoints: websocketEndpoints
    };
}
function createLogger(logPrefix) {
    return {
        info: (...args)=>_log.Log.log(logPrefix, ...args)
        ,
        warn: (...args)=>_log.Log.warn(logPrefix, ...args)
        ,
        error: (...args)=>_log.Log.error(logPrefix, ...args)
    };
}

//# sourceMappingURL=createDebugMiddleware.js.map