{"version": 3, "sources": ["../../../../../src/start/server/middleware/RuntimeRedirectMiddleware.ts"], "sourcesContent": ["import { parse } from 'url';\n\nimport { disableResponseCache, ExpoMiddleware } from './ExpoMiddleware';\nimport {\n  assertMissingRuntimePlatform,\n  assertRuntimePlatform,\n  parsePlatformHeader,\n  resolvePlatformFromUserAgentHeader,\n  RuntimePlatform,\n} from './resolvePlatform';\nimport { ServerRequest, ServerResponse } from './server.types';\nimport * as Log from '../../../log';\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:runtimeRedirect'\n) as typeof console.log;\n\n/** Runtime to target: expo = Expo Go, custom = Dev Client. */\ntype RuntimeTarget = 'expo' | 'custom';\n\nexport type DeepLinkHandler = (props: {\n  runtime: RuntimeTarget;\n  platform: RuntimePlatform;\n}) => void | Promise<void>;\n\nexport class RuntimeRedirectMiddleware extends ExpoMiddleware {\n  constructor(\n    protected projectRoot: string,\n    protected options: {\n      onDeepLink: DeepLinkHandler;\n      getLocation: (props: { runtime: RuntimeTarget }) => string | null | undefined;\n    }\n  ) {\n    super(projectRoot, ['/_expo/link']);\n  }\n\n  async handleRequestAsync(req: ServerRequest, res: ServerResponse): Promise<void> {\n    const { query } = parse(req.url!, true);\n    const isDevClient = query['choice'] === 'expo-dev-client';\n    const platform = parsePlatformHeader(req) ?? resolvePlatformFromUserAgentHeader(req);\n    assertMissingRuntimePlatform(platform);\n    assertRuntimePlatform(platform);\n    const runtime = isDevClient ? 'custom' : 'expo';\n\n    debug(`props:`, { platform, runtime });\n\n    this.options.onDeepLink({ runtime, platform });\n\n    const redirect = this.options.getLocation({ runtime });\n    if (!redirect) {\n      Log.warn(\n        `[redirect middleware]: Unable to determine redirect location for runtime '${runtime}' and platform '${platform}'`\n      );\n      res.statusCode = 404;\n      res.end();\n      return;\n    }\n    debug('Redirect ->', redirect);\n    res.setHeader('Location', redirect);\n\n    // Disable caching\n    disableResponseCache(res);\n\n    // 'Temporary Redirect'\n    res.statusCode = 307;\n    res.end();\n  }\n}\n"], "names": ["Log", "debug", "require", "RuntimeRedirectMiddleware", "ExpoMiddleware", "constructor", "projectRoot", "options", "handleRequestAsync", "req", "res", "query", "parse", "url", "isDevClient", "parsePlatformHeader", "platform", "resolvePlatformFromUserAgentHeader", "assertMissingRuntimePlatform", "assertRuntimePlatform", "runtime", "onDeepLink", "redirect", "getLocation", "warn", "statusCode", "end", "<PERSON><PERSON><PERSON><PERSON>", "disableResponseCache"], "mappings": "AAAA;;;;AAAsB,IAAA,IAAK,WAAL,KAAK,CAAA;AAE0B,IAAA,eAAkB,WAAlB,kBAAkB,CAAA;AAOhE,IAAA,gBAAmB,WAAnB,mBAAmB,CAAA;AAEdA,IAAAA,GAAG,mCAAM,cAAc,EAApB;;;;;;;;;;;;;;;;;;;;;;AAEf,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,8CAA8C,CAC/C,AAAsB,AAAC;AAUjB,MAAMC,yBAAyB,SAASC,eAAc,eAAA;IAC3DC,YACYC,WAAmB,EACnBC,OAGT,CACD;QACA,KAAK,CAACD,WAAW,EAAE;YAAC,aAAa;SAAC,CAAC,CAAC;aAN1BA,WAAmB,GAAnBA,WAAmB;aACnBC,OAGT,GAHSA,OAGT;KAGF;IAED,MAAMC,kBAAkB,CAACC,GAAkB,EAAEC,GAAmB,EAAiB;QAC/E,MAAM,EAAEC,KAAK,CAAA,EAAE,GAAGC,CAAAA,GAAAA,IAAK,AAAgB,CAAA,MAAhB,CAACH,GAAG,CAACI,GAAG,EAAG,IAAI,CAAC,AAAC;QACxC,MAAMC,WAAW,GAAGH,KAAK,CAAC,QAAQ,CAAC,KAAK,iBAAiB,AAAC;YACzCI,GAAwB;QAAzC,MAAMC,QAAQ,GAAGD,CAAAA,GAAwB,GAAxBA,CAAAA,GAAAA,gBAAmB,AAAK,CAAA,oBAAL,CAACN,GAAG,CAAC,YAAxBM,GAAwB,GAAIE,CAAAA,GAAAA,gBAAkC,AAAK,CAAA,mCAAL,CAACR,GAAG,CAAC,AAAC;QACrFS,CAAAA,GAAAA,gBAA4B,AAAU,CAAA,6BAAV,CAACF,QAAQ,CAAC,CAAC;QACvCG,CAAAA,GAAAA,gBAAqB,AAAU,CAAA,sBAAV,CAACH,QAAQ,CAAC,CAAC;QAChC,MAAMI,OAAO,GAAGN,WAAW,GAAG,QAAQ,GAAG,MAAM,AAAC;QAEhDb,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;YAAEe,QAAQ;YAAEI,OAAO;SAAE,CAAC,CAAC;QAEvC,IAAI,CAACb,OAAO,CAACc,UAAU,CAAC;YAAED,OAAO;YAAEJ,QAAQ;SAAE,CAAC,CAAC;QAE/C,MAAMM,QAAQ,GAAG,IAAI,CAACf,OAAO,CAACgB,WAAW,CAAC;YAAEH,OAAO;SAAE,CAAC,AAAC;QACvD,IAAI,CAACE,QAAQ,EAAE;YACbtB,GAAG,CAACwB,IAAI,CACN,CAAC,0EAA0E,EAAEJ,OAAO,CAAC,gBAAgB,EAAEJ,QAAQ,CAAC,CAAC,CAAC,CACnH,CAAC;YACFN,GAAG,CAACe,UAAU,GAAG,GAAG,CAAC;YACrBf,GAAG,CAACgB,GAAG,EAAE,CAAC;YACV,OAAO;SACR;QACDzB,KAAK,CAAC,aAAa,EAAEqB,QAAQ,CAAC,CAAC;QAC/BZ,GAAG,CAACiB,SAAS,CAAC,UAAU,EAAEL,QAAQ,CAAC,CAAC;QAEpC,kBAAkB;QAClBM,CAAAA,GAAAA,eAAoB,AAAK,CAAA,qBAAL,CAAClB,GAAG,CAAC,CAAC;QAE1B,uBAAuB;QACvBA,GAAG,CAACe,UAAU,GAAG,GAAG,CAAC;QACrBf,GAAG,CAACgB,GAAG,EAAE,CAAC;KACX;CACF;QA1CYvB,yBAAyB,GAAzBA,yBAAyB"}