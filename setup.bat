@echo off
echo ========================================
echo Construction Platform Setup Script
echo ========================================
echo.

echo [1/9] Creating required directories...
if not exist "bootstrap\cache" mkdir "bootstrap\cache"
if not exist "storage\app" mkdir "storage\app"
if not exist "storage\app\public" mkdir "storage\app\public"
if not exist "storage\framework" mkdir "storage\framework"
if not exist "storage\framework\cache" mkdir "storage\framework\cache"
if not exist "storage\framework\cache\data" mkdir "storage\framework\cache\data"
if not exist "storage\framework\sessions" mkdir "storage\framework\sessions"
if not exist "storage\framework\views" mkdir "storage\framework\views"
if not exist "storage\logs" mkdir "storage\logs"
echo Required directories created successfully

echo.
echo [2/9] Installing Composer dependencies...
call composer install
if %errorlevel% neq 0 (
    echo ERROR: Composer install failed
    pause
    exit /b 1
)

echo.
echo [3/9] Copying environment file...
if not exist .env (
    copy .env.example .env
    echo Environment file created from .env.example
) else (
    echo Environment file already exists
)

echo.
echo [4/9] Generating application key...
call php artisan key:generate
if %errorlevel% neq 0 (
    echo ERROR: Key generation failed
    pause
    exit /b 1
)

echo.
echo [5/9] Creating database (if not exists)...
call php artisan migrate:status >nul 2>&1
if %errorlevel% neq 0 (
    echo Database connection not available. Please ensure MySQL is running and database is created.
    echo You can create the database manually: CREATE DATABASE construction_platform;
    pause
)

echo.
echo [6/9] Running database migrations...
call php artisan migrate --force
if %errorlevel% neq 0 (
    echo ERROR: Migration failed
    pause
    exit /b 1
)

echo.
echo [7/9] Seeding database with sample data...
call php artisan db:seed --force
if %errorlevel% neq 0 (
    echo ERROR: Database seeding failed
    pause
    exit /b 1
)

echo.
echo [8/9] Creating storage symlink...
call php artisan storage:link
if %errorlevel% neq 0 (
    echo ERROR: Storage link creation failed
    pause
    exit /b 1
)

echo.
echo [9/9] Setting up React Native mobile app...
cd construction-admin
if exist package.json (
    echo Installing React Native dependencies...
    call npm install
    if %errorlevel% neq 0 (
        echo ERROR: npm install failed for React Native app
        cd ..
        pause
        exit /b 1
    )
    echo React Native app dependencies installed successfully
) else (
    echo React Native package.json not found, skipping...
)
cd ..

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo Default admin credentials:
echo Email: <EMAIL>
echo Password: admin123
echo.
echo To start the Laravel development server:
echo php artisan serve
echo.
echo To start the React Native development server:
echo cd construction-admin
echo npx expo start
echo.
echo The application will be available at:
echo Web: http://localhost:8000
echo Admin: http://localhost:8000/admin
echo API: http://localhost:8000/api
echo.
pause
