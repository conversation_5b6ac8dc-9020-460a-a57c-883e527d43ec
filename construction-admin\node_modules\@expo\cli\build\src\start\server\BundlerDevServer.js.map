{"version": 3, "sources": ["../../../../src/start/server/BundlerDevServer.ts"], "sourcesContent": ["import assert from 'assert';\nimport resolveFrom from 'resolve-from';\n\nimport { AsyncNgrok } from './AsyncNgrok';\nimport DevToolsPluginManager from './DevToolsPluginManager';\nimport { DevelopmentSession } from './DevelopmentSession';\nimport { CreateURLOptions, UrlCreator } from './UrlCreator';\nimport { PlatformBundlers } from './platformBundlers';\nimport * as Log from '../../log';\nimport { FileNotifier } from '../../utils/FileNotifier';\nimport { resolveWithTimeout } from '../../utils/delay';\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { openBrowserAsync } from '../../utils/open';\nimport {\n  BaseOpenInCustomProps,\n  BaseResolveDeviceProps,\n  PlatformManager,\n} from '../platforms/PlatformManager';\n\nconst debug = require('debug')('expo:start:server:devServer') as typeof console.log;\n\nexport type MessageSocket = {\n  broadcast: (method: string, params?: Record<string, any> | undefined) => void;\n};\n\nexport type ServerLike = {\n  close(callback?: (err?: Error) => void): void;\n  addListener?(event: string, listener: (...args: any[]) => void): unknown;\n};\n\nexport type DevServerInstance = {\n  /** Bundler dev server instance. */\n  server: ServerLike;\n  /** Dev server URL location properties. */\n  location: {\n    url: string;\n    port: number;\n    protocol: 'http' | 'https';\n    host?: string;\n  };\n  /** Additional middleware that's attached to the `server`. */\n  middleware: any;\n  /** Message socket for communicating with the runtime. */\n  messageSocket: MessageSocket;\n};\n\nexport interface BundlerStartOptions {\n  /** Should the dev server use `https` protocol. */\n  https?: boolean;\n  /** Should start the dev servers in development mode (minify). */\n  mode?: 'development' | 'production';\n  /** Is dev client enabled. */\n  devClient?: boolean;\n  /** Should run dev servers with clean caches. */\n  resetDevServer?: boolean;\n  /** Code signing private key path (defaults to same directory as certificate) */\n  privateKeyPath?: string;\n\n  /** Max amount of workers (threads) to use with Metro bundler, defaults to undefined for max workers. */\n  maxWorkers?: number;\n  /** Port to start the dev server on. */\n  port?: number;\n\n  /** Should start a headless dev server e.g. mock representation to approximate info from a server running in a different process. */\n  headless?: boolean;\n  /** Should instruct the bundler to create minified bundles. */\n  minify?: boolean;\n\n  /** Will the bundler be used for exporting. NOTE: This is an odd option to pass to the dev server. */\n  isExporting?: boolean;\n\n  // Webpack options\n  /** Should modify and create PWA icons. */\n  isImageEditingEnabled?: boolean;\n\n  location: CreateURLOptions;\n}\n\nconst PLATFORM_MANAGERS = {\n  simulator: () =>\n    require('../platforms/ios/ApplePlatformManager')\n      .ApplePlatformManager as typeof import('../platforms/ios/ApplePlatformManager').ApplePlatformManager,\n  emulator: () =>\n    require('../platforms/android/AndroidPlatformManager')\n      .AndroidPlatformManager as typeof import('../platforms/android/AndroidPlatformManager').AndroidPlatformManager,\n};\n\nexport abstract class BundlerDevServer {\n  /** Name of the bundler. */\n  abstract get name(): string;\n\n  /** Ngrok instance for managing tunnel connections. */\n  protected ngrok: AsyncNgrok | null = null;\n  /** Interfaces with the Expo 'Development Session' API. */\n  protected devSession: DevelopmentSession | null = null;\n  /** Http server and related info. */\n  protected instance: DevServerInstance | null = null;\n  /** Native platform interfaces for opening projects.  */\n  private platformManagers: Record<string, PlatformManager<any>> = {};\n  /** Manages the creation of dev server URLs. */\n  protected urlCreator?: UrlCreator | null = null;\n\n  private notifier: FileNotifier | null = null;\n  protected readonly devToolsPluginManager: DevToolsPluginManager;\n  public isDevClient: boolean;\n\n  constructor(\n    /** Project root folder. */\n    public projectRoot: string,\n    /** A mapping of bundlers to platforms. */\n    public platformBundlers: PlatformBundlers,\n    /** Advanced options */\n    options?: {\n      /**\n       * The instance of DevToolsPluginManager\n       * @default new DevToolsPluginManager(projectRoot)\n       */\n      devToolsPluginManager?: DevToolsPluginManager;\n      // TODO: Replace with custom scheme maybe...\n      isDevClient?: boolean;\n    }\n  ) {\n    this.devToolsPluginManager =\n      options?.devToolsPluginManager ?? new DevToolsPluginManager(projectRoot);\n    this.isDevClient = options?.isDevClient ?? false;\n  }\n\n  protected setInstance(instance: DevServerInstance) {\n    this.instance = instance;\n  }\n\n  /** Get the manifest middleware function. */\n  protected async getManifestMiddlewareAsync(\n    options: Pick<BundlerStartOptions, 'minify' | 'mode' | 'privateKeyPath'> = {}\n  ) {\n    const Middleware = require('./middleware/ExpoGoManifestHandlerMiddleware')\n      .ExpoGoManifestHandlerMiddleware as typeof import('./middleware/ExpoGoManifestHandlerMiddleware').ExpoGoManifestHandlerMiddleware;\n\n    const urlCreator = this.getUrlCreator();\n    const middleware = new Middleware(this.projectRoot, {\n      constructUrl: urlCreator.constructUrl.bind(urlCreator),\n      mode: options.mode,\n      minify: options.minify,\n      isNativeWebpack: this.name === 'webpack' && this.isTargetingNative(),\n      privateKeyPath: options.privateKeyPath,\n    });\n    return middleware;\n  }\n\n  /** Start the dev server using settings defined in the start command. */\n  public async startAsync(options: BundlerStartOptions): Promise<DevServerInstance> {\n    await this.stopAsync();\n\n    let instance: DevServerInstance;\n    if (options.headless) {\n      instance = await this.startHeadlessAsync(options);\n    } else {\n      instance = await this.startImplementationAsync(options);\n    }\n\n    this.setInstance(instance);\n    await this.postStartAsync(options);\n    return instance;\n  }\n\n  protected abstract startImplementationAsync(\n    options: BundlerStartOptions\n  ): Promise<DevServerInstance>;\n\n  public async waitForTypeScriptAsync(): Promise<boolean> {\n    return false;\n  }\n\n  public abstract startTypeScriptServices(): Promise<void>;\n\n  public async watchEnvironmentVariables(): Promise<void> {\n    // noop -- We've only implemented this functionality in Metro.\n  }\n\n  /**\n   * Creates a mock server representation that can be used to estimate URLs for a server started in another process.\n   * This is used for the run commands where you can reuse the server from a previous run.\n   */\n  private async startHeadlessAsync(options: BundlerStartOptions): Promise<DevServerInstance> {\n    if (!options.port)\n      throw new CommandError('HEADLESS_SERVER', 'headless dev server requires a port option');\n    this.urlCreator = this.getUrlCreator(options);\n\n    return {\n      // Create a mock server\n      server: {\n        close: () => {\n          this.instance = null;\n        },\n        addListener() {},\n      },\n      location: {\n        // The port is the main thing we want to send back.\n        port: options.port,\n        // localhost isn't always correct.\n        host: 'localhost',\n        // http is the only supported protocol on native.\n        url: `http://localhost:${options.port}`,\n        protocol: 'http',\n      },\n      middleware: {},\n      messageSocket: {\n        broadcast: () => {\n          throw new CommandError('HEADLESS_SERVER', 'Cannot broadcast messages to headless server');\n        },\n      },\n    };\n  }\n\n  /**\n   * Runs after the `startAsync` function, performing any additional common operations.\n   * You can assume the dev server is started by the time this function is called.\n   */\n  protected async postStartAsync(options: BundlerStartOptions) {\n    if (\n      options.location.hostType === 'tunnel' &&\n      !env.EXPO_OFFLINE &&\n      // This is a hack to prevent using tunnel on web since we block it upstream for some reason.\n      this.isTargetingNative()\n    ) {\n      await this._startTunnelAsync();\n    }\n    await this.startDevSessionAsync();\n\n    this.watchConfig();\n  }\n\n  protected abstract getConfigModuleIds(): string[];\n\n  protected watchConfig() {\n    this.notifier?.stopObserving();\n    this.notifier = new FileNotifier(this.projectRoot, this.getConfigModuleIds());\n    this.notifier.startObserving();\n  }\n\n  /** Create ngrok instance and start the tunnel server. Exposed for testing. */\n  public async _startTunnelAsync(): Promise<AsyncNgrok | null> {\n    const port = this.getInstance()?.location.port;\n    if (!port) return null;\n    debug('[ngrok] connect to port: ' + port);\n    this.ngrok = new AsyncNgrok(this.projectRoot, port);\n    await this.ngrok.startAsync();\n    return this.ngrok;\n  }\n\n  protected async startDevSessionAsync() {\n    // This is used to make Expo Go open the project in either Expo Go, or the web browser.\n    // Must come after ngrok (`startTunnelAsync`) setup.\n    this.devSession?.stopNotifying?.();\n    this.devSession = new DevelopmentSession(\n      this.projectRoot,\n      // This URL will be used on external devices so the computer IP won't be relevant.\n      this.isTargetingNative()\n        ? this.getNativeRuntimeUrl()\n        : this.getDevServerUrl({ hostType: 'localhost' }),\n      () => {\n        // TODO: This appears to be happening consistently after an hour.\n        // We should investigate why this is happening and fix it on our servers.\n        // Log.error(\n        //   chalk.red(\n        //     '\\nAn unexpected error occurred while updating the Dev Session API. This project will not appear in the \"Development servers\" section of the Expo Go app until this process has been restarted.'\n        //   )\n        // );\n        // Log.exception(error);\n        this.devSession?.closeAsync().catch((error) => {\n          debug('[dev-session] error closing: ' + error.message);\n        });\n      }\n    );\n\n    await this.devSession.startAsync({\n      runtime: this.isTargetingNative() ? 'native' : 'web',\n    });\n  }\n\n  public isTargetingNative() {\n    // Temporary hack while we implement multi-bundler dev server proxy.\n    return true;\n  }\n\n  public isTargetingWeb() {\n    return this.platformBundlers.web === this.name;\n  }\n\n  /**\n   * Sends a message over web sockets to any connected device,\n   * does nothing when the dev server is not running.\n   *\n   * @param method name of the command. In RN projects `reload`, and `devMenu` are available. In Expo Go, `sendDevCommand` is available.\n   * @param params\n   */\n  public broadcastMessage(\n    method: 'reload' | 'devMenu' | 'sendDevCommand',\n    params?: Record<string, any>\n  ) {\n    this.getInstance()?.messageSocket.broadcast(method, params);\n  }\n\n  /** Get the running dev server instance. */\n  public getInstance() {\n    return this.instance;\n  }\n\n  /** Stop the running dev server instance. */\n  async stopAsync() {\n    // Stop file watching.\n    this.notifier?.stopObserving();\n\n    // Stop the dev session timer and tell Expo API to remove dev session.\n    await this.devSession?.closeAsync();\n\n    // Stop ngrok if running.\n    await this.ngrok?.stopAsync().catch((e) => {\n      Log.error(`Error stopping ngrok:`);\n      Log.exception(e);\n    });\n\n    return resolveWithTimeout(\n      () =>\n        new Promise<void>((resolve, reject) => {\n          // Close the server.\n          debug(`Stopping dev server (bundler: ${this.name})`);\n\n          if (this.instance?.server) {\n            this.instance.server.close((error) => {\n              debug(`Stopped dev server (bundler: ${this.name})`);\n              this.instance = null;\n              if (error) {\n                reject(error);\n              } else {\n                resolve();\n              }\n            });\n          } else {\n            debug(`Stopped dev server (bundler: ${this.name})`);\n            this.instance = null;\n            resolve();\n          }\n        }),\n      {\n        // NOTE(Bacon): Metro dev server doesn't seem to be closing in time.\n        timeout: 1000,\n        errorMessage: `Timeout waiting for '${this.name}' dev server to close`,\n      }\n    );\n  }\n\n  public getUrlCreator(options: Partial<Pick<BundlerStartOptions, 'port' | 'location'>> = {}) {\n    if (!this.urlCreator) {\n      assert(options?.port, 'Dev server instance not found');\n      this.urlCreator = new UrlCreator(options.location, {\n        port: options.port,\n        getTunnelUrl: this.getTunnelUrl.bind(this),\n      });\n    }\n    return this.urlCreator;\n  }\n\n  public getNativeRuntimeUrl(opts: Partial<CreateURLOptions> = {}) {\n    return this.isDevClient\n      ? this.getUrlCreator().constructDevClientUrl(opts) ?? this.getDevServerUrl()\n      : this.getUrlCreator().constructUrl({ ...opts, scheme: 'exp' });\n  }\n\n  /** Get the URL for the running instance of the dev server. */\n  public getDevServerUrl(options: { hostType?: 'localhost' } = {}): string | null {\n    const instance = this.getInstance();\n    if (!instance?.location) {\n      return null;\n    }\n    const { location } = instance;\n    if (options.hostType === 'localhost') {\n      return `${location.protocol}://localhost:${location.port}`;\n    }\n    return location.url ?? null;\n  }\n\n  /** Get the base URL for JS inspector */\n  public getJsInspectorBaseUrl(): string {\n    if (this.name !== 'metro') {\n      throw new CommandError(\n        'DEV_SERVER',\n        `Cannot get the JS inspector base url - bundler[${this.name}]`\n      );\n    }\n    return this.getUrlCreator().constructUrl({ scheme: 'http' });\n  }\n\n  /** Get the tunnel URL from ngrok. */\n  public getTunnelUrl(): string | null {\n    return this.ngrok?.getActiveUrl() ?? null;\n  }\n\n  /** Open the dev server in a runtime. */\n  public async openPlatformAsync(\n    launchTarget: keyof typeof PLATFORM_MANAGERS | 'desktop',\n    resolver: BaseResolveDeviceProps<any> = {}\n  ) {\n    if (launchTarget === 'desktop') {\n      const serverUrl = this.getDevServerUrl({ hostType: 'localhost' });\n      // Allow opening the tunnel URL when using Metro web.\n      const url = this.name === 'metro' ? this.getTunnelUrl() ?? serverUrl : serverUrl;\n      await openBrowserAsync(url!);\n      return { url };\n    }\n\n    const runtime = this.isTargetingNative() ? (this.isDevClient ? 'custom' : 'expo') : 'web';\n    const manager = await this.getPlatformManagerAsync(launchTarget);\n    return manager.openAsync({ runtime }, resolver);\n  }\n\n  /** Open the dev server in a runtime. */\n  public async openCustomRuntimeAsync(\n    launchTarget: keyof typeof PLATFORM_MANAGERS,\n    launchProps: Partial<BaseOpenInCustomProps> = {},\n    resolver: BaseResolveDeviceProps<any> = {}\n  ) {\n    const runtime = this.isTargetingNative() ? (this.isDevClient ? 'custom' : 'expo') : 'web';\n    if (runtime !== 'custom') {\n      throw new CommandError(\n        `dev server cannot open custom runtimes either because it does not target native platforms or because it is not targeting dev clients. (target: ${runtime})`\n      );\n    }\n\n    const manager = await this.getPlatformManagerAsync(launchTarget);\n    return manager.openAsync({ runtime: 'custom', props: launchProps }, resolver);\n  }\n\n  /** Get the URL for opening in Expo Go. */\n  protected getExpoGoUrl(): string {\n    return this.getUrlCreator().constructUrl({ scheme: 'exp' });\n  }\n\n  /** Should use the interstitial page for selecting which runtime to use. */\n  protected isRedirectPageEnabled(): boolean {\n    return (\n      !env.EXPO_NO_REDIRECT_PAGE &&\n      // if user passed --dev-client flag, skip interstitial page\n      !this.isDevClient &&\n      // Checks if dev client is installed.\n      !!resolveFrom.silent(this.projectRoot, 'expo-dev-client')\n    );\n  }\n\n  /** Get the redirect URL when redirecting is enabled. */\n  public getRedirectUrl(platform: keyof typeof PLATFORM_MANAGERS | null = null): string | null {\n    if (!this.isRedirectPageEnabled()) {\n      debug('Redirect page is disabled');\n      return null;\n    }\n\n    return (\n      this.getUrlCreator().constructLoadingUrl(\n        {},\n        platform === 'emulator' ? 'android' : platform === 'simulator' ? 'ios' : null\n      ) ?? null\n    );\n  }\n\n  public getReactDevToolsUrl(): string {\n    return new URL(\n      '_expo/react-devtools',\n      this.getUrlCreator().constructUrl({ scheme: 'http' })\n    ).toString();\n  }\n\n  protected async getPlatformManagerAsync(platform: keyof typeof PLATFORM_MANAGERS) {\n    if (!this.platformManagers[platform]) {\n      const Manager = PLATFORM_MANAGERS[platform]();\n      const port = this.getInstance()?.location.port;\n      if (!port || !this.urlCreator) {\n        throw new CommandError(\n          'DEV_SERVER',\n          'Cannot interact with native platforms until dev server has started'\n        );\n      }\n      debug(`Creating platform manager (platform: ${platform}, port: ${port})`);\n      this.platformManagers[platform] = new Manager(this.projectRoot, port, {\n        getCustomRuntimeUrl: this.urlCreator.constructDevClientUrl.bind(this.urlCreator),\n        getExpoGoUrl: this.getExpoGoUrl.bind(this),\n        getRedirectUrl: this.getRedirectUrl.bind(this, platform),\n        getDevServerUrl: this.getDevServerUrl.bind(this, { hostType: 'localhost' }),\n      });\n    }\n    return this.platformManagers[platform];\n  }\n}\n"], "names": ["Log", "debug", "require", "PLATFORM_MANAGERS", "simulator", "ApplePlatformManager", "emulator", "AndroidPlatformManager", "BundlerDevServer", "constructor", "projectRoot", "platformBundlers", "options", "ngrok", "devSession", "instance", "platformManagers", "urlCreator", "notifier", "devToolsPluginManager", "DevToolsPluginManager", "isDevClient", "setInstance", "getManifestMiddlewareAsync", "Middleware", "ExpoGoManifestHandlerMiddleware", "getUrlCreator", "middleware", "constructUrl", "bind", "mode", "minify", "isNativeWebpack", "name", "isTargetingNative", "privateKeyPath", "startAsync", "stopAsync", "headless", "startHeadlessAsync", "startImplementationAsync", "postStartAsync", "waitForTypeScriptAsync", "watchEnvironmentVariables", "port", "CommandError", "server", "close", "addListener", "location", "host", "url", "protocol", "messageSocket", "broadcast", "hostType", "env", "EXPO_OFFLINE", "_startTunnelAsync", "startDevSessionAsync", "watchConfig", "stopObserving", "FileNotifier", "getConfigModuleIds", "startObserving", "getInstance", "AsyncNgrok", "stopNotifying", "DevelopmentSession", "getNativeRuntimeUrl", "getDevServerUrl", "closeAsync", "catch", "error", "message", "runtime", "isTargetingWeb", "web", "broadcastMessage", "method", "params", "e", "exception", "resolveWithTimeout", "Promise", "resolve", "reject", "timeout", "errorMessage", "assert", "UrlCreator", "getTunnelUrl", "opts", "constructDevClientUrl", "scheme", "getJsInspectorBaseUrl", "getActiveUrl", "openPlatformAsync", "launchTarget", "resolver", "serverUrl", "openBrowserAsync", "manager", "getPlatformManagerAsync", "openAsync", "openCustomRuntimeAsync", "launchProps", "props", "getExpoGoUrl", "isRedirectPageEnabled", "EXPO_NO_REDIRECT_PAGE", "resolveFrom", "silent", "getRedirectUrl", "platform", "constructLoadingUrl", "getReactDevToolsUrl", "URL", "toString", "Manager", "getCustomRuntimeUrl"], "mappings": "AAAA;;;;AAAmB,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACH,IAAA,YAAc,kCAAd,cAAc,EAAA;AAEX,IAAA,WAAc,WAAd,cAAc,CAAA;AACP,IAAA,sBAAyB,kCAAzB,yBAAyB,EAAA;AACxB,IAAA,mBAAsB,WAAtB,sBAAsB,CAAA;AACZ,IAAA,WAAc,WAAd,cAAc,CAAA;AAE/CA,IAAAA,GAAG,mCAAM,WAAW,EAAjB;AACc,IAAA,aAA0B,WAA1B,0BAA0B,CAAA;AACpB,IAAA,MAAmB,WAAnB,mBAAmB,CAAA;AAClC,IAAA,IAAiB,WAAjB,iBAAiB,CAAA;AACR,IAAA,OAAoB,WAApB,oBAAoB,CAAA;AAChB,IAAA,KAAkB,WAAlB,kBAAkB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,6BAA6B,CAAC,AAAsB,AAAC;AA2DpF,MAAMC,iBAAiB,GAAG;IACxBC,SAAS,EAAE,IACTF,OAAO,CAAC,uCAAuC,CAAC,CAC7CG,oBAAoB;IAA+E;IACxGC,QAAQ,EAAE,IACRJ,OAAO,CAAC,6CAA6C,CAAC,CACnDK,sBAAsB;CAC5B,AAAC;AAEK,MAAeC,gBAAgB;IAmBpCC,YAESC,WAAmB,EAEnBC,gBAAkC,EACzC,uBAAuB,CACvBC,OAQC,CACD;aAbOF,WAAmB,GAAnBA,WAAmB;aAEnBC,gBAAkC,GAAlCA,gBAAkC;aAlBjCE,KAAK,GAAsB,IAAI;aAE/BC,UAAU,GAA8B,IAAI;aAE5CC,QAAQ,GAA6B,IAAI;aAE3CC,gBAAgB,GAAyC,EAAE;aAEzDC,UAAU,GAAuB,IAAI;aAEvCC,QAAQ,GAAwB,IAAI;YAqBxCN,GAA8B;QADhC,IAAI,CAACO,qBAAqB,GACxBP,CAAAA,GAA8B,GAA9BA,OAAO,QAAuB,GAA9BA,KAAAA,CAA8B,GAA9BA,OAAO,CAAEO,qBAAqB,YAA9BP,GAA8B,GAAI,IAAIQ,sBAAqB,QAAA,CAACV,WAAW,CAAC,CAAC;YACxDE,IAAoB;QAAvC,IAAI,CAACS,WAAW,GAAGT,CAAAA,IAAoB,GAApBA,OAAO,QAAa,GAApBA,KAAAA,CAAoB,GAApBA,OAAO,CAAES,WAAW,YAApBT,IAAoB,GAAI,KAAK,CAAC;KAClD;IAED,AAAUU,WAAW,CAACP,QAA2B,EAAE;QACjD,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAAC;KAC1B;IAED,4CAA4C,CAC5C,MAAgBQ,0BAA0B,CACxCX,OAAwE,GAAG,EAAE,EAC7E;QACA,MAAMY,UAAU,GAAGtB,OAAO,CAAC,8CAA8C,CAAC,CACvEuB,+BAA+B,AAAiG,AAAC;QAEpI,MAAMR,UAAU,GAAG,IAAI,CAACS,aAAa,EAAE,AAAC;QACxC,MAAMC,UAAU,GAAG,IAAIH,UAAU,CAAC,IAAI,CAACd,WAAW,EAAE;YAClDkB,YAAY,EAAEX,UAAU,CAACW,YAAY,CAACC,IAAI,CAACZ,UAAU,CAAC;YACtDa,IAAI,EAAElB,OAAO,CAACkB,IAAI;YAClBC,MAAM,EAAEnB,OAAO,CAACmB,MAAM;YACtBC,eAAe,EAAE,IAAI,CAACC,IAAI,KAAK,SAAS,IAAI,IAAI,CAACC,iBAAiB,EAAE;YACpEC,cAAc,EAAEvB,OAAO,CAACuB,cAAc;SACvC,CAAC,AAAC;QACH,OAAOR,UAAU,CAAC;KACnB;IAED,wEAAwE,CACxE,MAAaS,UAAU,CAACxB,OAA4B,EAA8B;QAChF,MAAM,IAAI,CAACyB,SAAS,EAAE,CAAC;QAEvB,IAAItB,QAAQ,AAAmB,AAAC;QAChC,IAAIH,OAAO,CAAC0B,QAAQ,EAAE;YACpBvB,QAAQ,GAAG,MAAM,IAAI,CAACwB,kBAAkB,CAAC3B,OAAO,CAAC,CAAC;SACnD,MAAM;YACLG,QAAQ,GAAG,MAAM,IAAI,CAACyB,wBAAwB,CAAC5B,OAAO,CAAC,CAAC;SACzD;QAED,IAAI,CAACU,WAAW,CAACP,QAAQ,CAAC,CAAC;QAC3B,MAAM,IAAI,CAAC0B,cAAc,CAAC7B,OAAO,CAAC,CAAC;QACnC,OAAOG,QAAQ,CAAC;KACjB;IAMD,MAAa2B,sBAAsB,GAAqB;QACtD,OAAO,KAAK,CAAC;KACd;IAID,MAAaC,yBAAyB,GAAkB;IACtD,8DAA8D;KAC/D;IAED;;;KAGG,CACH,MAAcJ,kBAAkB,CAAC3B,OAA4B,EAA8B;QACzF,IAAI,CAACA,OAAO,CAACgC,IAAI,EACf,MAAM,IAAIC,OAAY,aAAA,CAAC,iBAAiB,EAAE,4CAA4C,CAAC,CAAC;QAC1F,IAAI,CAAC5B,UAAU,GAAG,IAAI,CAACS,aAAa,CAACd,OAAO,CAAC,CAAC;QAE9C,OAAO;YACL,uBAAuB;YACvBkC,MAAM,EAAE;gBACNC,KAAK,EAAE,IAAM;oBACX,IAAI,CAAChC,QAAQ,GAAG,IAAI,CAAC;iBACtB;gBACDiC,WAAW,IAAG,EAAE;aACjB;YACDC,QAAQ,EAAE;gBACR,mDAAmD;gBACnDL,IAAI,EAAEhC,OAAO,CAACgC,IAAI;gBAClB,kCAAkC;gBAClCM,IAAI,EAAE,WAAW;gBACjB,iDAAiD;gBACjDC,GAAG,EAAE,CAAC,iBAAiB,EAAEvC,OAAO,CAACgC,IAAI,CAAC,CAAC;gBACvCQ,QAAQ,EAAE,MAAM;aACjB;YACDzB,UAAU,EAAE,EAAE;YACd0B,aAAa,EAAE;gBACbC,SAAS,EAAE,IAAM;oBACf,MAAM,IAAIT,OAAY,aAAA,CAAC,iBAAiB,EAAE,8CAA8C,CAAC,CAAC;iBAC3F;aACF;SACF,CAAC;KACH;IAED;;;KAGG,CACH,MAAgBJ,cAAc,CAAC7B,OAA4B,EAAE;QAC3D,IACEA,OAAO,CAACqC,QAAQ,CAACM,QAAQ,KAAK,QAAQ,IACtC,CAACC,IAAG,IAAA,CAACC,YAAY,IACjB,4FAA4F;QAC5F,IAAI,CAACvB,iBAAiB,EAAE,EACxB;YACA,MAAM,IAAI,CAACwB,iBAAiB,EAAE,CAAC;SAChC;QACD,MAAM,IAAI,CAACC,oBAAoB,EAAE,CAAC;QAElC,IAAI,CAACC,WAAW,EAAE,CAAC;KACpB;IAID,AAAUA,WAAW,GAAG;YACtB,GAAa;QAAb,CAAA,GAAa,GAAb,IAAI,CAAC1C,QAAQ,SAAe,GAA5B,KAAA,CAA4B,GAA5B,GAAa,CAAE2C,aAAa,EAAE,AA5OlC,CA4OmC;QAC/B,IAAI,CAAC3C,QAAQ,GAAG,IAAI4C,aAAY,aAAA,CAAC,IAAI,CAACpD,WAAW,EAAE,IAAI,CAACqD,kBAAkB,EAAE,CAAC,CAAC;QAC9E,IAAI,CAAC7C,QAAQ,CAAC8C,cAAc,EAAE,CAAC;KAChC;IAED,8EAA8E,CAC9E,MAAaN,iBAAiB,GAA+B;YAC9C,GAAkB;QAA/B,MAAMd,IAAI,GAAG,CAAA,GAAkB,GAAlB,IAAI,CAACqB,WAAW,EAAE,SAAU,GAA5B,KAAA,CAA4B,GAA5B,GAAkB,CAAEhB,QAAQ,CAACL,IAAI,AAAC;QAC/C,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI,CAAC;QACvB3C,KAAK,CAAC,2BAA2B,GAAG2C,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC/B,KAAK,GAAG,IAAIqD,WAAU,WAAA,CAAC,IAAI,CAACxD,WAAW,EAAEkC,IAAI,CAAC,CAAC;QACpD,MAAM,IAAI,CAAC/B,KAAK,CAACuB,UAAU,EAAE,CAAC;QAC9B,OAAO,IAAI,CAACvB,KAAK,CAAC;KACnB;IAED,MAAgB8C,oBAAoB,GAAG;YACrC,uFAAuF;QACvF,oDAAoD;QACpD,IAAe;QAAf,CAAA,IAAe,GAAf,IAAI,CAAC7C,UAAU,SAAe,GAA9B,KAAA,CAA8B,GAA9B,IAAe,CAAEqD,aAAa,QAAI,GAAlC,KAAA,CAAkC,GAAlC,IAAe,CAAEA,aAAa,EAAI,AA9PtC,CA8PuC;QACnC,IAAI,CAACrD,UAAU,GAAG,IAAIsD,mBAAkB,mBAAA,CACtC,IAAI,CAAC1D,WAAW,EAChB,kFAAkF;QAClF,IAAI,CAACwB,iBAAiB,EAAE,GACpB,IAAI,CAACmC,mBAAmB,EAAE,GAC1B,IAAI,CAACC,eAAe,CAAC;YAAEf,QAAQ,EAAE,WAAW;SAAE,CAAC,EACnD,IAAM;gBACJ,iEAAiE;YACjE,yEAAyE;YACzE,aAAa;YACb,eAAe;YACf,uMAAuM;YACvM,MAAM;YACN,KAAK;YACL,wBAAwB;YACxB,GAAe;YAAf,CAAA,GAAe,GAAf,IAAI,CAACzC,UAAU,SAAY,GAA3B,KAAA,CAA2B,GAA3B,GAAe,CAAEyD,UAAU,EAAE,CAACC,KAAK,CAAC,CAACC,KAAK,GAAK;gBAC7CxE,KAAK,CAAC,+BAA+B,GAAGwE,KAAK,CAACC,OAAO,CAAC,CAAC;aACxD,CAAC,CAAC;SACJ,CACF,CAAC;QAEF,MAAM,IAAI,CAAC5D,UAAU,CAACsB,UAAU,CAAC;YAC/BuC,OAAO,EAAE,IAAI,CAACzC,iBAAiB,EAAE,GAAG,QAAQ,GAAG,KAAK;SACrD,CAAC,CAAC;KACJ;IAED,AAAOA,iBAAiB,GAAG;QACzB,oEAAoE;QACpE,OAAO,IAAI,CAAC;KACb;IAED,AAAO0C,cAAc,GAAG;QACtB,OAAO,IAAI,CAACjE,gBAAgB,CAACkE,GAAG,KAAK,IAAI,CAAC5C,IAAI,CAAC;KAChD;IAED;;;;;;KAMG,CACH,AAAO6C,gBAAgB,CACrBC,MAA+C,EAC/CC,MAA4B,EAC5B;YACA,GAAkB;QAAlB,CAAA,GAAkB,GAAlB,IAAI,CAACf,WAAW,EAAE,SAAe,GAAjC,KAAA,CAAiC,GAAjC,GAAkB,CAAEZ,aAAa,CAACC,SAAS,CAACyB,MAAM,EAAEC,MAAM,CAAC,CAAC;KAC7D;IAED,2CAA2C,CAC3C,AAAOf,WAAW,GAAG;QACnB,OAAO,IAAI,CAAClD,QAAQ,CAAC;KACtB;IAED,4CAA4C,CAC5C,MAAMsB,SAAS,GAAG;YAChB,sBAAsB;QACtB,IAAa,EAGP,IAAe,EAGf,IAAU;QANhB,CAAA,IAAa,GAAb,IAAI,CAACnB,QAAQ,SAAe,GAA5B,KAAA,CAA4B,GAA5B,IAAa,CAAE2C,aAAa,EAAE,AAxTlC,CAwTmC;QAE/B,sEAAsE;QACtE,OAAM,CAAA,IAAe,GAAf,IAAI,CAAC/C,UAAU,SAAY,GAA3B,KAAA,CAA2B,GAA3B,IAAe,CAAEyD,UAAU,EAAE,CAAA,CAAC;QAEpC,yBAAyB;QACzB,MAAM,CAAA,CAAA,IAAU,GAAV,IAAI,CAAC1D,KAAK,SAAW,GAArB,KAAA,CAAqB,GAArB,IAAU,CAAEwB,SAAS,EAAE,CAACmC,KAAK,CAAC,CAACS,CAAC,GAAK;YACzCjF,GAAG,CAACyE,KAAK,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC;YACnCzE,GAAG,CAACkF,SAAS,CAACD,CAAC,CAAC,CAAC;SAClB,CAAC,CAAA,CAAC;QAEH,OAAOE,CAAAA,GAAAA,MAAkB,AA2BxB,CAAA,mBA3BwB,CACvB;YACE,OAAA,IAAIC,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,GAAK;oBAIjC,GAAa;gBAHjB,oBAAoB;gBACpBrF,KAAK,CAAC,CAAC,8BAA8B,EAAE,IAAI,CAACgC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBAErD,IAAI,CAAA,GAAa,GAAb,IAAI,CAAClB,QAAQ,SAAQ,GAArB,KAAA,CAAqB,GAArB,GAAa,CAAE+B,MAAM,EAAE;oBACzB,IAAI,CAAC/B,QAAQ,CAAC+B,MAAM,CAACC,KAAK,CAAC,CAAC0B,KAAK,GAAK;wBACpCxE,KAAK,CAAC,CAAC,6BAA6B,EAAE,IAAI,CAACgC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;wBACpD,IAAI,CAAClB,QAAQ,GAAG,IAAI,CAAC;wBACrB,IAAI0D,KAAK,EAAE;4BACTa,MAAM,CAACb,KAAK,CAAC,CAAC;yBACf,MAAM;4BACLY,OAAO,EAAE,CAAC;yBACX;qBACF,CAAC,CAAC;iBACJ,MAAM;oBACLpF,KAAK,CAAC,CAAC,6BAA6B,EAAE,IAAI,CAACgC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpD,IAAI,CAAClB,QAAQ,GAAG,IAAI,CAAC;oBACrBsE,OAAO,EAAE,CAAC;iBACX;aACF,CAAC,CAAA;SAAA,EACJ;YACE,oEAAoE;YACpEE,OAAO,EAAE,IAAI;YACbC,YAAY,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAACvD,IAAI,CAAC,qBAAqB,CAAC;SACvE,CACF,CAAC;KACH;IAED,AAAOP,aAAa,CAACd,OAAgE,GAAG,EAAE,EAAE;QAC1F,IAAI,CAAC,IAAI,CAACK,UAAU,EAAE;YACpBwE,CAAAA,GAAAA,OAAM,AAAgD,CAAA,QAAhD,CAAC7E,OAAO,QAAM,GAAbA,KAAAA,CAAa,GAAbA,OAAO,CAAEgC,IAAI,EAAE,+BAA+B,CAAC,CAAC;YACvD,IAAI,CAAC3B,UAAU,GAAG,IAAIyE,WAAU,WAAA,CAAC9E,OAAO,CAACqC,QAAQ,EAAE;gBACjDL,IAAI,EAAEhC,OAAO,CAACgC,IAAI;gBAClB+C,YAAY,EAAE,IAAI,CAACA,YAAY,CAAC9D,IAAI,CAAC,IAAI,CAAC;aAC3C,CAAC,CAAC;SACJ;QACD,OAAO,IAAI,CAACZ,UAAU,CAAC;KACxB;IAED,AAAOoD,mBAAmB,CAACuB,IAA+B,GAAG,EAAE,EAAE;YAE3D,GAAgD;QADpD,OAAO,IAAI,CAACvE,WAAW,GACnB,CAAA,GAAgD,GAAhD,IAAI,CAACK,aAAa,EAAE,CAACmE,qBAAqB,CAACD,IAAI,CAAC,YAAhD,GAAgD,GAAI,IAAI,CAACtB,eAAe,EAAE,GAC1E,IAAI,CAAC5C,aAAa,EAAE,CAACE,YAAY,CAAC;YAAE,GAAGgE,IAAI;YAAEE,MAAM,EAAE,KAAK;SAAE,CAAC,CAAC;KACnE;IAED,8DAA8D,CAC9D,AAAOxB,eAAe,CAAC1D,OAAmC,GAAG,EAAE,EAAiB;QAC9E,MAAMG,QAAQ,GAAG,IAAI,CAACkD,WAAW,EAAE,AAAC;QACpC,IAAI,CAAClD,CAAAA,QAAQ,QAAU,GAAlBA,KAAAA,CAAkB,GAAlBA,QAAQ,CAAEkC,QAAQ,CAAA,EAAE;YACvB,OAAO,IAAI,CAAC;SACb;QACD,MAAM,EAAEA,QAAQ,CAAA,EAAE,GAAGlC,QAAQ,AAAC;QAC9B,IAAIH,OAAO,CAAC2C,QAAQ,KAAK,WAAW,EAAE;YACpC,OAAO,CAAC,EAAEN,QAAQ,CAACG,QAAQ,CAAC,aAAa,EAAEH,QAAQ,CAACL,IAAI,CAAC,CAAC,CAAC;SAC5D;YACMK,IAAY;QAAnB,OAAOA,CAAAA,IAAY,GAAZA,QAAQ,CAACE,GAAG,YAAZF,IAAY,GAAI,IAAI,CAAC;KAC7B;IAED,wCAAwC,CACxC,AAAO8C,qBAAqB,GAAW;QACrC,IAAI,IAAI,CAAC9D,IAAI,KAAK,OAAO,EAAE;YACzB,MAAM,IAAIY,OAAY,aAAA,CACpB,YAAY,EACZ,CAAC,+CAA+C,EAAE,IAAI,CAACZ,IAAI,CAAC,CAAC,CAAC,CAC/D,CAAC;SACH;QACD,OAAO,IAAI,CAACP,aAAa,EAAE,CAACE,YAAY,CAAC;YAAEkE,MAAM,EAAE,MAAM;SAAE,CAAC,CAAC;KAC9D;IAED,qCAAqC,CACrC,AAAOH,YAAY,GAAkB;YAC5B,GAAU;YAAV,IAA0B;QAAjC,OAAO,CAAA,IAA0B,GAA1B,CAAA,GAAU,GAAV,IAAI,CAAC9E,KAAK,SAAc,GAAxB,KAAA,CAAwB,GAAxB,GAAU,CAAEmF,YAAY,EAAE,YAA1B,IAA0B,GAAI,IAAI,CAAC;KAC3C;IAED,wCAAwC,CACxC,MAAaC,iBAAiB,CAC5BC,YAAwD,EACxDC,QAAqC,GAAG,EAAE,EAC1C;QACA,IAAID,YAAY,KAAK,SAAS,EAAE;YAC9B,MAAME,SAAS,GAAG,IAAI,CAAC9B,eAAe,CAAC;gBAAEf,QAAQ,EAAE,WAAW;aAAE,CAAC,AAAC;gBAE9B,GAAmB;YADvD,qDAAqD;YACrD,MAAMJ,GAAG,GAAG,IAAI,CAAClB,IAAI,KAAK,OAAO,GAAG,CAAA,GAAmB,GAAnB,IAAI,CAAC0D,YAAY,EAAE,YAAnB,GAAmB,GAAIS,SAAS,GAAGA,SAAS,AAAC;YACjF,MAAMC,CAAAA,GAAAA,KAAgB,AAAM,CAAA,iBAAN,CAAClD,GAAG,CAAE,CAAC;YAC7B,OAAO;gBAAEA,GAAG;aAAE,CAAC;SAChB;QAED,MAAMwB,OAAO,GAAG,IAAI,CAACzC,iBAAiB,EAAE,GAAI,IAAI,CAACb,WAAW,GAAG,QAAQ,GAAG,MAAM,GAAI,KAAK,AAAC;QAC1F,MAAMiF,OAAO,GAAG,MAAM,IAAI,CAACC,uBAAuB,CAACL,YAAY,CAAC,AAAC;QACjE,OAAOI,OAAO,CAACE,SAAS,CAAC;YAAE7B,OAAO;SAAE,EAAEwB,QAAQ,CAAC,CAAC;KACjD;IAED,wCAAwC,CACxC,MAAaM,sBAAsB,CACjCP,YAA4C,EAC5CQ,WAA2C,GAAG,EAAE,EAChDP,QAAqC,GAAG,EAAE,EAC1C;QACA,MAAMxB,OAAO,GAAG,IAAI,CAACzC,iBAAiB,EAAE,GAAI,IAAI,CAACb,WAAW,GAAG,QAAQ,GAAG,MAAM,GAAI,KAAK,AAAC;QAC1F,IAAIsD,OAAO,KAAK,QAAQ,EAAE;YACxB,MAAM,IAAI9B,OAAY,aAAA,CACpB,CAAC,+IAA+I,EAAE8B,OAAO,CAAC,CAAC,CAAC,CAC7J,CAAC;SACH;QAED,MAAM2B,OAAO,GAAG,MAAM,IAAI,CAACC,uBAAuB,CAACL,YAAY,CAAC,AAAC;QACjE,OAAOI,OAAO,CAACE,SAAS,CAAC;YAAE7B,OAAO,EAAE,QAAQ;YAAEgC,KAAK,EAAED,WAAW;SAAE,EAAEP,QAAQ,CAAC,CAAC;KAC/E;IAED,0CAA0C,CAC1C,AAAUS,YAAY,GAAW;QAC/B,OAAO,IAAI,CAAClF,aAAa,EAAE,CAACE,YAAY,CAAC;YAAEkE,MAAM,EAAE,KAAK;SAAE,CAAC,CAAC;KAC7D;IAED,2EAA2E,CAC3E,AAAUe,qBAAqB,GAAY;QACzC,OACE,CAACrD,IAAG,IAAA,CAACsD,qBAAqB,IAC1B,2DAA2D;QAC3D,CAAC,IAAI,CAACzF,WAAW,IACjB,qCAAqC;QACrC,CAAC,CAAC0F,YAAW,QAAA,CAACC,MAAM,CAAC,IAAI,CAACtG,WAAW,EAAE,iBAAiB,CAAC,CACzD;KACH;IAED,wDAAwD,CACxD,AAAOuG,cAAc,CAACC,QAA+C,GAAG,IAAI,EAAiB;QAC3F,IAAI,CAAC,IAAI,CAACL,qBAAqB,EAAE,EAAE;YACjC5G,KAAK,CAAC,2BAA2B,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC;SACb;YAGC,GAGC;QAJH,OACE,CAAA,GAGC,GAHD,IAAI,CAACyB,aAAa,EAAE,CAACyF,mBAAmB,CACtC,EAAE,EACFD,QAAQ,KAAK,UAAU,GAAG,SAAS,GAAGA,QAAQ,KAAK,WAAW,GAAG,KAAK,GAAG,IAAI,CAC9E,YAHD,GAGC,GAAI,IAAI,CACT;KACH;IAED,AAAOE,mBAAmB,GAAW;QACnC,OAAO,IAAIC,GAAG,CACZ,sBAAsB,EACtB,IAAI,CAAC3F,aAAa,EAAE,CAACE,YAAY,CAAC;YAAEkE,MAAM,EAAE,MAAM;SAAE,CAAC,CACtD,CAACwB,QAAQ,EAAE,CAAC;KACd;IAED,MAAgBf,uBAAuB,CAACW,QAAwC,EAAE;QAChF,IAAI,CAAC,IAAI,CAAClG,gBAAgB,CAACkG,QAAQ,CAAC,EAAE;gBAEvB,GAAkB;YAD/B,MAAMK,OAAO,GAAGpH,iBAAiB,CAAC+G,QAAQ,CAAC,EAAE,AAAC;YAC9C,MAAMtE,IAAI,GAAG,CAAA,GAAkB,GAAlB,IAAI,CAACqB,WAAW,EAAE,SAAU,GAA5B,KAAA,CAA4B,GAA5B,GAAkB,CAAEhB,QAAQ,CAACL,IAAI,AAAC;YAC/C,IAAI,CAACA,IAAI,IAAI,CAAC,IAAI,CAAC3B,UAAU,EAAE;gBAC7B,MAAM,IAAI4B,OAAY,aAAA,CACpB,YAAY,EACZ,oEAAoE,CACrE,CAAC;aACH;YACD5C,KAAK,CAAC,CAAC,qCAAqC,EAAEiH,QAAQ,CAAC,QAAQ,EAAEtE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1E,IAAI,CAAC5B,gBAAgB,CAACkG,QAAQ,CAAC,GAAG,IAAIK,OAAO,CAAC,IAAI,CAAC7G,WAAW,EAAEkC,IAAI,EAAE;gBACpE4E,mBAAmB,EAAE,IAAI,CAACvG,UAAU,CAAC4E,qBAAqB,CAAChE,IAAI,CAAC,IAAI,CAACZ,UAAU,CAAC;gBAChF2F,YAAY,EAAE,IAAI,CAACA,YAAY,CAAC/E,IAAI,CAAC,IAAI,CAAC;gBAC1CoF,cAAc,EAAE,IAAI,CAACA,cAAc,CAACpF,IAAI,CAAC,IAAI,EAAEqF,QAAQ,CAAC;gBACxD5C,eAAe,EAAE,IAAI,CAACA,eAAe,CAACzC,IAAI,CAAC,IAAI,EAAE;oBAAE0B,QAAQ,EAAE,WAAW;iBAAE,CAAC;aAC5E,CAAC,CAAC;SACJ;QACD,OAAO,IAAI,CAACvC,gBAAgB,CAACkG,QAAQ,CAAC,CAAC;KACxC;CACF;QApZqB1G,gBAAgB,GAAhBA,gBAAgB"}