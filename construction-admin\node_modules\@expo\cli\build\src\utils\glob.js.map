{"version": 3, "sources": ["../../../src/utils/glob.ts"], "sourcesContent": ["import G, { Glob } from 'glob';\n\n/** Finds all matching files. */\nexport function everyMatchAsync(pattern: string, options: G.IOptions) {\n  return new Promise<string[]>((resolve, reject) => {\n    const g = new Glob(pattern, options);\n    let called = false;\n    const callback = (er: Error | null, matched: string[]) => {\n      if (called) return;\n      called = true;\n      if (er) reject(er);\n      else resolve(matched);\n    };\n    g.on('error', callback);\n    g.on('end', (matches) => callback(null, matches));\n  });\n}\n\n/** Bails out early after finding the first matching file. */\nexport function anyMatchAsync(pattern: string, options: G.IOptions) {\n  return new Promise<string[]>((resolve, reject) => {\n    const g = new Glob(pattern, options);\n    let called = false;\n    const callback = (er: Error | null, matched: string[]) => {\n      if (called) return;\n      called = true;\n      if (er) reject(er);\n      else resolve(matched);\n    };\n    g.on('error', callback);\n    g.on('match', (matched) => {\n      // We've disabled using abort as it breaks the entire glob package across all instances.\n      // https://github.com/isaacs/node-glob/issues/279 & https://github.com/isaacs/node-glob/issues/342\n      // For now, just collect every match.\n      // g.abort();\n      callback(null, [matched]);\n    });\n    g.on('end', (matches) => callback(null, matches));\n  });\n}\n\n/**\n * Wait some time, then escape...\n * Adding this because glob can sometimes freeze and fail to resolve if any other glob uses `.abort()`.\n */\nexport function wrapGlobWithTimeout(\n  query: () => Promise<string[]>,\n  duration: number\n): Promise<string[] | false> {\n  return new Promise(async (resolve, reject) => {\n    const timeout = setTimeout(() => {\n      resolve(false);\n    }, duration);\n\n    process.on('SIGINT', () => clearTimeout(timeout));\n\n    try {\n      resolve(await query());\n    } catch (error) {\n      reject(error);\n    } finally {\n      clearTimeout(timeout);\n    }\n  });\n}\n"], "names": ["everyMatchAsync", "anyMatchAsync", "wrapGlobWithTimeout", "pattern", "options", "Promise", "resolve", "reject", "g", "Glob", "called", "callback", "er", "matched", "on", "matches", "query", "duration", "timeout", "setTimeout", "process", "clearTimeout", "error"], "mappings": "AAAA;;;;QAGgBA,eAAe,GAAfA,eAAe;QAgBfC,aAAa,GAAbA,aAAa;QA0BbC,mBAAmB,GAAnBA,mBAAmB;AA7CX,IAAA,KAAM,WAAN,MAAM,CAAA;AAGvB,SAASF,eAAe,CAACG,OAAe,EAAEC,OAAmB,EAAE;IACpE,OAAO,IAAIC,OAAO,CAAW,CAACC,OAAO,EAAEC,MAAM,GAAK;QAChD,MAAMC,CAAC,GAAG,IAAIC,KAAI,KAAA,CAACN,OAAO,EAAEC,OAAO,CAAC,AAAC;QACrC,IAAIM,MAAM,GAAG,KAAK,AAAC;QACnB,MAAMC,QAAQ,GAAG,CAACC,EAAgB,EAAEC,OAAiB,GAAK;YACxD,IAAIH,MAAM,EAAE,OAAO;YACnBA,MAAM,GAAG,IAAI,CAAC;YACd,IAAIE,EAAE,EAAEL,MAAM,CAACK,EAAE,CAAC,CAAC;iBACdN,OAAO,CAACO,OAAO,CAAC,CAAC;SACvB,AAAC;QACFL,CAAC,CAACM,EAAE,CAAC,OAAO,EAAEH,QAAQ,CAAC,CAAC;QACxBH,CAAC,CAACM,EAAE,CAAC,KAAK,EAAE,CAACC,OAAO,GAAKJ,QAAQ,CAAC,IAAI,EAAEI,OAAO,CAAC;QAAA,CAAC,CAAC;KACnD,CAAC,CAAC;CACJ;AAGM,SAASd,aAAa,CAACE,OAAe,EAAEC,OAAmB,EAAE;IAClE,OAAO,IAAIC,OAAO,CAAW,CAACC,OAAO,EAAEC,MAAM,GAAK;QAChD,MAAMC,CAAC,GAAG,IAAIC,KAAI,KAAA,CAACN,OAAO,EAAEC,OAAO,CAAC,AAAC;QACrC,IAAIM,MAAM,GAAG,KAAK,AAAC;QACnB,MAAMC,QAAQ,GAAG,CAACC,EAAgB,EAAEC,OAAiB,GAAK;YACxD,IAAIH,MAAM,EAAE,OAAO;YACnBA,MAAM,GAAG,IAAI,CAAC;YACd,IAAIE,EAAE,EAAEL,MAAM,CAACK,EAAE,CAAC,CAAC;iBACdN,OAAO,CAACO,OAAO,CAAC,CAAC;SACvB,AAAC;QACFL,CAAC,CAACM,EAAE,CAAC,OAAO,EAAEH,QAAQ,CAAC,CAAC;QACxBH,CAAC,CAACM,EAAE,CAAC,OAAO,EAAE,CAACD,OAAO,GAAK;YACzB,wFAAwF;YACxF,kGAAkG;YAClG,qCAAqC;YACrC,aAAa;YACbF,QAAQ,CAAC,IAAI,EAAE;gBAACE,OAAO;aAAC,CAAC,CAAC;SAC3B,CAAC,CAAC;QACHL,CAAC,CAACM,EAAE,CAAC,KAAK,EAAE,CAACC,OAAO,GAAKJ,QAAQ,CAAC,IAAI,EAAEI,OAAO,CAAC;QAAA,CAAC,CAAC;KACnD,CAAC,CAAC;CACJ;AAMM,SAASb,mBAAmB,CACjCc,KAA8B,EAC9BC,QAAgB,EACW;IAC3B,OAAO,IAAIZ,OAAO,CAAC,OAAOC,OAAO,EAAEC,MAAM,GAAK;QAC5C,MAAMW,OAAO,GAAGC,UAAU,CAAC,IAAM;YAC/Bb,OAAO,CAAC,KAAK,CAAC,CAAC;SAChB,EAAEW,QAAQ,CAAC,AAAC;QAEbG,OAAO,CAACN,EAAE,CAAC,QAAQ,EAAE,IAAMO,YAAY,CAACH,OAAO,CAAC;QAAA,CAAC,CAAC;QAElD,IAAI;YACFZ,OAAO,CAAC,MAAMU,KAAK,EAAE,CAAC,CAAC;SACxB,CAAC,OAAOM,KAAK,EAAE;YACdf,MAAM,CAACe,KAAK,CAAC,CAAC;SACf,QAAS;YACRD,YAAY,CAACH,OAAO,CAAC,CAAC;SACvB;KACF,CAAC,CAAC;CACJ"}