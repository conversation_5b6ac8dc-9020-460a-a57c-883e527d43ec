{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/InspectorDevice.ts"], "sourcesContent": ["import type { unstable_Device } from '@react-native/dev-middleware';\nimport fetch from 'node-fetch';\nimport type WS from 'ws';\n\nimport { NetworkResponseHandler } from './inspectorHandlers/NetworkResponse';\nimport { PageReloadHandler } from './inspectorHandlers/PageReload';\nimport { VscodeDebuggerGetPossibleBreakpointsHandler } from './inspectorHandlers/VscodeDebuggerGetPossibleBreakpoints';\nimport { VscodeDebuggerScriptParsedHandler } from './inspectorHandlers/VscodeDebuggerScriptParsed';\nimport { VscodeDebuggerSetBreakpointByUrlHandler } from './inspectorHandlers/VscodeDebuggerSetBreakpointByUrl';\nimport { VscodeRuntimeCallFunctionOnHandler } from './inspectorHandlers/VscodeRuntimeCallFunctionOn';\nimport { VscodeRuntimeGetPropertiesHandler } from './inspectorHandlers/VscodeRuntimeGetProperties';\nimport { DebuggerMetadata, DeviceRequest, InspectorHandler } from './inspectorHandlers/types';\nimport { type MetroBundlerDevServer } from '../MetroBundlerDevServer';\n\nexport function createInspectorDeviceClass(\n  metroBundler: MetroBundlerDevServer,\n  MetroDeviceClass: typeof unstable_Device\n): typeof unstable_Device {\n  return class ExpoInspectorDevice extends MetroDeviceClass implements InspectorHandler {\n    /** All handlers that should be used to intercept or reply to CDP events */\n    public handlers: InspectorHandler[] = [\n      // Generic handlers\n      new NetworkResponseHandler(),\n      new PageReloadHandler(metroBundler),\n      // Vscode-specific handlers\n      new VscodeDebuggerGetPossibleBreakpointsHandler(),\n      new VscodeDebuggerScriptParsedHandler(this),\n      new VscodeDebuggerSetBreakpointByUrlHandler(),\n      new VscodeRuntimeGetPropertiesHandler(),\n      new VscodeRuntimeCallFunctionOnHandler(),\n    ];\n\n    onDeviceMessage(message: any, info: DebuggerMetadata): boolean {\n      return this.handlers.some((handler) => handler.onDeviceMessage?.(message, info) ?? false);\n    }\n\n    onDebuggerMessage(message: any, info: DebuggerMetadata): boolean {\n      return this.handlers.some((handler) => handler.onDebuggerMessage?.(message, info) ?? false);\n    }\n\n    /** Hook into the message life cycle to answer more complex CDP messages */\n    async _processMessageFromDevice(message: DeviceRequest<any>, info: DebuggerMetadata) {\n      if (!this.onDeviceMessage(message, info)) {\n        await super._processMessageFromDevice(message, info);\n      }\n    }\n\n    /** Hook into the message life cycle to answer more complex CDP messages */\n    _interceptMessageFromDebugger(\n      request: Parameters<unstable_Device['_interceptMessageFromDebugger']>[0],\n      info: DebuggerMetadata,\n      socket: WS\n    ): boolean {\n      // Note, `socket` is the exact same as `info.socket`\n      if (this.onDebuggerMessage(request, info)) {\n        return true;\n      }\n\n      return super._interceptMessageFromDebugger(request, info, socket);\n    }\n\n    /**\n     * Overwrite the default text fetcher, to load sourcemaps from sources other than `localhost`.\n     * @todo Cedric: remove the custom `DebuggerScriptSource` handler when switching over to `metro@>=0.75.1`\n     * @see https://github.com/facebook/metro/blob/77f445f1bcd2264ad06174dbf8d542bc75834d29/packages/metro-inspector-proxy/src/Device.js#L573-L588\n     * @since metro-inspector-proxy@0.75.1\n     */\n    async _fetchText(url: URL): Promise<string> {\n      const LENGTH_LIMIT_BYTES = 350_000_000; // 350mb\n\n      const response = await fetch(url);\n      if (!response.ok) {\n        throw new Error(`Received status ${response.status} while fetching: ${url}`);\n      }\n\n      const contentLength = response.headers.get('Content-Length');\n      if (contentLength && Number(contentLength) > LENGTH_LIMIT_BYTES) {\n        throw new Error('Expected file size is too large (more than 350mb)');\n      }\n\n      const text = await response.text();\n      if (Buffer.byteLength(text, 'utf8') > LENGTH_LIMIT_BYTES) {\n        throw new Error('File size is too large (more than 350mb)');\n      }\n\n      return text;\n    }\n  };\n}\n"], "names": ["createInspectorDeviceClass", "metroBundler", "MetroDeviceClass", "ExpoInspectorDevice", "handlers", "NetworkResponseHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "VscodeDebuggerGetPossibleBreakpointsHandler", "VscodeDebuggerScriptParsedHandler", "VscodeDebuggerSetBreakpointByUrlHandler", "VscodeRuntimeGetPropertiesHandler", "VscodeRuntimeCallFunctionOnHandler", "onDeviceMessage", "message", "info", "handler", "some", "onDebuggerMessage", "_processMessageFromDevice", "_interceptMessageFromDebugger", "request", "socket", "_fetchText", "url", "LENGTH_LIMIT_BYTES", "response", "fetch", "ok", "Error", "status", "contentLength", "headers", "get", "Number", "text", "<PERSON><PERSON><PERSON>", "byteLength"], "mappings": "AAAA;;;;QAcgBA,0BAA0B,GAA1BA,0BAA0B;AAbxB,IAAA,UAAY,kCAAZ,YAAY,EAAA;AAGS,IAAA,gBAAqC,WAArC,qCAAqC,CAAA;AAC1C,IAAA,WAAgC,WAAhC,gCAAgC,CAAA;AACN,IAAA,qCAA0D,WAA1D,0DAA0D,CAAA;AACpE,IAAA,2BAAgD,WAAhD,gDAAgD,CAAA;AAC1C,IAAA,iCAAsD,WAAtD,sDAAsD,CAAA;AAC3D,IAAA,4BAAiD,WAAjD,iDAAiD,CAAA;AAClD,IAAA,2BAAgD,WAAhD,gDAAgD,CAAA;;;;;;AAI3F,SAASA,0BAA0B,CACxCC,YAAmC,EACnCC,gBAAwC,EAChB;IACxB,OAAO,MAAMC,mBAAmB,SAASD,gBAAgB;QACvD,2EAA2E,CAC3E,AAAOE,QAAQ,GAAuB;YACpC,mBAAmB;YACnB,IAAIC,gBAAsB,uBAAA,EAAE;YAC5B,IAAIC,WAAiB,kBAAA,CAACL,YAAY,CAAC;YACnC,2BAA2B;YAC3B,IAAIM,qCAA2C,4CAAA,EAAE;YACjD,IAAIC,2BAAiC,kCAAA,CAAC,IAAI,CAAC;YAC3C,IAAIC,iCAAuC,wCAAA,EAAE;YAC7C,IAAIC,2BAAiC,kCAAA,EAAE;YACvC,IAAIC,4BAAkC,mCAAA,EAAE;SACzC,CAAC;QAEFC,eAAe,CAACC,OAAY,EAAEC,IAAsB,EAAW;gBACtBC,GAAwC;YAA/E,OAAO,IAAI,CAACX,QAAQ,CAACY,IAAI,CAAC,CAACD,OAAO;gBAAKA,OAAAA,CAAAA,GAAwC,GAAxCA,OAAO,CAACH,eAAe,QAAiB,GAAxCG,KAAAA,CAAwC,GAAxCA,OAAO,CAACH,eAAe,CAAGC,OAAO,EAAEC,IAAI,CAAC,YAAxCC,GAAwC,GAAI,KAAK,CAAA;aAAA,CAAC,CAAC;SAC3F;QAEDE,iBAAiB,CAACJ,OAAY,EAAEC,IAAsB,EAAW;gBACxBC,GAA0C;YAAjF,OAAO,IAAI,CAACX,QAAQ,CAACY,IAAI,CAAC,CAACD,OAAO;gBAAKA,OAAAA,CAAAA,GAA0C,GAA1CA,OAAO,CAACE,iBAAiB,QAAiB,GAA1CF,KAAAA,CAA0C,GAA1CA,OAAO,CAACE,iBAAiB,CAAGJ,OAAO,EAAEC,IAAI,CAAC,YAA1CC,GAA0C,GAAI,KAAK,CAAA;aAAA,CAAC,CAAC;SAC7F;QAED,2EAA2E,CAC3E,MAAMG,yBAAyB,CAACL,OAA2B,EAAEC,IAAsB,EAAE;YACnF,IAAI,CAAC,IAAI,CAACF,eAAe,CAACC,OAAO,EAAEC,IAAI,CAAC,EAAE;gBACxC,MAAM,KAAK,CAACI,yBAAyB,CAACL,OAAO,EAAEC,IAAI,CAAC,CAAC;aACtD;SACF;QAED,2EAA2E,CAC3EK,6BAA6B,CAC3BC,OAAwE,EACxEN,IAAsB,EACtBO,MAAU,EACD;YACT,oDAAoD;YACpD,IAAI,IAAI,CAACJ,iBAAiB,CAACG,OAAO,EAAEN,IAAI,CAAC,EAAE;gBACzC,OAAO,IAAI,CAAC;aACb;YAED,OAAO,KAAK,CAACK,6BAA6B,CAACC,OAAO,EAAEN,IAAI,EAAEO,MAAM,CAAC,CAAC;SACnE;QAED;;;;;OAKG,CACH,MAAMC,UAAU,CAACC,GAAQ,EAAmB;YAC1C,MAAMC,kBAAkB,GAAG,SAAW,AAAC,EAAC,QAAQ;YAEhD,MAAMC,QAAQ,GAAG,MAAMC,CAAAA,GAAAA,UAAK,AAAK,CAAA,QAAL,CAACH,GAAG,CAAC,AAAC;YAClC,IAAI,CAACE,QAAQ,CAACE,EAAE,EAAE;gBAChB,MAAM,IAAIC,KAAK,CAAC,CAAC,gBAAgB,EAAEH,QAAQ,CAACI,MAAM,CAAC,iBAAiB,EAAEN,GAAG,CAAC,CAAC,CAAC,CAAC;aAC9E;YAED,MAAMO,aAAa,GAAGL,QAAQ,CAACM,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,AAAC;YAC7D,IAAIF,aAAa,IAAIG,MAAM,CAACH,aAAa,CAAC,GAAGN,kBAAkB,EAAE;gBAC/D,MAAM,IAAII,KAAK,CAAC,mDAAmD,CAAC,CAAC;aACtE;YAED,MAAMM,IAAI,GAAG,MAAMT,QAAQ,CAACS,IAAI,EAAE,AAAC;YACnC,IAAIC,MAAM,CAACC,UAAU,CAACF,IAAI,EAAE,MAAM,CAAC,GAAGV,kBAAkB,EAAE;gBACxD,MAAM,IAAII,KAAK,CAAC,0CAA0C,CAAC,CAAC;aAC7D;YAED,OAAOM,IAAI,CAAC;SACb;KACF,CAAC;CACH"}