<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture\Event\ErrorHandlerIsNotOverwritten;

use function set_error_handler;

set_error_handler(
    static function (int $errorNumber, string $errorString, string $errorFile, int $errorLine): bool
    {
        return true;
    },
);
