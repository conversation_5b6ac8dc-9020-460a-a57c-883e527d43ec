{"version": 3, "sources": ["../../../../../src/start/server/middleware/ExpoMiddleware.ts"], "sourcesContent": ["import { parse } from 'url';\n\nimport { ServerNext, ServerRequest, ServerResponse } from './server.types';\nimport * as Log from '../../../log';\n\n/** Base middleware creator for Expo dev servers. */\nexport abstract class ExpoMiddleware {\n  constructor(\n    protected projectRoot: string,\n    protected supportedPaths: string[]\n  ) {}\n\n  /**\n   * Returns true when the middleware should handle the incoming server request.\n   * Exposed for testing.\n   */\n  public shouldHandleRequest(req: ServerRequest): boolean {\n    if (!req.url) {\n      return false;\n    }\n    const parsed = parse(req.url);\n    // Strip the query params\n    if (!parsed.pathname) {\n      return false;\n    }\n\n    return this.supportedPaths.includes(parsed.pathname);\n  }\n\n  abstract handleRequestAsync(\n    req: ServerRequest,\n    res: ServerResponse,\n    next: ServerNext\n  ): Promise<void>;\n\n  /** Create a server middleware handler. */\n  public getHandler() {\n    const internalMiddleware = async (\n      req: ServerRequest,\n      res: ServerResponse,\n      next: ServerNext\n    ) => {\n      try {\n        return await this.handleRequestAsync(req, res, next);\n      } catch (error: any) {\n        Log.exception(error);\n        // 5xx = Server Error HTTP code\n        res.statusCode = 500;\n        if (typeof error === 'object' && error !== null) {\n          res.end(\n            JSON.stringify({\n              error: error.toString(),\n            })\n          );\n        } else {\n          res.end(`Unexpected error: ${error}`);\n        }\n      }\n    };\n    const middleware = async (req: ServerRequest, res: ServerResponse, next: ServerNext) => {\n      if (!this.shouldHandleRequest(req)) {\n        return next();\n      }\n      return internalMiddleware(req, res, next);\n    };\n\n    middleware.internal = internalMiddleware;\n\n    return middleware;\n  }\n}\n\nexport function disableResponseCache(res: ServerResponse): ServerResponse {\n  res.setHeader('Cache-Control', 'private, no-cache, no-store, must-revalidate');\n  res.setHeader('Expires', '-1');\n  res.setHeader('Pragma', 'no-cache');\n  return res;\n}\n"], "names": ["disableResponseCache", "Log", "ExpoMiddleware", "constructor", "projectRoot", "supportedPaths", "shouldHandleRequest", "req", "url", "parsed", "parse", "pathname", "includes", "<PERSON><PERSON><PERSON><PERSON>", "internalMiddleware", "res", "next", "handleRequestAsync", "error", "exception", "statusCode", "end", "JSON", "stringify", "toString", "middleware", "internal", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA;;;;QAwEgBA,oBAAoB,GAApBA,oBAAoB;AAxEd,IAAA,IAAK,WAAL,KAAK,CAAA;AAGfC,IAAAA,GAAG,mCAAM,cAAc,EAApB;;;;;;;;;;;;;;;;;;;;;;AAGR,MAAeC,cAAc;IAClCC,YACYC,WAAmB,EACnBC,cAAwB,CAClC;aAFUD,WAAmB,GAAnBA,WAAmB;aACnBC,cAAwB,GAAxBA,cAAwB;KAChC;IAEJ;;;KAGG,CACH,AAAOC,mBAAmB,CAACC,GAAkB,EAAW;QACtD,IAAI,CAACA,GAAG,CAACC,GAAG,EAAE;YACZ,OAAO,KAAK,CAAC;SACd;QACD,MAAMC,MAAM,GAAGC,CAAAA,GAAAA,IAAK,AAAS,CAAA,MAAT,CAACH,GAAG,CAACC,GAAG,CAAC,AAAC;QAC9B,yBAAyB;QACzB,IAAI,CAACC,MAAM,CAACE,QAAQ,EAAE;YACpB,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAACN,cAAc,CAACO,QAAQ,CAACH,MAAM,CAACE,QAAQ,CAAC,CAAC;KACtD;IAQD,0CAA0C,CAC1C,AAAOE,UAAU,GAAG;QAClB,MAAMC,kBAAkB,GAAG,OACzBP,GAAkB,EAClBQ,GAAmB,EACnBC,IAAgB,GACb;YACH,IAAI;gBACF,OAAO,MAAM,IAAI,CAACC,kBAAkB,CAACV,GAAG,EAAEQ,GAAG,EAAEC,IAAI,CAAC,CAAC;aACtD,CAAC,OAAOE,KAAK,EAAO;gBACnBjB,GAAG,CAACkB,SAAS,CAACD,KAAK,CAAC,CAAC;gBACrB,+BAA+B;gBAC/BH,GAAG,CAACK,UAAU,GAAG,GAAG,CAAC;gBACrB,IAAI,OAAOF,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;oBAC/CH,GAAG,CAACM,GAAG,CACLC,IAAI,CAACC,SAAS,CAAC;wBACbL,KAAK,EAAEA,KAAK,CAACM,QAAQ,EAAE;qBACxB,CAAC,CACH,CAAC;iBACH,MAAM;oBACLT,GAAG,CAACM,GAAG,CAAC,CAAC,kBAAkB,EAAEH,KAAK,CAAC,CAAC,CAAC,CAAC;iBACvC;aACF;SACF,AAAC;QACF,MAAMO,UAAU,GAAG,OAAOlB,GAAkB,EAAEQ,GAAmB,EAAEC,IAAgB,GAAK;YACtF,IAAI,CAAC,IAAI,CAACV,mBAAmB,CAACC,GAAG,CAAC,EAAE;gBAClC,OAAOS,IAAI,EAAE,CAAC;aACf;YACD,OAAOF,kBAAkB,CAACP,GAAG,EAAEQ,GAAG,EAAEC,IAAI,CAAC,CAAC;SAC3C,AAAC;QAEFS,UAAU,CAACC,QAAQ,GAAGZ,kBAAkB,CAAC;QAEzC,OAAOW,UAAU,CAAC;KACnB;CACF;QAhEqBvB,cAAc,GAAdA,cAAc;AAkE7B,SAASF,oBAAoB,CAACe,GAAmB,EAAkB;IACxEA,GAAG,CAACY,SAAS,CAAC,eAAe,EAAE,8CAA8C,CAAC,CAAC;IAC/EZ,GAAG,CAACY,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC/BZ,GAAG,CAACY,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IACpC,OAAOZ,GAAG,CAAC;CACZ"}