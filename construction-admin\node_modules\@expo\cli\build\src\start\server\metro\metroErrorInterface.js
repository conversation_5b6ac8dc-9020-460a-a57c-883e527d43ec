"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.logMetroErrorWithStack = logMetroErrorWithStack;
exports.logMetroError = logMetroError;
exports.logMetroErrorAsync = logMetroErrorAsync;
exports.getErrorOverlayHtmlAsync = getErrorOverlayHtmlAsync;
var _chalk = _interopRequireDefault(require("chalk"));
var _resolveFrom = _interopRequireDefault(require("resolve-from"));
var _terminalLink = _interopRequireDefault(require("terminal-link"));
var _log = require("../../../log");
var _errors = require("../../../utils/errors");
var _getStaticRenderFunctions = require("../getStaticRenderFunctions");
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function fill(width) {
    return Array(width).join(" ");
}
function formatPaths(config) {
    const filePath = _chalk.default.reset(config.filePath);
    return _chalk.default.dim("(") + filePath + _chalk.default.dim(`:${[
        config.line,
        config.col
    ].filter(Boolean).join(":")})`);
}
async function logMetroErrorWithStack(projectRoot, { stack , codeFrame , error  }) {
    if (error instanceof _errors.SilentError) {
        return;
    }
    // process.stdout.write('\u001b[0m'); // Reset attributes
    // process.stdout.write('\u001bc'); // Reset the terminal
    const { getStackFormattedLocation  } = require((0, _resolveFrom).default(projectRoot, "@expo/metro-runtime/symbolicate"));
    _log.Log.log();
    _log.Log.log(_chalk.default.red("Metro error: ") + error.message);
    _log.Log.log();
    if (codeFrame) {
        var ref;
        const maxWarningLineLength = Math.max(200, process.stdout.columns);
        const lineText = codeFrame.content;
        const isPreviewTooLong = codeFrame.content.split("\n").some((line)=>line.length > maxWarningLineLength
        );
        const column = (ref = codeFrame.location) == null ? void 0 : ref.column;
        // When the preview is too long, we skip reading the file and attempting to apply
        // code coloring, this is because it can get very slow.
        if (isPreviewTooLong) {
            var ref1, ref2;
            let previewLine = "";
            let cursorLine = "";
            const formattedPath = formatPaths({
                filePath: codeFrame.fileName,
                line: (ref1 = codeFrame.location) == null ? void 0 : ref1.row,
                col: (ref2 = codeFrame.location) == null ? void 0 : ref2.column
            });
            // Create a curtailed preview line like:
            // `...transition:'fade'},k._updatePropsStack=function(){clearImmediate(k._updateImmediate),k._updateImmediate...`
            // If there is no text preview or column number, we can't do anything.
            if (lineText && column != null) {
                var ref3;
                var ref4;
                const rangeWindow = Math.round(Math.max((ref4 = (ref3 = codeFrame.fileName) == null ? void 0 : ref3.length) != null ? ref4 : 0, Math.max(80, process.stdout.columns)) / 2);
                let minBounds = Math.max(0, column - rangeWindow);
                const maxBounds = Math.min(minBounds + rangeWindow * 2, lineText.length);
                previewLine = lineText.slice(minBounds, maxBounds);
                // If we splice content off the start, then we should append `...`.
                // This is unlikely to happen since we limit the activation size.
                if (minBounds > 0) {
                    // Adjust the min bounds so the cursor is aligned after we add the "..."
                    minBounds -= 3;
                    previewLine = _chalk.default.dim("...") + previewLine;
                }
                if (maxBounds < lineText.length) {
                    previewLine += _chalk.default.dim("...");
                }
                // If the column property could be found, then use that to fix the cursor location which is often broken in regex.
                cursorLine = (column == null ? "" : fill(column) + _chalk.default.reset("^")).slice(minBounds);
                _log.Log.log([
                    formattedPath,
                    "",
                    previewLine,
                    cursorLine,
                    _chalk.default.dim("(error truncated)")
                ].join("\n"));
            }
        } else {
            _log.Log.log(codeFrame.content);
        }
    }
    if (stack == null ? void 0 : stack.length) {
        _log.Log.log();
        _log.Log.log(_chalk.default.bold`Call Stack`);
        const stackProps = stack.map((frame)=>{
            return {
                title: frame.methodName,
                subtitle: getStackFormattedLocation(projectRoot, frame),
                collapse: frame.collapse
            };
        });
        stackProps.forEach((frame)=>{
            const position = _terminalLink.default.isSupported ? (0, _terminalLink).default(frame.subtitle, frame.subtitle) : frame.subtitle;
            let lineItem = _chalk.default.gray(`  ${frame.title} (${position})`);
            if (frame.collapse) {
                lineItem = _chalk.default.dim(lineItem);
            }
            _log.Log.log(lineItem);
        });
    } else {
        _log.Log.log(_chalk.default.gray(`  ${error.stack}`));
    }
}
async function logMetroError(projectRoot, { error  }) {
    var ref, ref5;
    if (error instanceof _errors.SilentError) {
        return;
    }
    const { LogBoxLog , parseErrorStack  } = require((0, _resolveFrom).default(projectRoot, "@expo/metro-runtime/symbolicate"));
    const stack = parseErrorStack(error.stack);
    const log = new LogBoxLog({
        level: "static",
        message: {
            content: error.message,
            substitutions: []
        },
        isComponentError: false,
        stack,
        category: "static",
        componentStack: []
    });
    await new Promise((res)=>log.symbolicate("stack", res)
    );
    var ref6;
    logMetroErrorWithStack(projectRoot, {
        stack: (ref6 = (ref = log.symbolicated) == null ? void 0 : (ref5 = ref.stack) == null ? void 0 : ref5.stack) != null ? ref6 : [],
        codeFrame: log.codeFrame,
        error
    });
}
/** @returns the html required to render the static metro error as an SPA. */ function logFromError({ error , projectRoot  }) {
    const { LogBoxLog , parseErrorStack  } = require((0, _resolveFrom).default(projectRoot, "@expo/metro-runtime/symbolicate"));
    const stack = parseErrorStack(error.stack);
    return new LogBoxLog({
        level: "static",
        message: {
            content: error.message,
            substitutions: []
        },
        isComponentError: false,
        stack,
        category: "static",
        componentStack: []
    });
}
async function logMetroErrorAsync({ error , projectRoot  }) {
    var ref, ref7;
    const log = logFromError({
        projectRoot,
        error
    });
    await new Promise((res)=>log.symbolicate("stack", res)
    );
    var ref8;
    logMetroErrorWithStack(projectRoot, {
        stack: (ref8 = (ref = log.symbolicated) == null ? void 0 : (ref7 = ref.stack) == null ? void 0 : ref7.stack) != null ? ref8 : [],
        codeFrame: log.codeFrame,
        error
    });
}
async function getErrorOverlayHtmlAsync({ error , projectRoot , routerRoot  }) {
    var ref, ref9;
    const log = logFromError({
        projectRoot,
        error
    });
    await new Promise((res)=>log.symbolicate("stack", res)
    );
    var ref10;
    logMetroErrorWithStack(projectRoot, {
        stack: (ref10 = (ref = log.symbolicated) == null ? void 0 : (ref9 = ref.stack) == null ? void 0 : ref9.stack) != null ? ref10 : [],
        codeFrame: log.codeFrame,
        error
    });
    const logBoxContext = {
        selectedLogIndex: 0,
        isDisabled: false,
        logs: [
            log
        ]
    };
    const html = `<html><head><style>#root,body,html{height:100%}body{overflow:hidden}#root{display:flex}</style></head><body><div id="root"></div><script id="_expo-static-error" type="application/json">${JSON.stringify(logBoxContext)}</script></body></html>`;
    const errorOverlayEntry = await (0, _getStaticRenderFunctions).createMetroEndpointAsync(projectRoot, // Keep the URL relative
    "", (0, _resolveFrom).default(projectRoot, "expo-router/_error"), {
        dev: true,
        platform: "web",
        minify: false,
        baseUrl: "",
        routerRoot
    });
    const htmlWithJs = html.replace("</body>", `<script src=${errorOverlayEntry}></script></body>`);
    return htmlWithJs;
}

//# sourceMappingURL=metroErrorInterface.js.map