{"version": 3, "sources": ["../../../../../src/start/server/middleware/resolvePlatform.ts"], "sourcesContent": ["import { parse } from 'url';\n\nimport { ServerRequest } from './server.types';\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:resolvePlatform'\n) as typeof console.log;\n\n/** Supported platforms */\nexport type RuntimePlatform = 'ios' | 'android';\n\n/**\n * Extract the runtime platform from the server request.\n * 1. Query param `platform`: `?platform=ios`\n * 2. Header `expo-platform`: `'expo-platform': ios`\n * 3. Legacy header `exponent-platform`: `'exponent-platform': ios`\n *\n * Returns first item in the case of an array.\n */\nexport function parsePlatformHeader(req: ServerRequest): string | null {\n  const url = parse(req.url!, /* parseQueryString */ true);\n  const platform =\n    url.query?.platform || req.headers['expo-platform'] || req.headers['exponent-platform'];\n  return (Array.isArray(platform) ? platform[0] : platform) ?? null;\n}\n\n/** Guess the platform from the user-agent header. */\nexport function resolvePlatformFromUserAgentHeader(req: ServerRequest): string | null {\n  let platform = null;\n  const userAgent = req.headers['user-agent'];\n  if (userAgent?.match(/Android/i)) {\n    platform = 'android';\n  }\n  if (userAgent?.match(/iPhone|iPad/i)) {\n    platform = 'ios';\n  }\n  debug(`Resolved platform ${platform} from user-agent header: ${userAgent}`);\n  return platform;\n}\n\n/** Assert if the runtime platform is not included. */\nexport function assertMissingRuntimePlatform(platform?: any): asserts platform {\n  if (!platform) {\n    throw new CommandError(\n      'PLATFORM_HEADER',\n      `Must specify \"expo-platform\" header or \"platform\" query parameter`\n    );\n  }\n}\n\n/** Assert if the runtime platform is not correct. */\nexport function assertRuntimePlatform(platform: string): asserts platform is RuntimePlatform {\n  const stringifiedPlatform = String(platform);\n  if (!['android', 'ios', 'web'].includes(stringifiedPlatform)) {\n    throw new CommandError(\n      'PLATFORM_HEADER',\n      `platform must be \"android\", \"ios\", or \"web\". Received: \"${platform}\"`\n    );\n  }\n}\n"], "names": ["parsePlatformHeader", "resolvePlatformFromUserAgentHeader", "assertMissingRuntimePlatform", "assertRuntimePlatform", "debug", "require", "req", "url", "parse", "platform", "query", "headers", "Array", "isArray", "userAgent", "match", "CommandError", "stringifiedPlatform", "String", "includes"], "mappings": "AAAA;;;;QAoBgBA,mBAAmB,GAAnBA,mBAAmB;QAQnBC,kCAAkC,GAAlCA,kCAAkC;QAclCC,4BAA4B,GAA5BA,4BAA4B;QAU5BC,qBAAqB,GAArBA,qBAAqB;AApDf,IAAA,IAAK,WAAL,KAAK,CAAA;AAGE,IAAA,OAAuB,WAAvB,uBAAuB,CAAA;AAEpD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,8CAA8C,CAC/C,AAAsB,AAAC;AAajB,SAASL,mBAAmB,CAACM,GAAkB,EAAiB;QAGnEC,GAAS;IAFX,MAAMA,GAAG,GAAGC,CAAAA,GAAAA,IAAK,AAAuC,CAAA,MAAvC,CAACF,GAAG,CAACC,GAAG,EAAG,sBAAsB,CAAC,IAAI,CAAC,AAAC;IACzD,MAAME,QAAQ,GACZF,CAAAA,CAAAA,GAAS,GAATA,GAAG,CAACG,KAAK,SAAU,GAAnBH,KAAAA,CAAmB,GAAnBA,GAAS,CAAEE,QAAQ,CAAA,IAAIH,GAAG,CAACK,OAAO,CAAC,eAAe,CAAC,IAAIL,GAAG,CAACK,OAAO,CAAC,mBAAmB,CAAC,AAAC;QACnF,IAAkD;IAAzD,OAAO,CAAA,IAAkD,GAAjDC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,YAAjD,IAAkD,GAAI,IAAI,CAAC;CACnE;AAGM,SAASR,kCAAkC,CAACK,GAAkB,EAAiB;IACpF,IAAIG,QAAQ,GAAG,IAAI,AAAC;IACpB,MAAMK,SAAS,GAAGR,GAAG,CAACK,OAAO,CAAC,YAAY,CAAC,AAAC;IAC5C,IAAIG,SAAS,QAAO,GAAhBA,KAAAA,CAAgB,GAAhBA,SAAS,CAAEC,KAAK,YAAY,EAAE;QAChCN,QAAQ,GAAG,SAAS,CAAC;KACtB;IACD,IAAIK,SAAS,QAAO,GAAhBA,KAAAA,CAAgB,GAAhBA,SAAS,CAAEC,KAAK,gBAAgB,EAAE;QACpCN,QAAQ,GAAG,KAAK,CAAC;KAClB;IACDL,KAAK,CAAC,CAAC,kBAAkB,EAAEK,QAAQ,CAAC,yBAAyB,EAAEK,SAAS,CAAC,CAAC,CAAC,CAAC;IAC5E,OAAOL,QAAQ,CAAC;CACjB;AAGM,SAASP,4BAA4B,CAACO,QAAc,EAAoB;IAC7E,IAAI,CAACA,QAAQ,EAAE;QACb,MAAM,IAAIO,OAAY,aAAA,CACpB,iBAAiB,EACjB,CAAC,iEAAiE,CAAC,CACpE,CAAC;KACH;CACF;AAGM,SAASb,qBAAqB,CAACM,QAAgB,EAAuC;IAC3F,MAAMQ,mBAAmB,GAAGC,MAAM,CAACT,QAAQ,CAAC,AAAC;IAC7C,IAAI,CAAC;QAAC,SAAS;QAAE,KAAK;QAAE,KAAK;KAAC,CAACU,QAAQ,CAACF,mBAAmB,CAAC,EAAE;QAC5D,MAAM,IAAID,OAAY,aAAA,CACpB,iBAAiB,EACjB,CAAC,wDAAwD,EAAEP,QAAQ,CAAC,CAAC,CAAC,CACvE,CAAC;KACH;CACF"}