{"version": 3, "sources": ["../../../src/utils/link.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport terminalLink from 'terminal-link';\n\n/**\n * Prints a link for given URL, using text if provided, otherwise text is just the URL.\n * Format links as dim (unless disabled) and with an underline.\n *\n * @example https://expo.dev\n */\nexport function link(\n  url: string,\n  { text = url, dim = true }: { text?: string; dim?: boolean } = {}\n): string {\n  let output: string;\n  // Links can be disabled via env variables https://github.com/jamestalmage/supports-hyperlinks/blob/master/index.js\n  if (terminalLink.isSupported) {\n    output = terminalLink(text, url);\n  } else {\n    output = `${text === url ? '' : text + ': '}${chalk.underline(url)}`;\n  }\n  return dim ? chalk.dim(output) : output;\n}\n\n/**\n * Provide a consistent \"Learn more\" link experience.\n * Format links as dim (unless disabled) with an underline.\n *\n * @example [Learn more](https://expo.dev)\n * @example Learn more: https://expo.dev\n */\nexport function learnMore(\n  url: string,\n  {\n    learnMoreMessage: maybeLearnMoreMessage,\n    dim = true,\n  }: { learnMoreMessage?: string; dim?: boolean } = {}\n): string {\n  return link(url, { text: maybeLearnMoreMessage ?? 'Learn more', dim });\n}\n"], "names": ["link", "learnMore", "url", "text", "dim", "output", "terminalLink", "isSupported", "chalk", "underline", "learnMoreMessage", "maybeLearnMoreMessage"], "mappings": "AAAA;;;;QASgBA,IAAI,GAAJA,IAAI;QAqBJC,SAAS,GAATA,SAAS;AA9BP,IAAA,MAAO,kCAAP,OAAO,EAAA;AACA,IAAA,aAAe,kCAAf,eAAe,EAAA;;;;;;AAQjC,SAASD,IAAI,CAClBE,GAAW,EACX,EAAEC,IAAI,EAAGD,GAAG,CAAA,EAAEE,GAAG,EAAG,IAAI,CAAA,EAAoC,GAAG,EAAE,EACzD;IACR,IAAIC,MAAM,AAAQ,AAAC;IACnB,mHAAmH;IACnH,IAAIC,aAAY,QAAA,CAACC,WAAW,EAAE;QAC5BF,MAAM,GAAGC,CAAAA,GAAAA,aAAY,AAAW,CAAA,QAAX,CAACH,IAAI,EAAED,GAAG,CAAC,CAAC;KAClC,MAAM;QACLG,MAAM,GAAG,CAAC,EAAEF,IAAI,KAAKD,GAAG,GAAG,EAAE,GAAGC,IAAI,GAAG,IAAI,CAAC,EAAEK,MAAK,QAAA,CAACC,SAAS,CAACP,GAAG,CAAC,CAAC,CAAC,CAAC;KACtE;IACD,OAAOE,GAAG,GAAGI,MAAK,QAAA,CAACJ,GAAG,CAACC,MAAM,CAAC,GAAGA,MAAM,CAAC;CACzC;AASM,SAASJ,SAAS,CACvBC,GAAW,EACX,EACEQ,gBAAgB,EAAEC,qBAAqB,CAAA,EACvCP,GAAG,EAAG,IAAI,CAAA,EACmC,GAAG,EAAE,EAC5C;IACR,OAAOJ,IAAI,CAACE,GAAG,EAAE;QAAEC,IAAI,EAAEQ,qBAAqB,WAArBA,qBAAqB,GAAI,YAAY;QAAEP,GAAG;KAAE,CAAC,CAAC;CACxE"}