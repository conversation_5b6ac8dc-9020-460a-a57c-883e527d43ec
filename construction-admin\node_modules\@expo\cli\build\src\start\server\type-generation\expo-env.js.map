{"version": 3, "sources": ["../../../../../src/start/server/type-generation/expo-env.ts"], "sourcesContent": ["import fs from 'fs/promises';\nimport path from 'path';\n\nconst template = `/// <reference types=\"expo/types\" />\n\n// NOTE: This file should not be edited and should be in your git ignore`;\n\nexport async function writeExpoEnvDTS(projectRoot: string) {\n  return fs.writeFile(path.join(projectRoot, 'expo-env.d.ts'), template);\n}\n\nexport async function removeExpoEnvDTS(projectRoot: string) {\n  // Force removal of expo-env.d.ts - Ignore any errors if the file does not exist\n  return fs.rm(path.join(projectRoot, 'expo-env.d.ts'), { force: true });\n}\n"], "names": ["writeExpoEnvDTS", "removeExpoEnvDTS", "template", "projectRoot", "fs", "writeFile", "path", "join", "rm", "force"], "mappings": "AAAA;;;;QAOsBA,eAAe,GAAfA,eAAe;QAIfC,gBAAgB,GAAhBA,gBAAgB;AAXvB,IAAA,SAAa,kCAAb,aAAa,EAAA;AACX,IAAA,KAAM,kCAAN,MAAM,EAAA;;;;;;AAEvB,MAAMC,QAAQ,GAAG,CAAC;;wEAEsD,CAAC,AAAC;AAEnE,eAAeF,eAAe,CAACG,WAAmB,EAAE;IACzD,OAAOC,SAAE,QAAA,CAACC,SAAS,CAACC,KAAI,QAAA,CAACC,IAAI,CAACJ,WAAW,EAAE,eAAe,CAAC,EAAED,QAAQ,CAAC,CAAC;CACxE;AAEM,eAAeD,gBAAgB,CAACE,WAAmB,EAAE;IAC1D,gFAAgF;IAChF,OAAOC,SAAE,QAAA,CAACI,EAAE,CAACF,KAAI,QAAA,CAACC,IAAI,CAACJ,WAAW,EAAE,eAAe,CAAC,EAAE;QAAEM,KAAK,EAAE,IAAI;KAAE,CAAC,CAAC;CACxE"}