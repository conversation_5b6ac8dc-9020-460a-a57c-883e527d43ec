{"version": 3, "sources": ["../../../src/utils/createFileTransform.ts"], "sourcesContent": ["import { IOSConfig } from '@expo/config-plugins';\nimport Minipass from 'minipass';\nimport path from 'path';\nimport picomatch from 'picomatch';\nimport { ReadEntry } from 'tar';\n\nconst debug = require('debug')('expo:file-transform') as typeof console.log;\n\nfunction escapeXMLCharacters(original: string): string {\n  const noAmps = original.replace('&', '&amp;');\n  const noLt = noAmps.replace('<', '&lt;');\n  const noGt = noLt.replace('>', '&gt;');\n  const noApos = noGt.replace('\"', '\\\\\"');\n  return noApos.replace(\"'\", \"\\\\'\");\n}\n\nclass Transformer extends Minipass {\n  data = '';\n\n  constructor(private settings: { name: string; extension: string }) {\n    super();\n  }\n\n  write(data: string) {\n    this.data += data;\n    return true;\n  }\n\n  getNormalizedName(): string {\n    if (['.xml', '.plist'].includes(this.settings.extension)) {\n      return escapeXMLCharacters(this.settings.name);\n    }\n    return this.settings.name;\n  }\n\n  end() {\n    const name = this.getNormalizedName();\n    const replaced = this.data\n      .replace(/Hello App Display Name/g, name)\n      .replace(/HelloWorld/g, IOSConfig.XcodeUtils.sanitizedName(name))\n      .replace(/helloworld/g, IOSConfig.XcodeUtils.sanitizedName(name.toLowerCase()));\n    super.write(replaced);\n    return super.end();\n  }\n}\n\nexport function createEntryResolver(name: string) {\n  return (entry: ReadEntry) => {\n    if (name) {\n      // Rewrite paths for bare workflow\n      entry.path = entry.path\n        .replace(\n          /HelloWorld/g,\n          entry.path.includes('android')\n            ? IOSConfig.XcodeUtils.sanitizedName(name.toLowerCase())\n            : IOSConfig.XcodeUtils.sanitizedName(name)\n        )\n        .replace(/helloworld/g, IOSConfig.XcodeUtils.sanitizedName(name).toLowerCase());\n    }\n    if (entry.type && /^file$/i.test(entry.type) && path.basename(entry.path) === 'gitignore') {\n      // Rename `gitignore` because npm ignores files named `.gitignore` when publishing.\n      // See: https://github.com/npm/npm/issues/1862\n      entry.path = entry.path.replace(/gitignore$/, '.gitignore');\n    }\n  };\n}\n\nexport function createFileTransform(name: string) {\n  return (entry: ReadEntry) => {\n    const extension = path.extname(entry.path);\n\n    // Binary files, don't process these (avoid decoding as utf8)\n    if (\n      ![\n        '.png',\n        '.jpg',\n        '.jpeg',\n        '.gif',\n        '.webp',\n        '.psd',\n        '.tiff',\n        '.svg',\n        '.jar',\n        '.keystore',\n        '.gz',\n\n        // Font files\n        '.otf',\n        '.ttf',\n      ].includes(extension) &&\n      name\n    ) {\n      return new Transformer({ name, extension });\n    }\n    return undefined;\n  };\n}\n\nexport function createGlobFilter(\n  globPattern: picomatch.Glob,\n  options?: picomatch.PicomatchOptions\n) {\n  const matcher = picomatch(globPattern, options);\n\n  debug(\n    'filter: created for pattern(s) \"%s\" (%s)',\n    Array.isArray(globPattern) ? globPattern.join('\", \"') : globPattern,\n    options\n  );\n\n  return (path: string) => {\n    const included = matcher(path);\n    debug('filter: %s - %s', included ? 'include' : 'exclude', path);\n    return included;\n  };\n}\n"], "names": ["createEntryResolver", "createFileTransform", "createGlobFilter", "debug", "require", "escapeXMLCharacters", "original", "noAmps", "replace", "noLt", "noGt", "noApos", "Transformer", "Minipass", "constructor", "settings", "data", "write", "getNormalizedName", "includes", "extension", "name", "end", "replaced", "IOSConfig", "XcodeUtils", "sanitizedName", "toLowerCase", "entry", "path", "type", "test", "basename", "extname", "undefined", "globPattern", "options", "matcher", "picomatch", "Array", "isArray", "join", "included"], "mappings": "AAAA;;;;QA8CgBA,mBAAmB,GAAnBA,mBAAmB;QAqBnBC,mBAAmB,GAAnBA,mBAAmB;QA+BnBC,gBAAgB,GAAhBA,gBAAgB;AAlGN,IAAA,cAAsB,WAAtB,sBAAsB,CAAA;AAC3B,IAAA,SAAU,kCAAV,UAAU,EAAA;AACd,IAAA,KAAM,kCAAN,MAAM,EAAA;AACD,IAAA,UAAW,kCAAX,WAAW,EAAA;;;;;;AAGjC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,qBAAqB,CAAC,AAAsB,AAAC;AAE5E,SAASC,mBAAmB,CAACC,QAAgB,EAAU;IACrD,MAAMC,MAAM,GAAGD,QAAQ,CAACE,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,AAAC;IAC9C,MAAMC,IAAI,GAAGF,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,AAAC;IACzC,MAAME,IAAI,GAAGD,IAAI,CAACD,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,AAAC;IACvC,MAAMG,MAAM,GAAGD,IAAI,CAACF,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,AAAC;IACxC,OAAOG,MAAM,CAACH,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;CACnC;AAED,MAAMI,WAAW,SAASC,SAAQ,QAAA;IAGhCC,YAAoBC,QAA6C,CAAE;QACjE,KAAK,EAAE,CAAC;aADUA,QAA6C,GAA7CA,QAA6C;aAFjEC,IAAI,GAAG,EAAE;KAIR;IAEDC,KAAK,CAACD,IAAY,EAAE;QAClB,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC;QAClB,OAAO,IAAI,CAAC;KACb;IAEDE,iBAAiB,GAAW;QAC1B,IAAI;YAAC,MAAM;YAAE,QAAQ;SAAC,CAACC,QAAQ,CAAC,IAAI,CAACJ,QAAQ,CAACK,SAAS,CAAC,EAAE;YACxD,OAAOf,mBAAmB,CAAC,IAAI,CAACU,QAAQ,CAACM,IAAI,CAAC,CAAC;SAChD;QACD,OAAO,IAAI,CAACN,QAAQ,CAACM,IAAI,CAAC;KAC3B;IAEDC,GAAG,GAAG;QACJ,MAAMD,IAAI,GAAG,IAAI,CAACH,iBAAiB,EAAE,AAAC;QACtC,MAAMK,QAAQ,GAAG,IAAI,CAACP,IAAI,CACvBR,OAAO,4BAA4Ba,IAAI,CAAC,CACxCb,OAAO,gBAAgBgB,cAAS,UAAA,CAACC,UAAU,CAACC,aAAa,CAACL,IAAI,CAAC,CAAC,CAChEb,OAAO,gBAAgBgB,cAAS,UAAA,CAACC,UAAU,CAACC,aAAa,CAACL,IAAI,CAACM,WAAW,EAAE,CAAC,CAAC,AAAC;QAClF,KAAK,CAACV,KAAK,CAACM,QAAQ,CAAC,CAAC;QACtB,OAAO,KAAK,CAACD,GAAG,EAAE,CAAC;KACpB;CACF;AAEM,SAAStB,mBAAmB,CAACqB,IAAY,EAAE;IAChD,OAAO,CAACO,KAAgB,GAAK;QAC3B,IAAIP,IAAI,EAAE;YACR,kCAAkC;YAClCO,KAAK,CAACC,IAAI,GAAGD,KAAK,CAACC,IAAI,CACpBrB,OAAO,gBAENoB,KAAK,CAACC,IAAI,CAACV,QAAQ,CAAC,SAAS,CAAC,GAC1BK,cAAS,UAAA,CAACC,UAAU,CAACC,aAAa,CAACL,IAAI,CAACM,WAAW,EAAE,CAAC,GACtDH,cAAS,UAAA,CAACC,UAAU,CAACC,aAAa,CAACL,IAAI,CAAC,CAC7C,CACAb,OAAO,gBAAgBgB,cAAS,UAAA,CAACC,UAAU,CAACC,aAAa,CAACL,IAAI,CAAC,CAACM,WAAW,EAAE,CAAC,CAAC;SACnF;QACD,IAAIC,KAAK,CAACE,IAAI,IAAI,UAAUC,IAAI,CAACH,KAAK,CAACE,IAAI,CAAC,IAAID,KAAI,QAAA,CAACG,QAAQ,CAACJ,KAAK,CAACC,IAAI,CAAC,KAAK,WAAW,EAAE;YACzF,mFAAmF;YACnF,8CAA8C;YAC9CD,KAAK,CAACC,IAAI,GAAGD,KAAK,CAACC,IAAI,CAACrB,OAAO,eAAe,YAAY,CAAC,CAAC;SAC7D;KACF,CAAC;CACH;AAEM,SAASP,mBAAmB,CAACoB,IAAY,EAAE;IAChD,OAAO,CAACO,KAAgB,GAAK;QAC3B,MAAMR,SAAS,GAAGS,KAAI,QAAA,CAACI,OAAO,CAACL,KAAK,CAACC,IAAI,CAAC,AAAC;QAE3C,6DAA6D;QAC7D,IACE,CAAC;YACC,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;YACN,OAAO;YACP,MAAM;YACN,OAAO;YACP,MAAM;YACN,MAAM;YACN,WAAW;YACX,KAAK;YAEL,aAAa;YACb,MAAM;YACN,MAAM;SACP,CAACV,QAAQ,CAACC,SAAS,CAAC,IACrBC,IAAI,EACJ;YACA,OAAO,IAAIT,WAAW,CAAC;gBAAES,IAAI;gBAAED,SAAS;aAAE,CAAC,CAAC;SAC7C;QACD,OAAOc,SAAS,CAAC;KAClB,CAAC;CACH;AAEM,SAAShC,gBAAgB,CAC9BiC,WAA2B,EAC3BC,OAAoC,EACpC;IACA,MAAMC,OAAO,GAAGC,CAAAA,GAAAA,UAAS,AAAsB,CAAA,QAAtB,CAACH,WAAW,EAAEC,OAAO,CAAC,AAAC;IAEhDjC,KAAK,CACH,0CAA0C,EAC1CoC,KAAK,CAACC,OAAO,CAACL,WAAW,CAAC,GAAGA,WAAW,CAACM,IAAI,CAAC,MAAM,CAAC,GAAGN,WAAW,EACnEC,OAAO,CACR,CAAC;IAEF,OAAO,CAACP,IAAY,GAAK;QACvB,MAAMa,QAAQ,GAAGL,OAAO,CAACR,IAAI,CAAC,AAAC;QAC/B1B,KAAK,CAAC,iBAAiB,EAAEuC,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAEb,IAAI,CAAC,CAAC;QACjE,OAAOa,QAAQ,CAAC;KACjB,CAAC;CACH"}