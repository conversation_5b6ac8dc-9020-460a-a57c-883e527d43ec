{"version": 3, "sources": ["../../../src/utils/plist.ts"], "sourcesContent": ["import plist from '@expo/plist';\nimport binaryPlist from 'bplist-parser';\nimport fs from 'fs/promises';\n\nimport { CommandError } from './errors';\nimport * as Log from '../log';\n\nconst CHAR_CHEVRON_OPEN = 60;\nconst CHAR_B_LOWER = 98;\n// .mobileprovision\n// const CHAR_ZERO = 30;\n\nexport async function parsePlistAsync(plistPath: string) {\n  Log.debug(`Parse plist: ${plistPath}`);\n\n  return parsePlistBuffer(await fs.readFile(plistPath));\n}\n\nexport function parsePlistBuffer(contents: Buffer) {\n  if (contents[0] === CHAR_CHEVRON_OPEN) {\n    const info = plist.parse(contents.toString());\n    if (Array.isArray(info)) return info[0];\n    return info;\n  } else if (contents[0] === CHAR_B_LOWER) {\n    // @ts-expect-error\n    const info = binaryPlist.parseBuffer(contents);\n    if (Array.isArray(info)) return info[0];\n    return info;\n  } else {\n    throw new CommandError(\n      'PLIST',\n      `Cannot parse plist of type byte (0x${contents[0].toString(16)})`\n    );\n  }\n}\n"], "names": ["parsePlistAsync", "parsePlist<PERSON><PERSON><PERSON>", "Log", "CHAR_CHEVRON_OPEN", "CHAR_B_LOWER", "plist<PERSON><PERSON>", "debug", "fs", "readFile", "contents", "info", "plist", "parse", "toString", "Array", "isArray", "binaryPlist", "parse<PERSON><PERSON>er", "CommandError"], "mappings": "AAAA;;;;QAYsBA,eAAe,GAAfA,eAAe;QAMrBC,gBAAgB,GAAhBA,gBAAgB;AAlBd,IAAA,MAAa,kCAAb,aAAa,EAAA;AACP,IAAA,aAAe,kCAAf,eAAe,EAAA;AACxB,IAAA,SAAa,kCAAb,aAAa,EAAA;AAEC,IAAA,OAAU,WAAV,UAAU,CAAA;AAC3BC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEf,MAAMC,iBAAiB,GAAG,EAAE,AAAC;AAC7B,MAAMC,YAAY,GAAG,EAAE,AAAC;AAIjB,eAAeJ,eAAe,CAACK,SAAiB,EAAE;IACvDH,GAAG,CAACI,KAAK,CAAC,CAAC,aAAa,EAAED,SAAS,CAAC,CAAC,CAAC,CAAC;IAEvC,OAAOJ,gBAAgB,CAAC,MAAMM,SAAE,QAAA,CAACC,QAAQ,CAACH,SAAS,CAAC,CAAC,CAAC;CACvD;AAEM,SAASJ,gBAAgB,CAACQ,QAAgB,EAAE;IACjD,IAAIA,QAAQ,CAAC,CAAC,CAAC,KAAKN,iBAAiB,EAAE;QACrC,MAAMO,IAAI,GAAGC,MAAK,QAAA,CAACC,KAAK,CAACH,QAAQ,CAACI,QAAQ,EAAE,CAAC,AAAC;QAC9C,IAAIC,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE,OAAOA,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,OAAOA,IAAI,CAAC;KACb,MAAM,IAAID,QAAQ,CAAC,CAAC,CAAC,KAAKL,YAAY,EAAE;QACvC,mBAAmB;QACnB,MAAMM,IAAI,GAAGM,aAAW,QAAA,CAACC,WAAW,CAACR,QAAQ,CAAC,AAAC;QAC/C,IAAIK,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE,OAAOA,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,OAAOA,IAAI,CAAC;KACb,MAAM;QACL,MAAM,IAAIQ,OAAY,aAAA,CACpB,OAAO,EACP,CAAC,mCAAmC,EAAET,QAAQ,CAAC,CAAC,CAAC,CAACI,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAClE,CAAC;KACH;CACF"}