{"version": 3, "sources": ["../../../../../src/start/server/middleware/DevToolsPluginMiddleware.ts"], "sourcesContent": ["import assert from 'assert';\nimport send from 'send';\n\nimport { ExpoMiddleware } from './ExpoMiddleware';\nimport { ServerRequest, ServerResponse } from './server.types';\nimport DevToolsPluginManager, { DevToolsPluginEndpoint } from '../DevToolsPluginManager';\n\nexport { DevToolsPluginEndpoint };\n\nexport class DevToolsPluginMiddleware extends ExpoMiddleware {\n  constructor(\n    projectRoot: string,\n    private readonly pluginManager: DevToolsPluginManager\n  ) {\n    super(projectRoot, [DevToolsPluginEndpoint]);\n  }\n\n  override shouldHandleRequest(req: ServerRequest): boolean {\n    if (!req.url?.startsWith(DevToolsPluginEndpoint)) {\n      return false;\n    }\n    return true;\n  }\n\n  async handleRequestAsync(req: ServerRequest, res: ServerResponse): Promise<void> {\n    assert(req.headers.host, 'Request headers must include host');\n    const { pathname } = new URL(req.url ?? '/', `http://${req.headers.host}`);\n    const pluginName = this.queryPossiblePluginName(\n      pathname.substring(DevToolsPluginEndpoint.length + 1)\n    );\n    const webpageRoot = await this.pluginManager.queryPluginWebpageRootAsync(pluginName);\n    if (!webpageRoot) {\n      res.statusCode = 404;\n      res.end();\n      return;\n    }\n\n    const pathInPluginRoot =\n      pathname.substring(DevToolsPluginEndpoint.length + pluginName.length + 1) || '/';\n    send(req, pathInPluginRoot, { root: webpageRoot }).pipe(res);\n  }\n\n  private queryPossiblePluginName(pathname: string): string {\n    const parts = pathname.split('/');\n    if (parts[0][0] === '@' && parts.length > 1) {\n      // Scoped package name\n      return `${parts[0]}/${parts[1]}`;\n    }\n    return parts[0];\n  }\n}\n"], "names": ["DevToolsPluginEndpoint", "DevToolsPluginMiddleware", "ExpoMiddleware", "constructor", "projectRoot", "pluginManager", "shouldHandleRequest", "req", "url", "startsWith", "handleRequestAsync", "res", "assert", "headers", "host", "pathname", "URL", "pluginName", "queryPossiblePluginName", "substring", "length", "webpageRoot", "queryPluginWebpageRootAsync", "statusCode", "end", "pathInPluginRoot", "send", "root", "pipe", "parts", "split"], "mappings": "AAAA;;;;+BAOS<PERSON>,wBAAsB;;;eAAtBA,sBAAsB,uBAAA;;;AAPZ,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACV,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEQ,IAAA,eAAkB,WAAlB,kBAAkB,CAAA;AAEa,IAAA,sBAA0B,WAA1B,0BAA0B,CAAA;;;;;;AAIjF,MAAMC,wBAAwB,SAASC,eAAc,eAAA;IAC1DC,YACEC,WAAmB,EACFC,aAAoC,CACrD;QACA,KAAK,CAACD,WAAW,EAAE;YAACJ,sBAAsB,uBAAA;SAAC,CAAC,CAAC;aAF5BK,aAAoC,GAApCA,aAAoC;KAGtD;IAED,AAASC,mBAAmB,CAACC,GAAkB,EAAW;YACnDA,GAAO;QAAZ,IAAI,EAACA,CAAAA,GAAO,GAAPA,GAAG,CAACC,GAAG,SAAY,GAAnBD,KAAAA,CAAmB,GAAnBA,GAAO,CAAEE,UAAU,CAACT,sBAAsB,uBAAA,CAAC,CAAA,EAAE;YAChD,OAAO,KAAK,CAAC;SACd;QACD,OAAO,IAAI,CAAC;KACb;IAED,MAAMU,kBAAkB,CAACH,GAAkB,EAAEI,GAAmB,EAAiB;QAC/EC,CAAAA,GAAAA,OAAM,AAAuD,CAAA,QAAvD,CAACL,GAAG,CAACM,OAAO,CAACC,IAAI,EAAE,mCAAmC,CAAC,CAAC;YACjCP,IAAO;QAApC,MAAM,EAAEQ,QAAQ,CAAA,EAAE,GAAG,IAAIC,GAAG,CAACT,CAAAA,IAAO,GAAPA,GAAG,CAACC,GAAG,YAAPD,IAAO,GAAI,GAAG,EAAE,CAAC,OAAO,EAAEA,GAAG,CAACM,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,AAAC;QAC3E,MAAMG,UAAU,GAAG,IAAI,CAACC,uBAAuB,CAC7CH,QAAQ,CAACI,SAAS,CAACnB,sBAAsB,uBAAA,CAACoB,MAAM,GAAG,CAAC,CAAC,CACtD,AAAC;QACF,MAAMC,WAAW,GAAG,MAAM,IAAI,CAAChB,aAAa,CAACiB,2BAA2B,CAACL,UAAU,CAAC,AAAC;QACrF,IAAI,CAACI,WAAW,EAAE;YAChBV,GAAG,CAACY,UAAU,GAAG,GAAG,CAAC;YACrBZ,GAAG,CAACa,GAAG,EAAE,CAAC;YACV,OAAO;SACR;QAED,MAAMC,gBAAgB,GACpBV,QAAQ,CAACI,SAAS,CAACnB,sBAAsB,uBAAA,CAACoB,MAAM,GAAGH,UAAU,CAACG,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,AAAC;QACnFM,CAAAA,GAAAA,KAAI,AAA8C,CAAA,QAA9C,CAACnB,GAAG,EAAEkB,gBAAgB,EAAE;YAAEE,IAAI,EAAEN,WAAW;SAAE,CAAC,CAACO,IAAI,CAACjB,GAAG,CAAC,CAAC;KAC9D;IAED,AAAQO,uBAAuB,CAACH,QAAgB,EAAU;QACxD,MAAMc,KAAK,GAAGd,QAAQ,CAACe,KAAK,CAAC,GAAG,CAAC,AAAC;QAClC,IAAID,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,KAAK,CAACT,MAAM,GAAG,CAAC,EAAE;YAC3C,sBAAsB;YACtB,OAAO,CAAC,EAAES,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAClC;QACD,OAAOA,KAAK,CAAC,CAAC,CAAC,CAAC;KACjB;CACF;QAzCY5B,wBAAwB,GAAxBA,wBAAwB"}