{"version": 3, "sources": ["../../../../../src/start/server/metro/externals.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport fs from 'fs';\nimport { builtinModules } from 'module';\nimport path from 'path';\n\nimport { copyAsync } from '../../../utils/dir';\n\n// A list of the Node.js standard library modules that are currently\n// available,\nexport const NODE_STDLIB_MODULES: string[] = [\n  'fs/promises',\n  ...(\n    builtinModules ||\n    // @ts-expect-error\n    (process.binding ? Object.keys(process.binding('natives')) : []) ||\n    []\n  ).filter((x) => !/^_|^(internal|v8|node-inspect)\\/|\\//.test(x) && !['sys'].includes(x)),\n].sort();\n\nexport const EXTERNAL_REQUIRE_POLYFILL = '.expo/metro/polyfill.js';\nexport const EXTERNAL_REQUIRE_NATIVE_POLYFILL = '.expo/metro/polyfill.native.js';\nexport const METRO_EXTERNALS_FOLDER = '.expo/metro/externals';\nexport const METRO_SHIMS_FOLDER = '.expo/metro/shims';\n\nexport function getNodeExternalModuleId(fromModule: string, moduleId: string) {\n  return path.relative(\n    path.dirname(fromModule),\n    path.join(METRO_EXTERNALS_FOLDER, moduleId, 'index.js')\n  );\n}\n\nexport async function setupShimFiles(projectRoot: string) {\n  await fs.promises.mkdir(path.join(projectRoot, METRO_SHIMS_FOLDER), { recursive: true });\n  // Copy the shims to the project folder in case we're running in a monorepo.\n  const shimsFolder = path.join(require.resolve('@expo/cli/package.json'), '../static/shims');\n\n  await copyAsync(shimsFolder, path.join(projectRoot, METRO_SHIMS_FOLDER), {\n    overwrite: false,\n    recursive: true,\n  });\n}\n\nexport async function setupNodeExternals(projectRoot: string) {\n  await tapExternalRequirePolyfill(projectRoot);\n  await tapNodeShims(projectRoot);\n}\n\nasync function tapExternalRequirePolyfill(projectRoot: string) {\n  await fs.promises.mkdir(path.join(projectRoot, path.dirname(EXTERNAL_REQUIRE_POLYFILL)), {\n    recursive: true,\n  });\n  await writeIfDifferentAsync(\n    path.join(projectRoot, EXTERNAL_REQUIRE_POLYFILL),\n    'global.$$require_external = typeof window === \"undefined\" ? require : () => null;'\n  );\n  await writeIfDifferentAsync(\n    path.join(projectRoot, EXTERNAL_REQUIRE_NATIVE_POLYFILL),\n    'global.$$require_external = (moduleId) => {throw new Error(`Node.js standard library module ${moduleId} is not available in this JavaScript environment`);}'\n  );\n}\n\nasync function writeIfDifferentAsync(filePath: string, contents: string): Promise<void> {\n  if (fs.existsSync(filePath)) {\n    const current = await fs.promises.readFile(filePath, 'utf8');\n    if (current === contents) return;\n  }\n\n  await fs.promises.writeFile(filePath, contents);\n}\n\nexport function isNodeExternal(moduleName: string): string | null {\n  const moduleId = moduleName.replace(/^node:/, '');\n  if (NODE_STDLIB_MODULES.includes(moduleId)) {\n    return moduleId;\n  }\n  return null;\n}\n\nfunction tapNodeShimContents(moduleId: string): string {\n  return `module.exports = $$require_external('node:${moduleId}');`;\n}\n\n// Ensure Node.js shims which require using `$$require_external` are available inside the project.\nasync function tapNodeShims(projectRoot: string) {\n  const externals: Record<string, string> = {};\n  for (const moduleId of NODE_STDLIB_MODULES) {\n    const shimDir = path.join(projectRoot, METRO_EXTERNALS_FOLDER, moduleId);\n    const shimPath = path.join(shimDir, 'index.js');\n    externals[moduleId] = shimPath;\n\n    if (!fs.existsSync(shimPath)) {\n      await fs.promises.mkdir(shimDir, { recursive: true });\n      await fs.promises.writeFile(shimPath, tapNodeShimContents(moduleId));\n    }\n  }\n}\n"], "names": ["getNodeExternalModuleId", "setupShimFiles", "setupNodeExternals", "isNodeExternal", "NODE_STDLIB_MODULES", "builtinModules", "process", "binding", "Object", "keys", "filter", "x", "test", "includes", "sort", "EXTERNAL_REQUIRE_POLYFILL", "EXTERNAL_REQUIRE_NATIVE_POLYFILL", "METRO_EXTERNALS_FOLDER", "METRO_SHIMS_FOLDER", "fromModule", "moduleId", "path", "relative", "dirname", "join", "projectRoot", "fs", "promises", "mkdir", "recursive", "shimsFolder", "require", "resolve", "copyAsync", "overwrite", "tapExternalRequirePolyfill", "tapNodeS<PERSON>s", "writeIfDifferentAsync", "filePath", "contents", "existsSync", "current", "readFile", "writeFile", "moduleName", "replace", "tapNodeShimContents", "externals", "shim<PERSON>ir", "s<PERSON><PERSON><PERSON>"], "mappings": "AAMA;;;;QAuBgBA,uBAAuB,GAAvBA,uBAAuB;QAOjBC,cAAc,GAAdA,cAAc;QAWdC,kBAAkB,GAAlBA,kBAAkB;QA4BxBC,cAAc,GAAdA,cAAc;;AArEf,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACY,IAAA,OAAQ,WAAR,QAAQ,CAAA;AACtB,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEG,IAAA,IAAoB,WAApB,oBAAoB,CAAA;;;;;;AAIvC,MAAMC,mBAAmB,GAAa;IAC3C,aAAa;OACV,CACDC,OAAc,eAAA,IACd,mBAAmB;IACnB,CAACC,OAAO,CAACC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACH,OAAO,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,CAAC,IAChE,EAAE,CACH,CAACG,MAAM,CAAC,CAACC,CAAC,GAAK,CAAC,sCAAsCC,IAAI,CAACD,CAAC,CAAC,IAAI,CAAC;YAAC,KAAK;SAAC,CAACE,QAAQ,CAACF,CAAC,CAAC;IAAA,CAAC;CACxF,CAACG,IAAI,EAAE,AAAC;QARIV,mBAAmB,GAAnBA,mBAAmB;AAUzB,MAAMW,yBAAyB,GAAG,yBAAyB,AAAC;QAAtDA,yBAAyB,GAAzBA,yBAAyB;AAC/B,MAAMC,gCAAgC,GAAG,gCAAgC,AAAC;QAApEA,gCAAgC,GAAhCA,gCAAgC;AACtC,MAAMC,sBAAsB,GAAG,uBAAuB,AAAC;QAAjDA,sBAAsB,GAAtBA,sBAAsB;AAC5B,MAAMC,kBAAkB,GAAG,mBAAmB,AAAC;QAAzCA,kBAAkB,GAAlBA,kBAAkB;AAExB,SAASlB,uBAAuB,CAACmB,UAAkB,EAAEC,QAAgB,EAAE;IAC5E,OAAOC,KAAI,QAAA,CAACC,QAAQ,CAClBD,KAAI,QAAA,CAACE,OAAO,CAACJ,UAAU,CAAC,EACxBE,KAAI,QAAA,CAACG,IAAI,CAACP,sBAAsB,EAAEG,QAAQ,EAAE,UAAU,CAAC,CACxD,CAAC;CACH;AAEM,eAAenB,cAAc,CAACwB,WAAmB,EAAE;IACxD,MAAMC,GAAE,QAAA,CAACC,QAAQ,CAACC,KAAK,CAACP,KAAI,QAAA,CAACG,IAAI,CAACC,WAAW,EAAEP,kBAAkB,CAAC,EAAE;QAAEW,SAAS,EAAE,IAAI;KAAE,CAAC,CAAC;IACzF,4EAA4E;IAC5E,MAAMC,WAAW,GAAGT,KAAI,QAAA,CAACG,IAAI,CAACO,OAAO,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE,iBAAiB,CAAC,AAAC;IAE5F,MAAMC,CAAAA,GAAAA,IAAS,AAGb,CAAA,UAHa,CAACH,WAAW,EAAET,KAAI,QAAA,CAACG,IAAI,CAACC,WAAW,EAAEP,kBAAkB,CAAC,EAAE;QACvEgB,SAAS,EAAE,KAAK;QAChBL,SAAS,EAAE,IAAI;KAChB,CAAC,CAAC;CACJ;AAEM,eAAe3B,kBAAkB,CAACuB,WAAmB,EAAE;IAC5D,MAAMU,0BAA0B,CAACV,WAAW,CAAC,CAAC;IAC9C,MAAMW,YAAY,CAACX,WAAW,CAAC,CAAC;CACjC;AAED,eAAeU,0BAA0B,CAACV,WAAmB,EAAE;IAC7D,MAAMC,GAAE,QAAA,CAACC,QAAQ,CAACC,KAAK,CAACP,KAAI,QAAA,CAACG,IAAI,CAACC,WAAW,EAAEJ,KAAI,QAAA,CAACE,OAAO,CAACR,yBAAyB,CAAC,CAAC,EAAE;QACvFc,SAAS,EAAE,IAAI;KAChB,CAAC,CAAC;IACH,MAAMQ,qBAAqB,CACzBhB,KAAI,QAAA,CAACG,IAAI,CAACC,WAAW,EAAEV,yBAAyB,CAAC,EACjD,mFAAmF,CACpF,CAAC;IACF,MAAMsB,qBAAqB,CACzBhB,KAAI,QAAA,CAACG,IAAI,CAACC,WAAW,EAAET,gCAAgC,CAAC,EACxD,6JAA6J,CAC9J,CAAC;CACH;AAED,eAAeqB,qBAAqB,CAACC,QAAgB,EAAEC,QAAgB,EAAiB;IACtF,IAAIb,GAAE,QAAA,CAACc,UAAU,CAACF,QAAQ,CAAC,EAAE;QAC3B,MAAMG,OAAO,GAAG,MAAMf,GAAE,QAAA,CAACC,QAAQ,CAACe,QAAQ,CAACJ,QAAQ,EAAE,MAAM,CAAC,AAAC;QAC7D,IAAIG,OAAO,KAAKF,QAAQ,EAAE,OAAO;KAClC;IAED,MAAMb,GAAE,QAAA,CAACC,QAAQ,CAACgB,SAAS,CAACL,QAAQ,EAAEC,QAAQ,CAAC,CAAC;CACjD;AAEM,SAASpC,cAAc,CAACyC,UAAkB,EAAiB;IAChE,MAAMxB,QAAQ,GAAGwB,UAAU,CAACC,OAAO,WAAW,EAAE,CAAC,AAAC;IAClD,IAAIzC,mBAAmB,CAACS,QAAQ,CAACO,QAAQ,CAAC,EAAE;QAC1C,OAAOA,QAAQ,CAAC;KACjB;IACD,OAAO,IAAI,CAAC;CACb;AAED,SAAS0B,mBAAmB,CAAC1B,QAAgB,EAAU;IACrD,OAAO,CAAC,0CAA0C,EAAEA,QAAQ,CAAC,GAAG,CAAC,CAAC;CACnE;AAED,kGAAkG;AAClG,eAAegB,YAAY,CAACX,WAAmB,EAAE;IAC/C,MAAMsB,SAAS,GAA2B,EAAE,AAAC;IAC7C,KAAK,MAAM3B,QAAQ,IAAIhB,mBAAmB,CAAE;QAC1C,MAAM4C,OAAO,GAAG3B,KAAI,QAAA,CAACG,IAAI,CAACC,WAAW,EAAER,sBAAsB,EAAEG,QAAQ,CAAC,AAAC;QACzE,MAAM6B,QAAQ,GAAG5B,KAAI,QAAA,CAACG,IAAI,CAACwB,OAAO,EAAE,UAAU,CAAC,AAAC;QAChDD,SAAS,CAAC3B,QAAQ,CAAC,GAAG6B,QAAQ,CAAC;QAE/B,IAAI,CAACvB,GAAE,QAAA,CAACc,UAAU,CAACS,QAAQ,CAAC,EAAE;YAC5B,MAAMvB,GAAE,QAAA,CAACC,QAAQ,CAACC,KAAK,CAACoB,OAAO,EAAE;gBAAEnB,SAAS,EAAE,IAAI;aAAE,CAAC,CAAC;YACtD,MAAMH,GAAE,QAAA,CAACC,QAAQ,CAACgB,SAAS,CAACM,QAAQ,EAAEH,mBAAmB,CAAC1B,QAAQ,CAAC,CAAC,CAAC;SACtE;KACF;CACF"}