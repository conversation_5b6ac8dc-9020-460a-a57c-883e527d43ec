"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
var _utils = require("./utils");
/** Android's stock emulator and other emulators such as genymotion use a standard localhost alias. */ const EMULATOR_LOCALHOST_ADDRESSES = [
    "********",
    "********"
];
/** Prefix for script URLs that are alphanumeric IDs. */ const FILE_PREFIX = "file://";
class VscodeDebuggerScriptParsedHandler {
    constructor(device){
        this.device = device;
    }
    onDeviceMessage(message, debuggerInfo) {
        if ((0, _utils).getDebuggerType(debuggerInfo.userAgent) !== "vscode" || message.method !== "Debugger.scriptParsed") {
            return false;
        }
        // See: https://github.com/facebook/metro/blob/f43caa371a813b257cb0b42028079645a1e85e0e/packages/metro-inspector-proxy/src/Device.js#L401-L410
        if (message.params.sourceMapURL) {
            for(let i = 0; i < EMULATOR_LOCALHOST_ADDRESSES.length; ++i){
                const address = EMULATOR_LOCALHOST_ADDRESSES[i];
                if (message.params.sourceMapURL.indexOf(address) >= 0) {
                    message.params.sourceMapURL = message.params.sourceMapURL.replace(address, "localhost");
                    debuggerInfo.originalSourceURLAddress = address;
                }
            }
        }
        // See: https://github.com/facebook/metro/blob/f43caa371a813b257cb0b42028079645a1e85e0e/packages/metro-inspector-proxy/src/Device.js#L431-L453
        if (message.params.url) {
            for(let i = 0; i < EMULATOR_LOCALHOST_ADDRESSES.length; ++i){
                const address = EMULATOR_LOCALHOST_ADDRESSES[i];
                if (message.params.url.indexOf(address) >= 0) {
                    message.params.url = message.params.url.replace(address, "localhost");
                    debuggerInfo.originalSourceURLAddress = address;
                }
            }
            // Chrome doesn't download source maps if URL param is not a valid
            // URL. Some frameworks pass alphanumeric script ID instead of URL which causes
            // Chrome to not download source maps. In this case we want to prepend script ID
            // with 'file://' prefix.
            if (message.params.url.match(/^[0-9a-z]+$/)) {
                message.params.url = FILE_PREFIX + message.params.url;
                debuggerInfo.prependedFilePrefix = true;
            }
            if (message.params.scriptId != null) {
                this.device._scriptIdToSourcePathMapping.set(message.params.scriptId, message.params.url);
            }
        }
        // Block `@react-native/dev-middleware`'s default source map inlining
        return true;
    }
}
exports.VscodeDebuggerScriptParsedHandler = VscodeDebuggerScriptParsedHandler;

//# sourceMappingURL=VscodeDebuggerScriptParsed.js.map