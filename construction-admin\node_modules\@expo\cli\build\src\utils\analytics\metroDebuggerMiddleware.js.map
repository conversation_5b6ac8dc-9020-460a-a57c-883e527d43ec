{"version": 3, "sources": ["../../../../src/utils/analytics/metroDebuggerMiddleware.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport { Middleware } from 'metro-config';\n\nimport { DebugTool, getMetroDebugProperties } from './getMetroDebugProperties';\nimport { logEventAsync } from './rudderstackClient';\nimport { env } from '../env';\n\ntype Request = Parameters<Middleware>[0];\ntype Response = Parameters<Middleware>[1];\ntype Next = Parameters<Middleware>[2];\n\n/**\n * Create a Metro middleware that reports when a debugger request was found.\n * This will only be reported once, if the app uses Hermes and telemetry is not enabled.\n */\nexport function createDebuggerTelemetryMiddleware(\n  projectRoot: string,\n  exp: ExpoConfig\n): Middleware {\n  let hasReported = false;\n\n  // This only works for Hermes apps, disable when telemetry is turned off\n  if (env.EXPO_NO_TELEMETRY || exp.jsEngine !== 'hermes') {\n    return (req: Request, res: Response, next: Next) => {\n      if (typeof next === 'function') {\n        next(undefined);\n      }\n    };\n  }\n\n  return (req: Request, res: Response, next: Next) => {\n    // Only report once\n    if (hasReported && typeof next === 'function') {\n      return next(undefined);\n    }\n\n    const debugTool = findDebugTool(req);\n    if (debugTool) {\n      hasReported = true;\n      logEventAsync('metro debug', getMetroDebugProperties(projectRoot, exp, debugTool));\n    }\n\n    if (typeof next === 'function') {\n      return next(undefined);\n    }\n  };\n}\n\n/** Exposed for testing */\nexport function findDebugTool(\n  req: Pick<Parameters<Middleware>[0], 'headers' | 'url'>\n): DebugTool | null {\n  if (req.headers['origin']?.includes('chrome-devtools')) {\n    return { name: 'chrome' };\n  }\n\n  if (req.url?.startsWith('/json')) {\n    const flipperUserAgent = req.headers['user-agent']?.match(/(Flipper)\\/([^\\s]+)/);\n    if (flipperUserAgent) {\n      return {\n        name: flipperUserAgent[1].toLowerCase(),\n        version: flipperUserAgent[2],\n      };\n    }\n  }\n\n  return null;\n}\n"], "names": ["createDebuggerTelemetryMiddleware", "findDebugTool", "projectRoot", "exp", "hasReported", "env", "EXPO_NO_TELEMETRY", "jsEngine", "req", "res", "next", "undefined", "debugTool", "logEventAsync", "getMetroDebugProperties", "headers", "includes", "name", "url", "startsWith", "flipperUserAgent", "match", "toLowerCase", "version"], "mappings": "AAAA;;;;QAegBA,iCAAiC,GAAjCA,iCAAiC;QAkCjCC,aAAa,GAAbA,aAAa;AA9CsB,IAAA,wBAA2B,WAA3B,2BAA2B,CAAA;AAChD,IAAA,kBAAqB,WAArB,qBAAqB,CAAA;AAC/B,IAAA,IAAQ,WAAR,QAAQ,CAAA;AAUrB,SAASD,iCAAiC,CAC/CE,WAAmB,EACnBC,GAAe,EACH;IACZ,IAAIC,WAAW,GAAG,KAAK,AAAC;IAExB,wEAAwE;IACxE,IAAIC,IAAG,IAAA,CAACC,iBAAiB,IAAIH,GAAG,CAACI,QAAQ,KAAK,QAAQ,EAAE;QACtD,OAAO,CAACC,GAAY,EAAEC,GAAa,EAAEC,IAAU,GAAK;YAClD,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;gBAC9BA,IAAI,CAACC,SAAS,CAAC,CAAC;aACjB;SACF,CAAC;KACH;IAED,OAAO,CAACH,GAAY,EAAEC,GAAa,EAAEC,IAAU,GAAK;QAClD,mBAAmB;QACnB,IAAIN,WAAW,IAAI,OAAOM,IAAI,KAAK,UAAU,EAAE;YAC7C,OAAOA,IAAI,CAACC,SAAS,CAAC,CAAC;SACxB;QAED,MAAMC,SAAS,GAAGX,aAAa,CAACO,GAAG,CAAC,AAAC;QACrC,IAAII,SAAS,EAAE;YACbR,WAAW,GAAG,IAAI,CAAC;YACnBS,CAAAA,GAAAA,kBAAa,AAAqE,CAAA,cAArE,CAAC,aAAa,EAAEC,CAAAA,GAAAA,wBAAuB,AAA6B,CAAA,wBAA7B,CAACZ,WAAW,EAAEC,GAAG,EAAES,SAAS,CAAC,CAAC,CAAC;SACpF;QAED,IAAI,OAAOF,IAAI,KAAK,UAAU,EAAE;YAC9B,OAAOA,IAAI,CAACC,SAAS,CAAC,CAAC;SACxB;KACF,CAAC;CACH;AAGM,SAASV,aAAa,CAC3BO,GAAuD,EACrC;QACdA,GAAqB,EAIrBA,IAAO;IAJX,IAAIA,CAAAA,GAAqB,GAArBA,GAAG,CAACO,OAAO,CAAC,QAAQ,CAAC,SAAU,GAA/BP,KAAAA,CAA+B,GAA/BA,GAAqB,CAAEQ,QAAQ,CAAC,iBAAiB,CAAC,EAAE;QACtD,OAAO;YAAEC,IAAI,EAAE,QAAQ;SAAE,CAAC;KAC3B;IAED,IAAIT,CAAAA,IAAO,GAAPA,GAAG,CAACU,GAAG,SAAY,GAAnBV,KAAAA,CAAmB,GAAnBA,IAAO,CAAEW,UAAU,CAAC,OAAO,CAAC,EAAE;YACPX,IAAyB;QAAlD,MAAMY,gBAAgB,GAAGZ,CAAAA,IAAyB,GAAzBA,GAAG,CAACO,OAAO,CAAC,YAAY,CAAC,SAAO,GAAhCP,KAAAA,CAAgC,GAAhCA,IAAyB,CAAEa,KAAK,uBAAuB,AAAC;QACjF,IAAID,gBAAgB,EAAE;YACpB,OAAO;gBACLH,IAAI,EAAEG,gBAAgB,CAAC,CAAC,CAAC,CAACE,WAAW,EAAE;gBACvCC,OAAO,EAAEH,gBAAgB,CAAC,CAAC,CAAC;aAC7B,CAAC;SACH;KACF;IAED,OAAO,IAAI,CAAC;CACb"}