"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getNodejsExtensions = getNodejsExtensions;
exports.withExtendedResolver = withExtendedResolver;
exports.shouldAliasAssetRegistryForWeb = shouldAliasAssetRegistryForWeb;
exports.shouldAliasModule = shouldAliasModule;
exports.withMetroMultiPlatformAsync = withMetroMultiPlatformAsync;
var _fs = _interopRequireDefault(require("fs"));
var metroResolver = _interopRequireWildcard(require("metro-resolver"));
var _path = _interopRequireDefault(require("path"));
var _resolveFrom = _interopRequireDefault(require("resolve-from"));
var _createExpoMetroResolver = require("./createExpoMetroResolver");
var _externals = require("./externals");
var _metroErrors = require("./metroErrors");
var _withMetroResolvers = require("./withMetroResolvers");
var _log = require("../../../log");
var _fileNotifier = require("../../../utils/FileNotifier");
var _env = require("../../../utils/env");
var _exit = require("../../../utils/exit");
var _interactive = require("../../../utils/interactive");
var _loadTsConfigPaths = require("../../../utils/tsconfig/loadTsConfigPaths");
var _resolveWithTsConfigPaths = require("../../../utils/tsconfig/resolveWithTsConfigPaths");
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _interopRequireWildcard(obj) {
    if (obj && obj.__esModule) {
        return obj;
    } else {
        var newObj = {};
        if (obj != null) {
            for(var key in obj){
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {};
                    if (desc.get || desc.set) {
                        Object.defineProperty(newObj, key, desc);
                    } else {
                        newObj[key] = obj[key];
                    }
                }
            }
        }
        newObj.default = obj;
        return newObj;
    }
}
const debug = require("debug")("expo:start:server:metro:multi-platform");
function withWebPolyfills(config) {
    const originalGetPolyfills = config.serializer.getPolyfills ? config.serializer.getPolyfills.bind(config.serializer) : ()=>[]
    ;
    const getPolyfills = (ctx)=>{
        if (ctx.platform === "web") {
            return [
                // NOTE: We might need this for all platforms
                _path.default.join(config.projectRoot, _externals.EXTERNAL_REQUIRE_POLYFILL)
            ];
        }
        // Generally uses `rn-get-polyfills`
        const polyfills = originalGetPolyfills(ctx);
        return [
            ...polyfills,
            _path.default.join(config.projectRoot, _externals.EXTERNAL_REQUIRE_NATIVE_POLYFILL)
        ];
    };
    return {
        ...config,
        serializer: {
            ...config.serializer,
            getPolyfills
        }
    };
}
function normalizeSlashes(p) {
    return p.replace(/\\/g, "/");
}
function getNodejsExtensions(srcExts) {
    const mjsExts = srcExts.filter((ext)=>/mjs$/.test(ext)
    );
    const nodejsSourceExtensions = srcExts.filter((ext)=>!/mjs$/.test(ext)
    );
    // find index of last `*.js` extension
    const jsIndex = nodejsSourceExtensions.reduce((index, ext, i)=>{
        return /jsx?$/.test(ext) ? i : index;
    }, -1);
    // insert `*.mjs` extensions after `*.js` extensions
    nodejsSourceExtensions.splice(jsIndex + 1, 0, ...mjsExts);
    return nodejsSourceExtensions;
}
function withExtendedResolver(config, { tsconfig , isTsconfigPathsEnabled , isFastResolverEnabled , isExporting  }) {
    var ref5, ref1, ref2, ref3;
    if (isFastResolverEnabled) {
        _log.Log.warn(`Experimental bundling features are enabled.`);
    }
    // Get the `transformer.assetRegistryPath`
    // this needs to be unified since you can't dynamically
    // swap out the transformer based on platform.
    const assetRegistryPath = _fs.default.realpathSync(_path.default.resolve((0, _resolveFrom).default(config.projectRoot, "@react-native/assets-registry/registry.js")));
    const defaultResolver = metroResolver.resolve;
    var ref4;
    const resolver = isFastResolverEnabled ? (0, _createExpoMetroResolver).createFastResolver({
        preserveSymlinks: (ref4 = (ref5 = config.resolver) == null ? void 0 : ref5.unstable_enableSymlinks) != null ? ref4 : true,
        blockList: Array.isArray((ref1 = config.resolver) == null ? void 0 : ref1.blockList) ? (ref2 = config.resolver) == null ? void 0 : ref2.blockList : [
            (ref3 = config.resolver) == null ? void 0 : ref3.blockList
        ]
    }) : defaultResolver;
    const aliases = {
        web: {
            "react-native": "react-native-web",
            "react-native/index": "react-native-web"
        }
    };
    const universalAliases = [];
    // This package is currently always installed as it is included in the `expo` package.
    if (_resolveFrom.default.silent(config.projectRoot, "@expo/vector-icons")) {
        debug("Enabling alias: react-native-vector-icons -> @expo/vector-icons");
        universalAliases.push([
            /^react-native-vector-icons(\/.*)?/,
            "@expo/vector-icons$1"
        ]);
    }
    const preferredMainFields = {
        // Defaults from Expo Webpack. Most packages using `react-native` don't support web
        // in the `react-native` field, so we should prefer the `browser` field.
        // https://github.com/expo/router/issues/37
        web: [
            "browser",
            "module",
            "main"
        ]
    };
    var _paths1, _baseUrl1;
    let tsConfigResolve = isTsconfigPathsEnabled && ((tsconfig == null ? void 0 : tsconfig.paths) || (tsconfig == null ? void 0 : tsconfig.baseUrl) != null) ? _resolveWithTsConfigPaths.resolveWithTsConfigPaths.bind(_resolveWithTsConfigPaths.resolveWithTsConfigPaths, {
        paths: (_paths1 = tsconfig.paths) != null ? _paths1 : {},
        baseUrl: (_baseUrl1 = tsconfig.baseUrl) != null ? _baseUrl1 : config.projectRoot,
        hasBaseUrl: !!tsconfig.baseUrl
    }) : null;
    // TODO: Move this to be a transform key for invalidation.
    if (!isExporting && (0, _interactive).isInteractive()) {
        if (isTsconfigPathsEnabled) {
            // TODO: We should track all the files that used imports and invalidate them
            // currently the user will need to save all the files that use imports to
            // use the new aliases.
            const configWatcher = new _fileNotifier.FileNotifier(config.projectRoot, [
                "./tsconfig.json",
                "./jsconfig.json", 
            ]);
            configWatcher.startObserving(()=>{
                debug("Reloading tsconfig.json");
                (0, _loadTsConfigPaths).loadTsConfigPathsAsync(config.projectRoot).then((tsConfigPaths)=>{
                    if ((tsConfigPaths == null ? void 0 : tsConfigPaths.paths) && !!Object.keys(tsConfigPaths.paths).length) {
                        debug("Enabling tsconfig.json paths support");
                        var _paths, _baseUrl;
                        tsConfigResolve = _resolveWithTsConfigPaths.resolveWithTsConfigPaths.bind(_resolveWithTsConfigPaths.resolveWithTsConfigPaths, {
                            paths: (_paths = tsConfigPaths.paths) != null ? _paths : {},
                            baseUrl: (_baseUrl = tsConfigPaths.baseUrl) != null ? _baseUrl : config.projectRoot,
                            hasBaseUrl: !!tsConfigPaths.baseUrl
                        });
                    } else {
                        debug("Disabling tsconfig.json paths support");
                        tsConfigResolve = null;
                    }
                });
            });
            // TODO: This probably prevents the process from exiting.
            (0, _exit).installExitHooks(()=>{
                configWatcher.stopObserving();
            });
        } else {
            debug("Skipping tsconfig.json paths support");
        }
    }
    let nodejsSourceExtensions = null;
    const shimsFolder = _path.default.join(config.projectRoot, _externals.METRO_SHIMS_FOLDER);
    function getStrictResolver({ resolveRequest , ...context }, platform) {
        return function doResolve(moduleName) {
            return resolver(context, moduleName, platform);
        };
    }
    function getOptionalResolver(context, platform) {
        const doResolve = getStrictResolver(context, platform);
        return function optionalResolve(moduleName) {
            try {
                return doResolve(moduleName);
            } catch (error) {
                // If the error is directly related to a resolver not being able to resolve a module, then
                // we can ignore the error and try the next resolver. Otherwise, we should throw the error.
                const isResolutionError = (0, _metroErrors).isFailedToResolveNameError(error) || (0, _metroErrors).isFailedToResolvePathError(error);
                if (!isResolutionError) {
                    throw error;
                }
            }
            return null;
        };
    }
    const metroConfigWithCustomResolver = (0, _withMetroResolvers).withMetroResolvers(config, [
        // tsconfig paths
        (context, moduleName, platform)=>{
            var ref;
            return (ref = tsConfigResolve == null ? void 0 : tsConfigResolve({
                originModulePath: context.originModulePath,
                moduleName
            }, getOptionalResolver(context, platform))) != null ? ref : null;
        },
        // Node.js externals support
        (context, moduleName, platform)=>{
            var // In browser runtimes, we want to either resolve a local node module by the same name, or shim the module to
            // prevent crashing when Node.js built-ins are imported.
            ref;
            // This is a web-only feature, we may extend the shimming to native platforms in the future.
            if (platform !== "web") {
                return null;
            }
            const moduleId = (0, _externals).isNodeExternal(moduleName);
            if (!moduleId) {
                return null;
            }
            if (((ref = context.customResolverOptions) == null ? void 0 : ref.environment) !== "node") {
                // Perform optional resolve first. If the module doesn't exist (no module in the node_modules)
                // then we can mock the file to use an empty module.
                const result = getOptionalResolver(context, platform)(moduleName);
                return result != null ? result : {
                    // In this case, mock the file to use an empty module.
                    type: "empty"
                };
            }
            const redirectedModuleName = (0, _externals).getNodeExternalModuleId(context.originModulePath, moduleId);
            debug(`Redirecting Node.js external "${moduleId}" to "${redirectedModuleName}"`);
            return getStrictResolver(context, platform)(redirectedModuleName);
        },
        // Basic moduleId aliases
        (context, moduleName, platform)=>{
            // Conditionally remap `react-native` to `react-native-web` on web in
            // a way that doesn't require Babel to resolve the alias.
            if (platform && platform in aliases && aliases[platform][moduleName]) {
                const redirectedModuleName = aliases[platform][moduleName];
                return getStrictResolver(context, platform)(redirectedModuleName);
            }
            for (const [matcher, alias] of universalAliases){
                const match = moduleName.match(matcher);
                if (match) {
                    var ref;
                    const aliasedModule = alias.replace(/\$(\d+)/g, (_, index)=>(ref = match[parseInt(index, 10)]) != null ? ref : ""
                    );
                    const doResolve = getStrictResolver(context, platform);
                    debug(`Alias "${moduleName}" to "${aliasedModule}"`);
                    return doResolve(aliasedModule);
                }
            }
            return null;
        },
        // HACK(EvanBacon):
        // React Native uses `event-target-shim` incorrectly and this causes the native runtime
        // to fail to load. This is a temporary workaround until we can fix this upstream.
        // https://github.com/facebook/react-native/pull/38628
        (context, moduleName, platform)=>{
            if (platform !== "web" && moduleName === "event-target-shim") {
                debug("For event-target-shim to use js:", context.originModulePath);
                const doResolve = getStrictResolver(context, platform);
                return doResolve("event-target-shim/dist/event-target-shim.js");
            }
            return null;
        },
        // TODO: Reduce these as much as possible in the future.
        // Complex post-resolution rewrites.
        (context, moduleName, platform)=>{
            const doResolve = getStrictResolver(context, platform);
            const result = doResolve(moduleName);
            if (result.type !== "sourceFile") {
                return result;
            }
            // Replace the web resolver with the original one.
            // This is basically an alias for web-only.
            // TODO: Drop this in favor of the standalone asset registry module.
            if (shouldAliasAssetRegistryForWeb(platform, result)) {
                // @ts-expect-error: `readonly` for some reason.
                result.filePath = assetRegistryPath;
            }
            if (platform === "web" && result.filePath.includes("node_modules")) {
                // Replace with static shims
                const normalName = normalizeSlashes(result.filePath)// Drop everything up until the `node_modules` folder.
                .replace(/.*node_modules\//, "");
                const shimPath = _path.default.join(shimsFolder, normalName);
                if (_fs.default.existsSync(shimPath)) {
                    // @ts-expect-error: `readonly` for some reason.
                    result.filePath = shimPath;
                }
            }
            return result;
        }, 
    ]);
    // Ensure we mutate the resolution context to include the custom resolver options for server and web.
    const metroConfigWithCustomContext = (0, _withMetroResolvers).withMetroMutatedResolverContext(metroConfigWithCustomResolver, (immutableContext, moduleName, platform)=>{
        var ref;
        const context = {
            ...immutableContext,
            preferNativePlatform: platform !== "web"
        };
        if (((ref = context.customResolverOptions) == null ? void 0 : ref.environment) === "node") {
            // Adjust nodejs source extensions to sort mjs after js, including platform variants.
            if (nodejsSourceExtensions === null) {
                nodejsSourceExtensions = getNodejsExtensions(context.sourceExts);
            }
            context.sourceExts = nodejsSourceExtensions;
            context.unstable_enablePackageExports = true;
            context.unstable_conditionNames = [
                "node",
                "require"
            ];
            context.unstable_conditionsByPlatform = {};
            // Node.js runtimes should only be importing main at the moment.
            // This is a temporary fix until we can support the package.json exports.
            context.mainFields = [
                "main",
                "module"
            ];
        } else {
            // Non-server changes
            if (!_env.env.EXPO_METRO_NO_MAIN_FIELD_OVERRIDE && platform && platform in preferredMainFields) {
                context.mainFields = preferredMainFields[platform];
            }
        }
        return context;
    });
    return (0, _withMetroResolvers).withMetroErrorReportingResolver(metroConfigWithCustomContext);
}
function shouldAliasAssetRegistryForWeb(platform, result) {
    return platform === "web" && (result == null ? void 0 : result.type) === "sourceFile" && typeof (result == null ? void 0 : result.filePath) === "string" && normalizeSlashes(result.filePath).endsWith("react-native-web/dist/modules/AssetRegistry/index.js");
}
function shouldAliasModule(input, alias) {
    var ref, ref6;
    return input.platform === alias.platform && ((ref = input.result) == null ? void 0 : ref.type) === "sourceFile" && typeof ((ref6 = input.result) == null ? void 0 : ref6.filePath) === "string" && normalizeSlashes(input.result.filePath).endsWith(alias.output);
}
async function withMetroMultiPlatformAsync(projectRoot, { config , exp , platformBundlers , isTsconfigPathsEnabled , webOutput , isFastResolverEnabled , isExporting  }) {
    if (!config.projectRoot) {
        // @ts-expect-error: read-only types
        config.projectRoot = projectRoot;
    }
    var _EXPO_PUBLIC_PROJECT_ROOT;
    // Required for @expo/metro-runtime to format paths in the web LogBox.
    process.env.EXPO_PUBLIC_PROJECT_ROOT = (_EXPO_PUBLIC_PROJECT_ROOT = process.env.EXPO_PUBLIC_PROJECT_ROOT) != null ? _EXPO_PUBLIC_PROJECT_ROOT : projectRoot;
    if ([
        "static",
        "server"
    ].includes(webOutput != null ? webOutput : "")) {
        // Enable static rendering in runtime space.
        process.env.EXPO_PUBLIC_USE_STATIC = "1";
    }
    // This is used for running Expo CLI in development against projects outside the monorepo.
    if (!isDirectoryIn(__dirname, projectRoot)) {
        if (!config.watchFolders) {
            // @ts-expect-error: watchFolders is readonly
            config.watchFolders = [];
        }
        // @ts-expect-error: watchFolders is readonly
        config.watchFolders.push(_path.default.join(require.resolve("metro-runtime/package.json"), "../.."));
    }
    // @ts-expect-error
    config.transformer._expoRouterWebRendering = webOutput;
    // @ts-expect-error: Invalidate the cache when the location of expo-router changes on-disk.
    config.transformer._expoRouterPath = _resolveFrom.default.silent(projectRoot, "expo-router");
    let tsconfig = null;
    if (isTsconfigPathsEnabled) {
        tsconfig = await (0, _loadTsConfigPaths).loadTsConfigPathsAsync(projectRoot);
    }
    await (0, _externals).setupShimFiles(projectRoot);
    await (0, _externals).setupNodeExternals(projectRoot);
    let expoConfigPlatforms = Object.entries(platformBundlers).filter(([platform, bundler])=>{
        var ref;
        return bundler === "metro" && ((ref = exp.platforms) == null ? void 0 : ref.includes(platform));
    }).map(([platform])=>platform
    );
    if (Array.isArray(config.resolver.platforms)) {
        expoConfigPlatforms = [
            ...new Set(expoConfigPlatforms.concat(config.resolver.platforms))
        ];
    }
    // @ts-expect-error: typed as `readonly`.
    config.resolver.platforms = expoConfigPlatforms;
    config = withWebPolyfills(config);
    return withExtendedResolver(config, {
        tsconfig,
        isExporting,
        isTsconfigPathsEnabled,
        isFastResolverEnabled
    });
}
function isDirectoryIn(targetPath, rootPath) {
    return targetPath.startsWith(rootPath) && targetPath.length >= rootPath.length;
}

//# sourceMappingURL=withMetroMultiPlatform.js.map